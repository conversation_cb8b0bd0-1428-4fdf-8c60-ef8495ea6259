"use client"

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useLanguage } from '@/components/providers/language-provider'
import { useToast } from '@/hooks/use-toast'
import {
  MessageCircle,
  Package,
  DollarSign,
  Clock,
  Truck,
  Shield,
  Star,
  Send,
  FileText,
  Calculator,
  Globe,
  Users,
  CheckCircle,
  AlertCircle,
} from 'lucide-react'

interface Product {
  id: string
  name: string
  nameAr?: string
  slug: string
  description: string
  price: number
  wholesalePrice?: number
  minimumOrder: number
  stockQuantity: number
  category: {
    name: string
    nameAr?: string
  }
  supplier: {
    id: string
    name: string
    nameAr?: string
    contactPerson: string
    rating: number
    verified: boolean
    location: string
    responseTime: string
  }
  images: string[]
  specifications?: Record<string, string>
  leadTime?: number
  weight?: number
  dimensions?: {
    length: number
    width: number
    height: number
  }
}

interface ProductInquiryFormProps {
  product: Product
  onClose?: () => void
}

const urgencyOptions = [
  { id: 'low', name: 'Low Priority', nameAr: 'أولوية منخفضة', description: 'No rush, flexible timeline', descriptionAr: 'لا عجلة، جدول زمني مرن' },
  { id: 'normal', name: 'Normal Priority', nameAr: 'أولوية عادية', description: 'Standard processing time', descriptionAr: 'وقت معالجة قياسي' },
  { id: 'high', name: 'High Priority', nameAr: 'أولوية عالية', description: 'Need quote within 24 hours', descriptionAr: 'أحتاج عرض سعر خلال 24 ساعة' },
  { id: 'urgent', name: 'Urgent', nameAr: 'عاجل', description: 'Need immediate response', descriptionAr: 'أحتاج رد فوري' },
]

const inquiryTypes = [
  { id: 'quote', name: 'Price Quote', nameAr: 'عرض سعر', description: 'Get pricing for specific quantities' },
  { id: 'sample', name: 'Sample Request', nameAr: 'طلب عينة', description: 'Request product samples' },
  { id: 'bulk', name: 'Bulk Order', nameAr: 'طلب جملة', description: 'Large quantity purchase' },
  { id: 'custom', name: 'Customization', nameAr: 'تخصيص', description: 'Custom specifications or branding' },
  { id: 'shipping', name: 'Shipping Info', nameAr: 'معلومات الشحن', description: 'Shipping costs and options' },
  { id: 'certification', name: 'Certification', nameAr: 'شهادات', description: 'Product certifications and compliance' },
]

export function ProductInquiryForm({ product, onClose }: ProductInquiryFormProps) {
  const { data: session } = useSession()
  const router = useRouter()
  const { language, t } = useLanguage()
  const { toast } = useToast()
  const isArabic = language === 'ar'

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    inquiryType: 'quote',
    urgency: 'normal',
    quantity: product.minimumOrder.toString(),
    targetPrice: '',
    specifications: '',
    customRequirements: '',
    shippingDestination: '',
    expectedDelivery: '',
    businessType: '',
    companySize: '',
    additionalNotes: '',
    contactPreference: 'email',
    customerName: session?.user?.name || '',
    customerEmail: session?.user?.email || '',
    customerPhone: '',
    customerCompany: '',
  })

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const calculateEstimatedTotal = () => {
    const quantity = parseInt(formData.quantity) || 0
    const unitPrice = product.wholesalePrice || product.price
    return quantity * unitPrice
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!session) {
      toast({
        title: isArabic ? "مطلوب تسجيل الدخول" : "Authentication Required",
        description: isArabic ? "يرجى تسجيل الدخول لإرسال استفسار" : "Please log in to submit an inquiry",
        variant: "destructive",
      })
      return
    }

    if (!formData.quantity || parseInt(formData.quantity) < product.minimumOrder) {
      toast({
        title: isArabic ? "كمية غير صحيحة" : "Invalid Quantity",
        description: isArabic 
          ? `الحد الأدنى للطلب هو ${product.minimumOrder} قطعة`
          : `Minimum order quantity is ${product.minimumOrder} pieces`,
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch('/api/inquiries/product', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: product.id,
          supplierId: product.supplier.id,
          customerId: session.user.id,
          ...formData,
          estimatedTotal: calculateEstimatedTotal(),
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to submit inquiry')
      }

      const result = await response.json()

      toast({
        title: isArabic ? "تم إرسال الاستفسار" : "Inquiry Submitted",
        description: isArabic 
          ? `تم إرسال استفسارك #${result.inquiryNumber} بنجاح. سيتواصل معك المورد قريباً.`
          : `Your inquiry #${result.inquiryNumber} has been submitted successfully. The supplier will contact you soon.`,
      })

      // Redirect to inquiries page
      router.push('/account/inquiries')

    } catch (error) {
      toast({
        title: isArabic ? "خطأ في الإرسال" : "Submission Failed",
        description: isArabic 
          ? "حدث خطأ أثناء إرسال الاستفسار. يرجى المحاولة مرة أخرى."
          : "There was an error submitting your inquiry. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!session) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">
            {isArabic ? "مطلوب تسجيل الدخول" : "Login Required"}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {isArabic 
              ? "يرجى تسجيل الدخول لإرسال استفسار عن المنتج"
              : "Please log in to submit a product inquiry"
            }
          </p>
          <Button onClick={() => router.push('/auth/signin')}>
            {isArabic ? "تسجيل الدخول" : "Sign In"}
          </Button>
        </CardContent>
      </Card>
    )
  }

  const selectedInquiryType = inquiryTypes.find(type => type.id === formData.inquiryType)
  const selectedUrgency = urgencyOptions.find(urgency => urgency.id === formData.urgency)

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Product Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Package className="h-5 w-5 mr-2" />
            {isArabic ? "استفسار عن المنتج" : "Product Inquiry"}
          </CardTitle>
          <CardDescription>
            {isArabic 
              ? "احصل على عرض سعر مخصص ومعلومات مفصلة عن المنتج"
              : "Get a customized quote and detailed product information"
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-start space-x-4">
            <img
              src={product.images[0] || '/placeholder-product.jpg'}
              alt={isArabic && product.nameAr ? product.nameAr : product.name}
              className="w-20 h-20 object-cover rounded-lg"
            />
            <div className="flex-1">
              <h3 className="font-semibold text-lg">
                {isArabic && product.nameAr ? product.nameAr : product.name}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm mb-2">
                {isArabic && product.category.nameAr ? product.category.nameAr : product.category.name}
              </p>
              <div className="flex items-center space-x-4 text-sm">
                <span>
                  {isArabic ? "السعر:" : "Price:"} ${product.wholesalePrice || product.price}
                </span>
                <span>
                  {isArabic ? "الحد الأدنى:" : "MOQ:"} {product.minimumOrder} {isArabic ? "قطعة" : "pcs"}
                </span>
                <span>
                  {isArabic ? "متوفر:" : "Stock:"} {product.stockQuantity} {isArabic ? "قطعة" : "pcs"}
                </span>
              </div>
            </div>
            <div className="text-right">
              <div className="flex items-center space-x-2 mb-1">
                <Star className="h-4 w-4 text-yellow-400 fill-current" />
                <span className="text-sm">{product.supplier.rating}</span>
                {product.supplier.verified && (
                  <Shield className="h-4 w-4 text-green-500" />
                )}
              </div>
              <p className="text-sm text-gray-600">
                {isArabic && product.supplier.nameAr ? product.supplier.nameAr : product.supplier.name}
              </p>
              <p className="text-xs text-gray-500">{product.supplier.location}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Inquiry Form */}
      <Card>
        <CardHeader>
          <CardTitle>
            {isArabic ? "تفاصيل الاستفسار" : "Inquiry Details"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Inquiry Type & Urgency */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="inquiryType">
                  {isArabic ? "نوع الاستفسار" : "Inquiry Type"} *
                </Label>
                <Select value={formData.inquiryType} onValueChange={(value) => handleInputChange('inquiryType', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {inquiryTypes.map((type) => (
                      <SelectItem key={type.id} value={type.id}>
                        {isArabic ? type.nameAr : type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {selectedInquiryType && (
                  <p className="text-xs text-gray-500 mt-1">
                    {selectedInquiryType.description}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="urgency">
                  {isArabic ? "الأولوية" : "Priority"} *
                </Label>
                <Select value={formData.urgency} onValueChange={(value) => handleInputChange('urgency', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {urgencyOptions.map((urgency) => (
                      <SelectItem key={urgency.id} value={urgency.id}>
                        {isArabic ? urgency.nameAr : urgency.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {selectedUrgency && (
                  <p className="text-xs text-gray-500 mt-1">
                    {isArabic ? selectedUrgency.descriptionAr : selectedUrgency.description}
                  </p>
                )}
              </div>
            </div>

            {/* Quantity & Target Price */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="quantity">
                  {isArabic ? "الكمية المطلوبة" : "Required Quantity"} *
                </Label>
                <Input
                  id="quantity"
                  type="number"
                  min={product.minimumOrder}
                  max={product.stockQuantity}
                  value={formData.quantity}
                  onChange={(e) => handleInputChange('quantity', e.target.value)}
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  {isArabic ? "الحد الأدنى:" : "Minimum:"} {product.minimumOrder} {isArabic ? "قطعة" : "pieces"}
                </p>
              </div>

              <div>
                <Label htmlFor="targetPrice">
                  {isArabic ? "السعر المستهدف (للقطعة)" : "Target Price (per piece)"}
                </Label>
                <Input
                  id="targetPrice"
                  type="number"
                  step="0.01"
                  value={formData.targetPrice}
                  onChange={(e) => handleInputChange('targetPrice', e.target.value)}
                  placeholder={isArabic ? "مثال: 5.50" : "e.g., 5.50"}
                />
              </div>
            </div>

            {/* Estimated Total */}
            {formData.quantity && (
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="font-medium">
                    {isArabic ? "التكلفة المقدرة:" : "Estimated Total:"}
                  </span>
                  <span className="text-xl font-bold text-blue-600">
                    ${calculateEstimatedTotal().toLocaleString()}
                  </span>
                </div>
                <p className="text-xs text-gray-600 mt-1">
                  {formData.quantity} × ${product.wholesalePrice || product.price} = ${calculateEstimatedTotal().toLocaleString()}
                </p>
              </div>
            )}

            {/* Specifications & Requirements */}
            <div>
              <Label htmlFor="specifications">
                {isArabic ? "المواصفات المطلوبة" : "Required Specifications"}
              </Label>
              <Textarea
                id="specifications"
                value={formData.specifications}
                onChange={(e) => handleInputChange('specifications', e.target.value)}
                placeholder={isArabic 
                  ? "أي مواصفات خاصة، ألوان، أحجام، مواد، إلخ..."
                  : "Any specific specifications, colors, sizes, materials, etc..."
                }
                rows={3}
              />
            </div>

            {/* Custom Requirements */}
            <div>
              <Label htmlFor="customRequirements">
                {isArabic ? "متطلبات التخصيص" : "Customization Requirements"}
              </Label>
              <Textarea
                id="customRequirements"
                value={formData.customRequirements}
                onChange={(e) => handleInputChange('customRequirements', e.target.value)}
                placeholder={isArabic 
                  ? "طباعة الشعار، التعبئة المخصصة، تغيير التصميم، إلخ..."
                  : "Logo printing, custom packaging, design changes, etc..."
                }
                rows={3}
              />
            </div>

            {/* Shipping & Delivery */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="shippingDestination">
                  {isArabic ? "وجهة الشحن" : "Shipping Destination"}
                </Label>
                <Input
                  id="shippingDestination"
                  value={formData.shippingDestination}
                  onChange={(e) => handleInputChange('shippingDestination', e.target.value)}
                  placeholder={isArabic ? "المدينة، الدولة" : "City, Country"}
                />
              </div>

              <div>
                <Label htmlFor="expectedDelivery">
                  {isArabic ? "التاريخ المطلوب للتسليم" : "Expected Delivery Date"}
                </Label>
                <Input
                  id="expectedDelivery"
                  type="date"
                  value={formData.expectedDelivery}
                  onChange={(e) => handleInputChange('expectedDelivery', e.target.value)}
                />
              </div>
            </div>

            {/* Business Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="businessType">
                  {isArabic ? "نوع النشاط التجاري" : "Business Type"}
                </Label>
                <Select value={formData.businessType} onValueChange={(value) => handleInputChange('businessType', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder={isArabic ? "اختر نوع النشاط" : "Select business type"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="importer">{isArabic ? "مستورد" : "Importer"}</SelectItem>
                    <SelectItem value="distributor">{isArabic ? "موزع" : "Distributor"}</SelectItem>
                    <SelectItem value="retailer">{isArabic ? "تاجر تجزئة" : "Retailer"}</SelectItem>
                    <SelectItem value="manufacturer">{isArabic ? "مصنع" : "Manufacturer"}</SelectItem>
                    <SelectItem value="other">{isArabic ? "أخرى" : "Other"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="companySize">
                  {isArabic ? "حجم الشركة" : "Company Size"}
                </Label>
                <Select value={formData.companySize} onValueChange={(value) => handleInputChange('companySize', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder={isArabic ? "اختر حجم الشركة" : "Select company size"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="startup">{isArabic ? "شركة ناشئة" : "Startup"}</SelectItem>
                    <SelectItem value="small">{isArabic ? "صغيرة (1-50 موظف)" : "Small (1-50 employees)"}</SelectItem>
                    <SelectItem value="medium">{isArabic ? "متوسطة (51-200 موظف)" : "Medium (51-200 employees)"}</SelectItem>
                    <SelectItem value="large">{isArabic ? "كبيرة (200+ موظف)" : "Large (200+ employees)"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Contact Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="customerName">
                  {isArabic ? "الاسم الكامل" : "Full Name"} *
                </Label>
                <Input
                  id="customerName"
                  value={formData.customerName}
                  onChange={(e) => handleInputChange('customerName', e.target.value)}
                  required
                />
              </div>

              <div>
                <Label htmlFor="customerCompany">
                  {isArabic ? "اسم الشركة" : "Company Name"}
                </Label>
                <Input
                  id="customerCompany"
                  value={formData.customerCompany}
                  onChange={(e) => handleInputChange('customerCompany', e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="customerEmail">
                  {isArabic ? "البريد الإلكتروني" : "Email Address"} *
                </Label>
                <Input
                  id="customerEmail"
                  type="email"
                  value={formData.customerEmail}
                  onChange={(e) => handleInputChange('customerEmail', e.target.value)}
                  required
                />
              </div>

              <div>
                <Label htmlFor="customerPhone">
                  {isArabic ? "رقم الهاتف" : "Phone Number"}
                </Label>
                <Input
                  id="customerPhone"
                  value={formData.customerPhone}
                  onChange={(e) => handleInputChange('customerPhone', e.target.value)}
                  placeholder="+****************"
                />
              </div>
            </div>

            {/* Additional Notes */}
            <div>
              <Label htmlFor="additionalNotes">
                {isArabic ? "ملاحظات إضافية" : "Additional Notes"}
              </Label>
              <Textarea
                id="additionalNotes"
                value={formData.additionalNotes}
                onChange={(e) => handleInputChange('additionalNotes', e.target.value)}
                placeholder={isArabic 
                  ? "أي معلومات إضافية أو أسئلة خاصة..."
                  : "Any additional information or special questions..."
                }
                rows={3}
              />
            </div>

            {/* Submit Button */}
            <div className="flex items-center justify-between pt-6 border-t">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                <CheckCircle className="h-4 w-4 inline mr-1 text-green-500" />
                {isArabic 
                  ? "سيتم إرسال استفسارك مباشرة للمورد"
                  : "Your inquiry will be sent directly to the supplier"
                }
                <br />
                <Clock className="h-4 w-4 inline mr-1 text-blue-500" />
                {isArabic 
                  ? `وقت الاستجابة المتوقع: ${product.supplier.responseTime}`
                  : `Expected response time: ${product.supplier.responseTime}`
                }
              </div>
              <div className="flex space-x-3">
                {onClose && (
                  <Button type="button" variant="outline" onClick={onClose}>
                    {isArabic ? "إلغاء" : "Cancel"}
                  </Button>
                )}
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="min-w-[140px]"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      {isArabic ? "جاري الإرسال..." : "Submitting..."}
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      {isArabic ? "إرسال الاستفسار" : "Submit Inquiry"}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Supplier Response Time */}
      <Card className="bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <Clock className="h-5 w-5 text-green-600" />
            <div>
              <h4 className="font-medium text-green-900 dark:text-green-100">
                {isArabic ? "وقت الاستجابة المتوقع" : "Expected Response Time"}
              </h4>
              <p className="text-sm text-green-700 dark:text-green-200">
                {isArabic 
                  ? `${product.supplier.contactPerson} عادة ما يستجيب خلال ${product.supplier.responseTime}`
                  : `${product.supplier.contactPerson} typically responds within ${product.supplier.responseTime}`
                }
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
