"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { formatDate } from "@/lib/utils"
import {
  Search,
  Package,
  Truck,
  MapPin,
  Clock,
  CheckCircle,
  AlertCircle,
  Phone,
  Mail,
  Calendar,
  User,
  CreditCard,
  ArrowRight,
  ExternalLink,
  Copy,
  Download,
} from "lucide-react"

interface TrackingEvent {
  id: string
  status: string
  description: string
  location: string
  timestamp: Date
  isCompleted: boolean
}

interface OrderDetails {
  orderNumber: string
  trackingNumber: string
  status: 'processing' | 'shipped' | 'in_transit' | 'out_for_delivery' | 'delivered' | 'exception'
  estimatedDelivery: Date
  actualDelivery?: Date
  carrier: string
  shippingMethod: string
  recipient: {
    name: string
    address: string
    city: string
    state: string
    zipCode: string
    country: string
  }
  items: Array<{
    name: string
    quantity: number
    image: string
  }>
  trackingEvents: TrackingEvent[]
}

export default function TrackOrderPage() {
  const [trackingInput, setTrackingInput] = useState("")
  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  const { t } = useLanguage()
  const { toast } = useToast()

  // Mock order data
  const mockOrderDetails: OrderDetails = {
    orderNumber: "ORD-2024-001",
    trackingNumber: "TRK123456789",
    status: 'in_transit',
    estimatedDelivery: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
    carrier: "FedEx",
    shippingMethod: "Express Shipping",
    recipient: {
      name: "John Doe",
      address: "123 Main Street",
      city: "New York",
      state: "NY",
      zipCode: "10001",
      country: "United States"
    },
    items: [
      {
        name: "Premium Wireless Headphones",
        quantity: 1,
        image: "/product-1.jpg"
      },
      {
        name: "Smart Fitness Watch",
        quantity: 1,
        image: "/product-2.jpg"
      }
    ],
    trackingEvents: [
      {
        id: "1",
        status: "Order Placed",
        description: "Your order has been received and is being processed",
        location: "AIDEVCOMMERCE Warehouse",
        timestamp: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000), // 4 days ago
        isCompleted: true
      },
      {
        id: "2",
        status: "Order Processed",
        description: "Your order has been processed and prepared for shipment",
        location: "AIDEVCOMMERCE Warehouse",
        timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        isCompleted: true
      },
      {
        id: "3",
        status: "Shipped",
        description: "Your package has been shipped and is on its way",
        location: "New York, NY Distribution Center",
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        isCompleted: true
      },
      {
        id: "4",
        status: "In Transit",
        description: "Your package is in transit to the destination",
        location: "Philadelphia, PA Sorting Facility",
        timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
        isCompleted: true
      },
      {
        id: "5",
        status: "Out for Delivery",
        description: "Your package is out for delivery",
        location: "Local Delivery Facility",
        timestamp: new Date(),
        isCompleted: false
      },
      {
        id: "6",
        status: "Delivered",
        description: "Package delivered successfully",
        location: "123 Main Street, New York, NY",
        timestamp: new Date(Date.now() + 4 * 60 * 60 * 1000), // 4 hours from now
        isCompleted: false
      }
    ]
  }

  const handleTrackOrder = async () => {
    if (!trackingInput.trim()) {
      setError("Please enter a tracking number or order number")
      return
    }

    setIsLoading(true)
    setError("")

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))

    // Mock validation - accept specific tracking numbers
    if (trackingInput.toLowerCase().includes('trk123456789') || 
        trackingInput.toLowerCase().includes('ord-2024-001')) {
      setOrderDetails(mockOrderDetails)
      toast({
        title: "Order Found!",
        description: "Your order tracking information has been loaded."
      })
    } else {
      setError("Order not found. Please check your tracking number and try again.")
      setOrderDetails(null)
    }

    setIsLoading(false)
  }

  const getStatusColor = (status: OrderDetails['status']) => {
    switch (status) {
      case 'processing':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'shipped':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'in_transit':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'out_for_delivery':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      case 'delivered':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'exception':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const copyTrackingNumber = () => {
    if (orderDetails) {
      navigator.clipboard.writeText(orderDetails.trackingNumber)
      toast({
        title: "Copied!",
        description: "Tracking number copied to clipboard"
      })
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-600 to-primary-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Track Your Order
            </h1>
            <p className="text-xl text-primary-100 mb-8">
              Enter your tracking number or order number to get real-time updates on your shipment
            </p>
            
            {/* Tracking Input */}
            <div className="max-w-2xl mx-auto">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="text"
                    placeholder="Enter tracking number or order number (e.g., TRK123456789)"
                    value={trackingInput}
                    onChange={(e) => setTrackingInput(e.target.value)}
                    className="w-full pl-12 pr-4 py-4 text-gray-900 bg-white rounded-lg shadow-lg focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
                    onKeyPress={(e) => e.key === 'Enter' && handleTrackOrder()}
                  />
                </div>
                <Button
                  onClick={handleTrackOrder}
                  disabled={isLoading}
                  className="bg-white text-primary-600 hover:bg-gray-100 px-8 py-4 h-auto"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600 mr-2"></div>
                      Tracking...
                    </>
                  ) : (
                    <>
                      <Package className="h-4 w-4 mr-2" />
                      Track Order
                    </>
                  )}
                </Button>
              </div>
              {error && (
                <div className="mt-4 p-4 bg-red-100 border border-red-300 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <span className="text-red-800 text-sm">{error}</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Order Details */}
      {orderDetails && (
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              {/* Order Summary */}
              <Card className="mb-8">
                <CardHeader>
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <CardTitle className="text-2xl">Order #{orderDetails.orderNumber}</CardTitle>
                      <CardDescription className="mt-2">
                        Tracking Number: {orderDetails.trackingNumber}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={copyTrackingNumber}
                          className="ml-2 p-1 h-auto"
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </CardDescription>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <Badge className={getStatusColor(orderDetails.status)}>
                        <Package className="h-3 w-3 mr-1" />
                        {orderDetails.status.replace('_', ' ').toUpperCase()}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                        <Truck className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">Carrier</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{orderDetails.carrier}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                        <Clock className="h-5 w-5 text-green-600 dark:text-green-400" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">Estimated Delivery</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {formatDate(orderDetails.estimatedDelivery)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                        <MapPin className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">Shipping Method</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{orderDetails.shippingMethod}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Tracking Timeline */}
                <div className="lg:col-span-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>Tracking History</CardTitle>
                      <CardDescription>
                        Real-time updates on your package's journey
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        {orderDetails.trackingEvents.map((event, index) => (
                          <div key={event.id} className="flex items-start space-x-4">
                            <div className="flex flex-col items-center">
                              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                event.isCompleted
                                  ? 'bg-green-100 dark:bg-green-900'
                                  : index === orderDetails.trackingEvents.findIndex(e => !e.isCompleted)
                                  ? 'bg-blue-100 dark:bg-blue-900'
                                  : 'bg-gray-100 dark:bg-gray-800'
                              }`}>
                                {event.isCompleted ? (
                                  <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                                ) : index === orderDetails.trackingEvents.findIndex(e => !e.isCompleted) ? (
                                  <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                ) : (
                                  <div className="w-2 h-2 bg-gray-400 rounded-full" />
                                )}
                              </div>
                              {index < orderDetails.trackingEvents.length - 1 && (
                                <div className={`w-0.5 h-12 mt-2 ${
                                  event.isCompleted ? 'bg-green-200 dark:bg-green-800' : 'bg-gray-200 dark:bg-gray-700'
                                }`} />
                              )}
                            </div>
                            <div className="flex-1 pb-6">
                              <div className="flex items-center justify-between mb-1">
                                <h3 className={`font-medium ${
                                  event.isCompleted
                                    ? 'text-gray-900 dark:text-white'
                                    : index === orderDetails.trackingEvents.findIndex(e => !e.isCompleted)
                                    ? 'text-blue-600 dark:text-blue-400'
                                    : 'text-gray-500 dark:text-gray-400'
                                }`}>
                                  {event.status}
                                </h3>
                                <span className="text-sm text-gray-500">
                                  {formatDate(event.timestamp)}
                                </span>
                              </div>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                                {event.description}
                              </p>
                              <p className="text-xs text-gray-500 flex items-center">
                                <MapPin className="h-3 w-3 mr-1" />
                                {event.location}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Order Items & Delivery Info */}
                <div className="space-y-6">
                  {/* Order Items */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Order Items</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {orderDetails.items.map((item, index) => (
                          <div key={index} className="flex items-center space-x-3">
                            <div className="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                              <Package className="h-6 w-6 text-gray-400" />
                            </div>
                            <div className="flex-1">
                              <p className="font-medium text-gray-900 dark:text-white text-sm">
                                {item.name}
                              </p>
                              <p className="text-xs text-gray-500">
                                Quantity: {item.quantity}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Delivery Address */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Delivery Address</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 text-sm">
                        <p className="font-medium text-gray-900 dark:text-white">
                          {orderDetails.recipient.name}
                        </p>
                        <p className="text-gray-600 dark:text-gray-400">
                          {orderDetails.recipient.address}
                        </p>
                        <p className="text-gray-600 dark:text-gray-400">
                          {orderDetails.recipient.city}, {orderDetails.recipient.state} {orderDetails.recipient.zipCode}
                        </p>
                        <p className="text-gray-600 dark:text-gray-400">
                          {orderDetails.recipient.country}
                        </p>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Actions */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Need Help?</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <Button variant="outline" className="w-full justify-start">
                        <Phone className="h-4 w-4 mr-2" />
                        Contact Carrier
                      </Button>
                      <Button variant="outline" className="w-full justify-start">
                        <Mail className="h-4 w-4 mr-2" />
                        Report Issue
                      </Button>
                      <Button variant="outline" className="w-full justify-start">
                        <Download className="h-4 w-4 mr-2" />
                        Download Receipt
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Help Section */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Need Help with Your Order?
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Our customer support team is here to help with any questions about your order
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <Card className="text-center">
              <CardContent className="p-6">
                <Phone className="h-12 w-12 text-primary-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Call Support
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  Speak with our customer service team
                </p>
                <Button variant="outline" className="w-full">
                  +****************
                </Button>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="p-6">
                <Mail className="h-12 w-12 text-primary-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Email Support
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  Send us a detailed message
                </p>
                <Button variant="outline" className="w-full">
                  Send Email
                </Button>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="p-6">
                <Package className="h-12 w-12 text-primary-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Order Issues
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  Report problems with your order
                </p>
                <Button variant="outline" className="w-full">
                  Report Issue
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  )
}
