import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding for China-Arab Wholesale Marketplace...')

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 12)

  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin User',
      nameAr: 'مدير النظام',
      password: hashedPassword,
      role: 'SUPER_ADMIN',
      emailVerified: new Date(),
    },
  })

  console.log('✅ Admin user created:', adminUser.email)

  // Create sample Arab merchant users
  const merchantPassword = await bcrypt.hash('merchant123', 12)

  const merchant1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON>',
      nameAr: 'أحمد حسن',
      role: 'CUSTOMER',
      password: merchantPassword,
      emailVerified: new Date(),
      phone: '+971501234567',
      country: 'UAE',
      city: 'Dubai',
    },
  })

  const merchant2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Omar Salem',
      nameAr: 'عمر سالم',
      role: 'CUSTOMER',
      password: merchantPassword,
      emailVerified: new Date(),
      phone: '+966501234567',
      country: 'Saudi Arabia',
      city: 'Riyadh',
    },
  })

  console.log('✅ Merchant users created')

  // Create merchant profiles
  await prisma.merchantProfile.create({
    data: {
      userId: merchant1.id,
      companyName: 'Hassan Trading LLC',
      companyNameAr: 'شركة حسن للتجارة ذ.م.م',
      businessType: 'importer',
      establishedYear: 2015,
      contactPerson: 'Ahmed Hassan',
      position: 'CEO',
      whatsapp: '+971501234567',
      businessAddress: 'Dubai International City',
      city: 'Dubai',
      country: 'UAE',
      verified: true,
      preferredLanguage: 'ar',
      currency: 'USD',
      creditLimit: 50000,
    },
  })

  await prisma.merchantProfile.create({
    data: {
      userId: merchant2.id,
      companyName: 'Salem Import & Export',
      companyNameAr: 'شركة سالم للاستيراد والتصدير',
      businessType: 'distributor',
      establishedYear: 2018,
      contactPerson: 'Omar Salem',
      position: 'Managing Director',
      whatsapp: '+966501234567',
      businessAddress: 'King Fahd Road',
      city: 'Riyadh',
      country: 'Saudi Arabia',
      verified: true,
      preferredLanguage: 'ar',
      currency: 'USD',
      creditLimit: 75000,
    },
  })

  console.log('✅ Merchant profiles created')

  // Create Chinese suppliers
  const supplier1 = await prisma.supplier.create({
    data: {
      name: 'Guangzhou Electronics Manufacturing Co.',
      nameAr: 'شركة قوانغتشو لتصنيع الإلكترونيات',
      companyName: 'Guangzhou Electronics Manufacturing Co., Ltd.',
      contactPerson: 'Li Wei',
      email: '<EMAIL>',
      phone: '+86-20-12345678',
      whatsapp: '+86-***********',
      wechat: 'liwei_gzelec',
      address: 'No. 123 Tianhe Road',
      city: 'Guangzhou',
      province: 'Guangdong',
      country: 'China',
      postalCode: '510000',
      businessLicense: 'BL-GZ-2020-001',
      verified: true,
      rating: 4.8,
      trustScore: 95,
    },
  })

  const supplier2 = await prisma.supplier.create({
    data: {
      name: 'Yiwu Commodity Trading Hub',
      nameAr: 'مركز ييوو للتجارة السلعية',
      companyName: 'Yiwu International Commodity Trading Co., Ltd.',
      contactPerson: 'Zhang Ming',
      email: '<EMAIL>',
      phone: '+86-579-87654321',
      whatsapp: '+86-***********',
      wechat: 'zhangming_yiwu',
      address: 'Yiwu International Trade City',
      city: 'Yiwu',
      province: 'Zhejiang',
      country: 'China',
      postalCode: '322000',
      businessLicense: 'BL-YW-2019-002',
      verified: true,
      rating: 4.6,
      trustScore: 88,
    },
  })

  const supplier3 = await prisma.supplier.create({
    data: {
      name: 'Shenzhen Tech Solutions',
      nameAr: 'شنتشن للحلول التقنية',
      companyName: 'Shenzhen Advanced Technology Solutions Ltd.',
      contactPerson: 'Wang Lei',
      email: '<EMAIL>',
      phone: '+86-755-98765432',
      whatsapp: '+86-***********',
      wechat: 'wanglei_sztech',
      address: 'Nanshan Technology Park',
      city: 'Shenzhen',
      province: 'Guangdong',
      country: 'China',
      postalCode: '518000',
      businessLicense: 'BL-SZ-2021-003',
      verified: true,
      rating: 4.9,
      trustScore: 98,
    },
  })

  console.log('✅ Chinese suppliers created')

  // Create test user
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Test User',
      password: await bcrypt.hash('user123', 12),
      role: 'USER',
      emailVerified: new Date(),
    },
  })

  console.log('✅ Test user created:', testUser.email)

  // Create product categories
  const categories = [
    {
      name: 'Electronics & Technology',
      nameAr: 'الإلكترونيات والتكنولوجيا',
      slug: 'electronics-technology',
      description: 'Consumer electronics, smartphones, computers, and tech accessories',
      descriptionAr: 'الإلكترونيات الاستهلاكية والهواتف الذكية وأجهزة الكمبيوتر والإكسسوارات التقنية',
      status: 'ACTIVE' as const,
    },
    {
      name: 'Textiles & Apparel',
      nameAr: 'المنسوجات والملابس',
      slug: 'textiles-apparel',
      description: 'Clothing, fabrics, shoes, and fashion accessories for wholesale',
      descriptionAr: 'الملابس والأقمشة والأحذية وإكسسوارات الموضة للبيع بالجملة',
      status: 'ACTIVE' as const,
    },
    {
      name: 'Home & Living',
      nameAr: 'المنزل والمعيشة',
      slug: 'home-living',
      description: 'Furniture, home decor, kitchenware, and household items',
      descriptionAr: 'الأثاث وديكور المنزل وأدوات المطبخ والأدوات المنزلية',
      status: 'ACTIVE' as const,
    },
    {
      name: 'Industrial Equipment',
      nameAr: 'المعدات الصناعية',
      slug: 'industrial-equipment',
      description: 'Manufacturing machinery, production lines, and industrial tools',
      descriptionAr: 'آلات التصنيع وخطوط الإنتاج والأدوات الصناعية',
      status: 'ACTIVE' as const,
    },
    {
      name: 'Raw Materials',
      nameAr: 'المواد الخام',
      slug: 'raw-materials',
      description: 'Metals, plastics, chemicals, and other manufacturing materials',
      descriptionAr: 'المعادن والبلاستيك والمواد الكيميائية ومواد التصنيع الأخرى',
      status: 'ACTIVE' as const,
    },
    {
      name: 'Stock Liquidation',
      nameAr: 'تصفية المخزون',
      slug: 'stock-liquidation',
      description: 'Discounted bulk inventory and overstock items',
      descriptionAr: 'مخزون مخفض بالجملة وعناصر فائض المخزون',
      status: 'ACTIVE' as const,
    },
  ]

  const createdCategories = []
  for (const category of categories) {
    const created = await prisma.category.upsert({
      where: { slug: category.slug },
      update: {},
      create: category,
    })
    createdCategories.push(created)
  }

  console.log('✅ Categories created:', createdCategories.length)

  // Create sample products
  const products = [
    {
      name: 'Wireless Bluetooth Headphones',
      slug: 'wireless-bluetooth-headphones',
      description: 'High-quality wireless headphones with noise cancellation and long battery life.',
      shortDescription: 'Premium wireless headphones with noise cancellation',
      price: 199.99,
      comparePrice: 249.99,
      sku: 'WBH-001',
      categoryId: createdCategories[0].id, // Electronics
      tags: ['wireless', 'bluetooth', 'headphones', 'audio'],
      status: 'ACTIVE' as const,
      featured: true,
      quantity: 50,
    },
    {
      name: 'Smart Fitness Watch',
      slug: 'smart-fitness-watch',
      description: 'Advanced fitness tracking watch with heart rate monitor, GPS, and smartphone connectivity.',
      shortDescription: 'Smart watch with fitness tracking and GPS',
      price: 299.99,
      comparePrice: 399.99,
      sku: 'SFW-001',
      categoryId: createdCategories[3].id, // Sports & Outdoors
      tags: ['smartwatch', 'fitness', 'gps', 'health'],
      status: 'ACTIVE' as const,
      featured: true,
      quantity: 30,
    },
    {
      name: 'Organic Cotton T-Shirt',
      slug: 'organic-cotton-t-shirt',
      description: 'Comfortable and sustainable organic cotton t-shirt available in multiple colors.',
      shortDescription: 'Sustainable organic cotton t-shirt',
      price: 29.99,
      comparePrice: 39.99,
      sku: 'OCT-001',
      categoryId: createdCategories[1].id, // Clothing
      tags: ['organic', 'cotton', 'sustainable', 'clothing'],
      status: 'ACTIVE' as const,
      featured: false,
      quantity: 100,
    },
    {
      name: 'Smart Home Security Camera',
      slug: 'smart-home-security-camera',
      description: 'WiFi-enabled security camera with night vision, motion detection, and mobile app control.',
      shortDescription: 'WiFi security camera with night vision',
      price: 149.99,
      comparePrice: 199.99,
      sku: 'SHSC-001',
      categoryId: createdCategories[2].id, // Home & Garden
      tags: ['security', 'camera', 'smart-home', 'wifi'],
      status: 'ACTIVE' as const,
      featured: true,
      quantity: 25,
    },
  ]

  const createdProducts = []
  for (const product of products) {
    const created = await prisma.product.upsert({
      where: { slug: product.slug },
      update: {},
      create: product,
    })
    createdProducts.push(created)
  }

  console.log('✅ Products created:', createdProducts.length)

  // Create service categories
  const serviceCategories = [
    {
      name: 'Web Development',
      slug: 'web-development',
      description: 'Custom web development services',
      status: 'ACTIVE' as const,
    },
    {
      name: 'Digital Marketing',
      slug: 'digital-marketing',
      description: 'Comprehensive digital marketing solutions',
      status: 'ACTIVE' as const,
    },
    {
      name: 'Consulting',
      slug: 'consulting',
      description: 'Business and technology consulting',
      status: 'ACTIVE' as const,
    },
  ]

  const createdServiceCategories = []
  for (const category of serviceCategories) {
    const created = await prisma.serviceCategory.upsert({
      where: { slug: category.slug },
      update: {},
      create: category,
    })
    createdServiceCategories.push(created)
  }

  console.log('✅ Service categories created:', createdServiceCategories.length)

  // Create sample services
  const services = [
    {
      name: 'E-commerce Website Development',
      slug: 'ecommerce-website-development',
      description: 'Complete e-commerce website development with modern technologies and best practices.',
      shortDescription: 'Professional e-commerce website development',
      price: 2999.99,
      duration: 2880, // 48 hours
      categoryId: createdServiceCategories[0].id,
      features: ['Responsive Design', 'Payment Integration', 'Admin Dashboard', 'SEO Optimization'],
      images: [],
      status: 'ACTIVE' as const,
      featured: true,
    },
    {
      name: 'SEO Optimization Package',
      slug: 'seo-optimization-package',
      description: 'Comprehensive SEO optimization to improve your website\'s search engine rankings.',
      shortDescription: 'Complete SEO optimization service',
      price: 999.99,
      duration: 720, // 12 hours
      categoryId: createdServiceCategories[1].id,
      features: ['Keyword Research', 'On-page SEO', 'Technical SEO', 'Content Optimization'],
      images: [],
      status: 'ACTIVE' as const,
      featured: true,
    },
    {
      name: 'Business Strategy Consulting',
      slug: 'business-strategy-consulting',
      description: 'Expert business strategy consulting to help grow your business and optimize operations.',
      shortDescription: 'Professional business strategy consulting',
      price: 199.99,
      duration: 60, // 1 hour
      categoryId: createdServiceCategories[2].id,
      features: ['Market Analysis', 'Growth Strategy', 'Competitive Analysis', 'Action Plan'],
      images: [],
      status: 'ACTIVE' as const,
      featured: false,
    },
  ]

  const createdServices = []
  for (const service of services) {
    const created = await prisma.service.upsert({
      where: { slug: service.slug },
      update: {},
      create: service,
    })
    createdServices.push(created)
  }

  console.log('✅ Services created:', createdServices.length)

  // Create production line categories
  const productionLineCategories = [
    {
      name: 'Manufacturing Equipment',
      slug: 'manufacturing-equipment',
      description: 'Industrial manufacturing equipment and machinery',
      status: 'ACTIVE' as const,
    },
    {
      name: 'Packaging Solutions',
      slug: 'packaging-solutions',
      description: 'Automated packaging and labeling systems',
      status: 'ACTIVE' as const,
    },
  ]

  const createdProductionLineCategories = []
  for (const category of productionLineCategories) {
    const created = await prisma.productionLineCategory.upsert({
      where: { slug: category.slug },
      update: {},
      create: category,
    })
    createdProductionLineCategories.push(created)
  }

  console.log('✅ Production line categories created:', createdProductionLineCategories.length)

  // Create sample production lines
  const productionLines = [
    {
      name: 'Automated Assembly Line System',
      slug: 'automated-assembly-line-system',
      description: 'State-of-the-art automated assembly line system for high-volume manufacturing.',
      shortDescription: 'Advanced automated assembly line',
      specifications: {
        capacity: '1000 units/hour',
        power: '50kW',
        dimensions: '20m x 5m x 3m',
        weight: '15000kg',
        automation: 'Fully automated',
        quality: 'ISO 9001 certified',
      },
      images: [],
      videos: [],
      categoryId: createdProductionLineCategories[0].id,
      status: 'ACTIVE' as const,
      featured: true,
    },
    {
      name: 'Smart Packaging Line',
      slug: 'smart-packaging-line',
      description: 'Intelligent packaging line with quality control and automated labeling.',
      shortDescription: 'Smart automated packaging solution',
      specifications: {
        capacity: '500 packages/hour',
        power: '25kW',
        dimensions: '15m x 3m x 2.5m',
        weight: '8000kg',
        features: 'Quality control, Auto-labeling',
        certification: 'CE certified',
      },
      images: [],
      videos: [],
      categoryId: createdProductionLineCategories[1].id,
      status: 'ACTIVE' as const,
      featured: true,
    },
  ]

  const createdProductionLines = []
  for (const line of productionLines) {
    const created = await prisma.productionLine.upsert({
      where: { slug: line.slug },
      update: {},
      create: line,
    })
    createdProductionLines.push(created)
  }

  console.log('✅ Production lines created:', createdProductionLines.length)

  // Create blog categories
  const blogCategories = [
    {
      name: 'Technology',
      slug: 'technology',
      description: 'Latest technology trends and insights',
      status: 'ACTIVE' as const,
    },
    {
      name: 'Business',
      slug: 'business',
      description: 'Business strategies and growth tips',
      status: 'ACTIVE' as const,
    },
    {
      name: 'Industry News',
      slug: 'industry-news',
      description: 'Latest news from the industry',
      status: 'ACTIVE' as const,
    },
  ]

  const createdBlogCategories = []
  for (const category of blogCategories) {
    const created = await prisma.blogCategory.upsert({
      where: { slug: category.slug },
      update: {},
      create: category,
    })
    createdBlogCategories.push(created)
  }

  console.log('✅ Blog categories created:', createdBlogCategories.length)

  // Create sample blog posts
  const blogPosts = [
    {
      title: 'The Future of E-commerce: AI and Machine Learning',
      slug: 'future-of-ecommerce-ai-machine-learning',
      excerpt: 'Discover how artificial intelligence and machine learning are revolutionizing the e-commerce industry.',
      content: 'Artificial intelligence and machine learning are transforming the e-commerce landscape in unprecedented ways. From personalized product recommendations to intelligent chatbots, these technologies are enhancing customer experiences and driving business growth...',
      featuredImage: '/blog/ai-ecommerce.jpg',
      images: ['/blog/ai-ecommerce-1.jpg', '/blog/ai-ecommerce-2.jpg'],
      authorId: adminUser.id,
      categoryId: createdBlogCategories[0].id, // Technology
      tags: ['AI', 'Machine Learning', 'E-commerce', 'Technology'],
      status: 'published' as const,
      featured: true,
      publishedAt: new Date(),
      seoTitle: 'The Future of E-commerce: AI and Machine Learning | AIDEVCOMMERCE',
      seoDescription: 'Learn how AI and ML are revolutionizing e-commerce. Discover the latest trends and technologies shaping the future of online retail.',
      readingTime: 8,
    },
    {
      title: 'Building a Successful Online Business in 2024',
      slug: 'building-successful-online-business-2024',
      excerpt: 'Essential strategies and tips for launching and growing a profitable online business in today\'s competitive market.',
      content: 'Starting an online business in 2024 requires a strategic approach and understanding of current market trends. This comprehensive guide covers everything from market research to digital marketing strategies...',
      featuredImage: '/blog/online-business.jpg',
      images: ['/blog/online-business-1.jpg'],
      authorId: adminUser.id,
      categoryId: createdBlogCategories[1].id, // Business
      tags: ['Business', 'Entrepreneurship', 'Online Business', 'Strategy'],
      status: 'published' as const,
      featured: true,
      publishedAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
      seoTitle: 'Building a Successful Online Business in 2024 | AIDEVCOMMERCE',
      seoDescription: 'Complete guide to starting and growing an online business in 2024. Learn proven strategies for success.',
      readingTime: 12,
    },
    {
      title: 'Top 10 E-commerce Trends to Watch in 2024',
      slug: 'top-10-ecommerce-trends-2024',
      excerpt: 'Stay ahead of the competition with these emerging e-commerce trends that will shape the industry in 2024.',
      content: 'The e-commerce industry continues to evolve rapidly, with new trends emerging that reshape how businesses operate and customers shop. Here are the top 10 trends to watch in 2024...',
      featuredImage: '/blog/ecommerce-trends.jpg',
      images: [],
      authorId: adminUser.id,
      categoryId: createdBlogCategories[2].id, // Industry News
      tags: ['E-commerce', 'Trends', 'Industry', '2024'],
      status: 'published' as const,
      featured: false,
      publishedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      seoTitle: 'Top 10 E-commerce Trends to Watch in 2024 | AIDEVCOMMERCE',
      seoDescription: 'Discover the latest e-commerce trends for 2024. Stay competitive with insights into emerging technologies and consumer behaviors.',
      readingTime: 6,
    },
    {
      title: 'How to Optimize Your Website for Better Performance',
      slug: 'optimize-website-better-performance',
      excerpt: 'Learn practical techniques to improve your website\'s speed, user experience, and search engine rankings.',
      content: 'Website performance is crucial for user experience and SEO. This guide covers essential optimization techniques including image compression, code minification, and caching strategies...',
      featuredImage: '/blog/website-optimization.jpg',
      images: ['/blog/performance-1.jpg', '/blog/performance-2.jpg'],
      authorId: adminUser.id,
      categoryId: createdBlogCategories[0].id, // Technology
      tags: ['Web Development', 'Performance', 'SEO', 'Optimization'],
      status: 'published' as const,
      featured: false,
      publishedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      seoTitle: 'Website Performance Optimization Guide | AIDEVCOMMERCE',
      seoDescription: 'Complete guide to optimizing website performance. Learn techniques to improve speed, UX, and SEO rankings.',
      readingTime: 10,
    },
  ]

  const createdBlogPosts = []
  for (const post of blogPosts) {
    const created = await prisma.blogPost.upsert({
      where: { slug: post.slug },
      update: {},
      create: post,
    })
    createdBlogPosts.push(created)
  }

  console.log('✅ Blog posts created:', createdBlogPosts.length)

  console.log('🎉 Database seeding completed successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
