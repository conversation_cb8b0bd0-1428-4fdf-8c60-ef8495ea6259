"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useLanguage } from "@/components/providers/language-provider"
import {
  Star,
  Quote,
  ChevronLeft,
  ChevronRight,
  Users,
  Award,
  CheckCircle,
  Heart,
  ThumbsUp,
  MessageCircle,
} from "lucide-react"

interface Testimonial {
  id: string
  name: string
  role: string
  company: string
  avatar: string
  rating: number
  content: string
  date: string
  verified: boolean
  category: string
  location: string
}

const testimonials: Testimonial[] = [
  {
    id: "1",
    name: "<PERSON>",
    role: "CEO",
    company: "TechStart Inc.",
    avatar: "/testimonial-1.jpg",
    rating: 5,
    content: "AIDEVCOMMERCE has completely transformed our business operations. The AI-powered recommendations have increased our sales by 40%, and the customer service is exceptional. The platform is intuitive, reliable, and constantly evolving with new features.",
    date: "2024-01-15",
    verified: true,
    category: "Business Growth",
    location: "San Francisco, CA"
  },
  {
    id: "2",
    name: "<PERSON>",
    role: "CTO",
    company: "InnovateLab",
    avatar: "/testimonial-2.jpg",
    rating: 5,
    content: "The technical excellence and innovation behind AIDEVCOMMERCE is remarkable. Their API integration was seamless, and the AI capabilities have helped us deliver personalized experiences to our customers. Outstanding platform!",
    date: "2024-01-10",
    verified: true,
    category: "Technical Excellence",
    location: "New York, NY"
  },
  {
    id: "3",
    name: "Emily Rodriguez",
    role: "Marketing Director",
    company: "GrowthCorp",
    avatar: "/testimonial-3.jpg",
    rating: 5,
    content: "The analytics and insights provided by AIDEVCOMMERCE are game-changing. We've been able to understand our customers better and create targeted campaigns that actually convert. The ROI has been incredible.",
    date: "2024-01-08",
    verified: true,
    category: "Marketing Success",
    location: "Austin, TX"
  },
  {
    id: "4",
    name: "David Thompson",
    role: "Operations Manager",
    company: "ScaleUp Solutions",
    avatar: "/testimonial-4.jpg",
    rating: 5,
    content: "From day one, AIDEVCOMMERCE has exceeded our expectations. The platform handles our high-volume transactions effortlessly, and the customer support team is always there when we need them. Highly recommended!",
    date: "2024-01-05",
    verified: true,
    category: "Scalability",
    location: "Seattle, WA"
  },
  {
    id: "5",
    name: "Lisa Wang",
    role: "Founder",
    company: "EcoTech Ventures",
    avatar: "/testimonial-5.jpg",
    rating: 5,
    content: "The sustainability features and ethical business practices of AIDEVCOMMERCE align perfectly with our values. They've helped us build a responsible e-commerce presence while maintaining excellent performance.",
    date: "2024-01-03",
    verified: true,
    category: "Sustainability",
    location: "Portland, OR"
  },
  {
    id: "6",
    name: "James Wilson",
    role: "E-commerce Manager",
    company: "RetailPro",
    avatar: "/testimonial-6.jpg",
    rating: 5,
    content: "The user experience is phenomenal. Our customers love the personalized shopping experience, and we've seen a significant increase in customer retention. The platform just works beautifully.",
    date: "2024-01-01",
    verified: true,
    category: "User Experience",
    location: "Chicago, IL"
  }
]

const categories = [
  "All Reviews",
  "Business Growth",
  "Technical Excellence", 
  "Marketing Success",
  "Scalability",
  "Sustainability",
  "User Experience"
]

export function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [selectedCategory, setSelectedCategory] = useState("All Reviews")
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const { t } = useLanguage()

  const filteredTestimonials = selectedCategory === "All Reviews" 
    ? testimonials 
    : testimonials.filter(t => t.category === selectedCategory)

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % filteredTestimonials.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [isAutoPlaying, filteredTestimonials.length])

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % filteredTestimonials.length)
    setIsAutoPlaying(false)
  }

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + filteredTestimonials.length) % filteredTestimonials.length)
    setIsAutoPlaying(false)
  }

  const goToTestimonial = (index: number) => {
    setCurrentIndex(index)
    setIsAutoPlaying(false)
  }

  const currentTestimonial = filteredTestimonials[currentIndex]

  if (!currentTestimonial) return null

  return (
    <section className="py-20 bg-gradient-to-br from-amber-50 via-orange-50 to-red-50 dark:from-amber-900/20 dark:via-orange-900/20 dark:to-red-900/20">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge className="mb-4 px-4 py-2 bg-gradient-to-r from-orange-500 to-red-500 text-white">
            <Heart className="h-4 w-4 mr-2" />
            Customer Love
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
              What Our Customers
            </span>
            <br />
            <span className="text-gray-900 dark:text-white">Are Saying</span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed">
            Don't just take our word for it. Here's what real customers have to say about 
            their experience with AIDEVCOMMERCE and how we've helped transform their businesses.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-2 mb-12">
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => {
                setSelectedCategory(category)
                setCurrentIndex(0)
                setIsAutoPlaying(true)
              }}
              className={selectedCategory === category ? "bg-gradient-to-r from-orange-500 to-red-500" : ""}
            >
              {category}
            </Button>
          ))}
        </div>

        {/* Main Testimonial Display */}
        <div className="max-w-4xl mx-auto mb-12">
          <Card className="overflow-hidden shadow-2xl border-0 bg-white dark:bg-gray-800">
            <CardContent className="p-0">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-0">
                {/* Customer Photo & Info */}
                <div className="bg-gradient-to-br from-orange-500 to-red-500 p-8 text-white flex flex-col justify-center items-center text-center">
                  {/* Avatar */}
                  <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mb-4 backdrop-blur-sm">
                    <Users className="h-12 w-12 text-white" />
                  </div>

                  {/* Customer Info */}
                  <h3 className="text-xl font-bold mb-1">{currentTestimonial.name}</h3>
                  <p className="text-orange-100 text-sm mb-1">{currentTestimonial.role}</p>
                  <p className="text-orange-100 text-sm font-medium mb-2">{currentTestimonial.company}</p>
                  <p className="text-orange-200 text-xs mb-4">{currentTestimonial.location}</p>

                  {/* Rating */}
                  <div className="flex items-center space-x-1 mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star 
                        key={i} 
                        className={`h-4 w-4 ${i < currentTestimonial.rating ? 'text-yellow-300 fill-current' : 'text-orange-300'}`} 
                      />
                    ))}
                  </div>

                  {/* Verified Badge */}
                  {currentTestimonial.verified && (
                    <div className="flex items-center space-x-1 bg-white/20 rounded-full px-3 py-1">
                      <CheckCircle className="h-3 w-3" />
                      <span className="text-xs">Verified Customer</span>
                    </div>
                  )}
                </div>

                {/* Testimonial Content */}
                <div className="lg:col-span-2 p-8 flex flex-col justify-center">
                  {/* Quote Icon */}
                  <Quote className="h-12 w-12 text-orange-500 mb-6" />

                  {/* Content */}
                  <blockquote className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed mb-6 italic">
                    "{currentTestimonial.content}"
                  </blockquote>

                  {/* Meta Info */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <Badge className="bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                        {currentTestimonial.category}
                      </Badge>
                      <span className="text-sm text-gray-500">
                        {new Date(currentTestimonial.date).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm" className="p-2">
                        <ThumbsUp className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" className="p-2">
                        <MessageCircle className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Navigation Controls */}
        <div className="flex items-center justify-center space-x-4 mb-12">
          <Button
            variant="outline"
            size="sm"
            onClick={prevTestimonial}
            className="w-10 h-10 rounded-full p-0"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          {/* Dots Indicator */}
          <div className="flex space-x-2">
            {filteredTestimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToTestimonial(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? "bg-orange-500 w-8"
                    : "bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500"
                }`}
              />
            ))}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={nextTestimonial}
            className="w-10 h-10 rounded-full p-0"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Testimonial Grid Preview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          {filteredTestimonials.slice(0, 3).map((testimonial, index) => (
            <Card 
              key={testimonial.id}
              className={`cursor-pointer transition-all duration-300 hover:shadow-lg hover:-translate-y-1 ${
                index === currentIndex ? 'ring-2 ring-orange-500' : ''
              }`}
              onClick={() => goToTestimonial(index)}
            >
              <CardContent className="p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-sm">
                      {testimonial.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white text-sm">
                      {testimonial.name}
                    </h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {testimonial.role}, {testimonial.company}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-1 mb-3">
                  {[...Array(5)].map((_, i) => (
                    <Star 
                      key={i} 
                      className={`h-3 w-3 ${i < testimonial.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
                    />
                  ))}
                </div>

                <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">
                  "{testimonial.content}"
                </p>

                <div className="flex items-center justify-between mt-4">
                  <Badge variant="outline" className="text-xs">
                    {testimonial.category}
                  </Badge>
                  {testimonial.verified && (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Trust Indicators */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600 mb-2">4.9/5</div>
            <div className="text-gray-600 dark:text-gray-400">Average Rating</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600 mb-2">10,000+</div>
            <div className="text-gray-600 dark:text-gray-400">Reviews</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600 mb-2">98%</div>
            <div className="text-gray-600 dark:text-gray-400">Satisfaction Rate</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600 mb-2">24/7</div>
            <div className="text-gray-600 dark:text-gray-400">Support</div>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-2xl p-8">
            <h3 className="text-2xl font-bold mb-4">
              Join Thousands of Satisfied Customers
            </h3>
            <p className="text-orange-100 mb-6 max-w-2xl mx-auto">
              Experience the difference that AIDEVCOMMERCE can make for your business. 
              Start your journey today and see why customers love working with us.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="px-8 py-4 text-lg font-semibold bg-white text-orange-600 hover:bg-gray-100">
                Start Free Trial
              </Button>
              <Button variant="outline" size="lg" className="px-8 py-4 text-lg font-semibold border-white text-white hover:bg-white/10">
                View All Reviews
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
