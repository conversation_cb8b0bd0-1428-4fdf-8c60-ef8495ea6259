// Email service for sending notifications
// In production, you would use services like SendGrid, Mailgun, or AWS SES

interface EmailTemplate {
  subject: string
  html: string
  text: string
}

interface OrderConfirmationData {
  orderId: string
  customerName: string
  customerEmail: string
  orderDate: Date
  items: Array<{
    name: string
    quantity: number
    price: number
    image?: string
  }>
  subtotal: number
  shipping: number
  tax: number
  total: number
  shippingAddress: {
    name: string
    address: string
    city: string
    state: string
    zipCode: string
    country: string
  }
  estimatedDelivery: Date
}

interface WelcomeEmailData {
  customerName: string
  customerEmail: string
}

interface PasswordResetData {
  customerName: string
  customerEmail: string
  resetToken: string
  resetUrl: string
}

class EmailService {
  private apiKey: string
  private fromEmail: string
  private fromName: string

  constructor() {
    this.apiKey = process.env.EMAIL_API_KEY || ''
    this.fromEmail = process.env.FROM_EMAIL || '<EMAIL>'
    this.fromName = process.env.FROM_NAME || 'AIDEVCOMMERCE'
  }

  // Order confirmation email template
  private generateOrderConfirmationTemplate(data: OrderConfirmationData): EmailTemplate {
    const subject = `Order Confirmation - ${data.orderId}`
    
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${subject}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #3b82f6; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9fafb; }
            .order-details { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; }
            .item { display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #e5e7eb; }
            .total { font-weight: bold; font-size: 18px; color: #3b82f6; }
            .footer { text-align: center; padding: 20px; color: #6b7280; }
            .button { background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 10px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Order Confirmed!</h1>
              <p>Thank you for your purchase, ${data.customerName}</p>
            </div>
            
            <div class="content">
              <h2>Order Details</h2>
              <div class="order-details">
                <p><strong>Order Number:</strong> ${data.orderId}</p>
                <p><strong>Order Date:</strong> ${data.orderDate.toLocaleDateString()}</p>
                <p><strong>Estimated Delivery:</strong> ${data.estimatedDelivery.toLocaleDateString()}</p>
                
                <h3>Items Ordered</h3>
                ${data.items.map(item => `
                  <div class="item">
                    <span>${item.name} × ${item.quantity}</span>
                    <span>$${(item.price * item.quantity).toFixed(2)}</span>
                  </div>
                `).join('')}
                
                <div class="item">
                  <span>Subtotal</span>
                  <span>$${data.subtotal.toFixed(2)}</span>
                </div>
                <div class="item">
                  <span>Shipping</span>
                  <span>$${data.shipping.toFixed(2)}</span>
                </div>
                <div class="item">
                  <span>Tax</span>
                  <span>$${data.tax.toFixed(2)}</span>
                </div>
                <div class="item total">
                  <span>Total</span>
                  <span>$${data.total.toFixed(2)}</span>
                </div>
              </div>
              
              <h3>Shipping Address</h3>
              <div class="order-details">
                <p>${data.shippingAddress.name}</p>
                <p>${data.shippingAddress.address}</p>
                <p>${data.shippingAddress.city}, ${data.shippingAddress.state} ${data.shippingAddress.zipCode}</p>
                <p>${data.shippingAddress.country}</p>
              </div>
              
              <div style="text-align: center;">
                <a href="https://aidevcommerce.com/account/orders" class="button">Track Your Order</a>
              </div>
            </div>
            
            <div class="footer">
              <p>Questions? Contact <NAME_EMAIL></p>
              <p>© 2024 AIDEVCOMMERCE. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `
    
    const text = `
      Order Confirmed!
      
      Thank you for your purchase, ${data.customerName}
      
      Order Number: ${data.orderId}
      Order Date: ${data.orderDate.toLocaleDateString()}
      Estimated Delivery: ${data.estimatedDelivery.toLocaleDateString()}
      
      Items Ordered:
      ${data.items.map(item => `${item.name} × ${item.quantity} - $${(item.price * item.quantity).toFixed(2)}`).join('\n')}
      
      Subtotal: $${data.subtotal.toFixed(2)}
      Shipping: $${data.shipping.toFixed(2)}
      Tax: $${data.tax.toFixed(2)}
      Total: $${data.total.toFixed(2)}
      
      Shipping Address:
      ${data.shippingAddress.name}
      ${data.shippingAddress.address}
      ${data.shippingAddress.city}, ${data.shippingAddress.state} ${data.shippingAddress.zipCode}
      ${data.shippingAddress.country}
      
      Track your order: https://aidevcommerce.com/account/orders
      
      Questions? Contact <NAME_EMAIL>
    `
    
    return { subject, html, text }
  }

  // Welcome email template
  private generateWelcomeTemplate(data: WelcomeEmailData): EmailTemplate {
    const subject = `Welcome to AIDEVCOMMERCE, ${data.customerName}!`
    
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${subject}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #3b82f6; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9fafb; }
            .button { background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 10px 0; }
            .footer { text-align: center; padding: 20px; color: #6b7280; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Welcome to AIDEVCOMMERCE!</h1>
            </div>
            
            <div class="content">
              <h2>Hello ${data.customerName},</h2>
              <p>Welcome to AIDEVCOMMERCE! We're excited to have you join our community of smart shoppers.</p>
              
              <p>Here's what you can do with your new account:</p>
              <ul>
                <li>Browse our extensive catalog of products</li>
                <li>Get AI-powered product recommendations</li>
                <li>Track your orders and delivery status</li>
                <li>Save items to your wishlist</li>
                <li>Access exclusive member deals</li>
              </ul>
              
              <div style="text-align: center;">
                <a href="https://aidevcommerce.com/shop" class="button">Start Shopping</a>
              </div>
            </div>
            
            <div class="footer">
              <p>Questions? Contact <NAME_EMAIL></p>
              <p>© 2024 AIDEVCOMMERCE. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `
    
    const text = `
      Welcome to AIDEVCOMMERCE!
      
      Hello ${data.customerName},
      
      Welcome to AIDEVCOMMERCE! We're excited to have you join our community of smart shoppers.
      
      Here's what you can do with your new account:
      - Browse our extensive catalog of products
      - Get AI-powered product recommendations
      - Track your orders and delivery status
      - Save items to your wishlist
      - Access exclusive member deals
      
      Start shopping: https://aidevcommerce.com/shop
      
      Questions? Contact <NAME_EMAIL>
    `
    
    return { subject, html, text }
  }

  // Password reset email template
  private generatePasswordResetTemplate(data: PasswordResetData): EmailTemplate {
    const subject = 'Reset Your AIDEVCOMMERCE Password'
    
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${subject}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #3b82f6; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9fafb; }
            .button { background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 10px 0; }
            .footer { text-align: center; padding: 20px; color: #6b7280; }
            .warning { background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 6px; margin: 15px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Password Reset Request</h1>
            </div>
            
            <div class="content">
              <h2>Hello ${data.customerName},</h2>
              <p>We received a request to reset your password for your AIDEVCOMMERCE account.</p>
              
              <div style="text-align: center;">
                <a href="${data.resetUrl}" class="button">Reset Password</a>
              </div>
              
              <div class="warning">
                <p><strong>Security Notice:</strong></p>
                <p>This link will expire in 1 hour for security reasons. If you didn't request this password reset, please ignore this email.</p>
              </div>
              
              <p>If the button doesn't work, copy and paste this link into your browser:</p>
              <p style="word-break: break-all; color: #3b82f6;">${data.resetUrl}</p>
            </div>
            
            <div class="footer">
              <p>Questions? Contact <NAME_EMAIL></p>
              <p>© 2024 AIDEVCOMMERCE. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `
    
    const text = `
      Password Reset Request
      
      Hello ${data.customerName},
      
      We received a request to reset your password for your AIDEVCOMMERCE account.
      
      Reset your password: ${data.resetUrl}
      
      Security Notice:
      This link will expire in 1 hour for security reasons. If you didn't request this password reset, please ignore this email.
      
      Questions? Contact <NAME_EMAIL>
    `
    
    return { subject, html, text }
  }

  // Send email function (mock implementation)
  private async sendEmail(to: string, template: EmailTemplate): Promise<boolean> {
    try {
      // In production, you would use a real email service like:
      // - SendGrid
      // - Mailgun
      // - AWS SES
      // - Nodemailer with SMTP
      
      console.log('📧 Email would be sent to:', to)
      console.log('📧 Subject:', template.subject)
      console.log('📧 Content preview:', template.text.substring(0, 100) + '...')
      
      // Simulate email sending delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      return true
    } catch (error) {
      console.error('Failed to send email:', error)
      return false
    }
  }

  // Public methods
  async sendOrderConfirmation(data: OrderConfirmationData): Promise<boolean> {
    const template = this.generateOrderConfirmationTemplate(data)
    return this.sendEmail(data.customerEmail, template)
  }

  async sendWelcomeEmail(data: WelcomeEmailData): Promise<boolean> {
    const template = this.generateWelcomeTemplate(data)
    return this.sendEmail(data.customerEmail, template)
  }

  async sendPasswordReset(data: PasswordResetData): Promise<boolean> {
    const template = this.generatePasswordResetTemplate(data)
    return this.sendEmail(data.customerEmail, template)
  }
}

// Export singleton instance
export const emailService = new EmailService()

// Export types for use in other files
export type { OrderConfirmationData, WelcomeEmailData, PasswordResetData }
