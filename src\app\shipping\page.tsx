"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { formatPrice } from "@/lib/utils"
import {
  Truck,
  Clock,
  MapPin,
  Package,
  Globe,
  Shield,
  Calculator,
  CheckCircle,
  AlertTriangle,
  Plane,
  Ship,
  Zap,
  Calendar,
  DollarSign,
} from "lucide-react"

export default function ShippingPage() {
  const [selectedCountry, setSelectedCountry] = useState("US")
  const [orderValue, setOrderValue] = useState(100)
  const { t } = useLanguage()

  const shippingMethods = [
    {
      id: "standard",
      name: "Standard Shipping",
      description: "Reliable delivery for everyday orders",
      icon: Truck,
      timeframe: "5-7 business days",
      cost: "Free on orders $75+",
      baseCost: 9.99,
      features: ["Tracking included", "Signature not required", "Insurance up to $100"],
      color: "bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400"
    },
    {
      id: "express",
      name: "Express Shipping",
      description: "Faster delivery when you need it sooner",
      icon: Zap,
      timeframe: "2-3 business days",
      cost: "From $19.99",
      baseCost: 19.99,
      features: ["Priority handling", "Tracking included", "Insurance up to $500"],
      color: "bg-orange-100 text-orange-600 dark:bg-orange-900 dark:text-orange-400"
    },
    {
      id: "overnight",
      name: "Overnight Shipping",
      description: "Next business day delivery",
      icon: Plane,
      timeframe: "1 business day",
      cost: "From $39.99",
      baseCost: 39.99,
      features: ["Next day delivery", "Signature required", "Insurance up to $1000"],
      color: "bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400"
    },
    {
      id: "international",
      name: "International Shipping",
      description: "Worldwide delivery to 25+ countries",
      icon: Globe,
      timeframe: "7-14 business days",
      cost: "Calculated at checkout",
      baseCost: 29.99,
      features: ["Customs handling", "Tracking included", "Duties may apply"],
      color: "bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400"
    }
  ]

  const countries = [
    { code: "US", name: "United States", flag: "🇺🇸", zone: "domestic" },
    { code: "CA", name: "Canada", flag: "🇨🇦", zone: "international" },
    { code: "UK", name: "United Kingdom", flag: "🇬🇧", zone: "international" },
    { code: "DE", name: "Germany", flag: "🇩🇪", zone: "international" },
    { code: "FR", name: "France", flag: "🇫🇷", zone: "international" },
    { code: "JP", name: "Japan", flag: "🇯🇵", zone: "international" },
    { code: "AU", name: "Australia", flag: "🇦🇺", zone: "international" },
    { code: "BR", name: "Brazil", flag: "🇧🇷", zone: "international" }
  ]

  const shippingZones = [
    {
      zone: "Zone 1 - Domestic",
      countries: ["United States"],
      timeframe: "1-7 business days",
      startingPrice: "Free on $75+"
    },
    {
      zone: "Zone 2 - North America",
      countries: ["Canada", "Mexico"],
      timeframe: "7-10 business days",
      startingPrice: "From $19.99"
    },
    {
      zone: "Zone 3 - Europe",
      countries: ["UK", "Germany", "France", "Italy", "Spain", "Netherlands"],
      timeframe: "7-14 business days",
      startingPrice: "From $29.99"
    },
    {
      zone: "Zone 4 - Asia Pacific",
      countries: ["Japan", "Australia", "Singapore", "South Korea"],
      timeframe: "10-14 business days",
      startingPrice: "From $34.99"
    },
    {
      zone: "Zone 5 - Rest of World",
      countries: ["Brazil", "India", "South Africa", "Other countries"],
      timeframe: "14-21 business days",
      startingPrice: "From $39.99"
    }
  ]

  const restrictions = [
    {
      category: "Electronics",
      restrictions: ["Lithium batteries require special handling", "Some countries restrict certain electronics"],
      icon: Package
    },
    {
      category: "Liquids & Chemicals",
      restrictions: ["Hazardous materials cannot be shipped internationally", "Cosmetics may have restrictions"],
      icon: AlertTriangle
    },
    {
      category: "Large Items",
      restrictions: ["Oversized items may require freight shipping", "Additional handling fees may apply"],
      icon: Truck
    }
  ]

  const calculateShipping = () => {
    const selectedCountryData = countries.find(c => c.code === selectedCountry)
    if (!selectedCountryData) return 0

    if (selectedCountryData.zone === "domestic") {
      return orderValue >= 75 ? 0 : 9.99
    } else {
      return 29.99 // Base international rate
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-600 to-primary-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Shipping Information
            </h1>
            <p className="text-xl text-primary-100 mb-8">
              Fast, reliable shipping to your door with multiple delivery options and worldwide coverage
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Badge className="bg-white/20 text-white px-4 py-2">
                <Truck className="h-4 w-4 mr-2" />
                Free Shipping $75+
              </Badge>
              <Badge className="bg-white/20 text-white px-4 py-2">
                <Globe className="h-4 w-4 mr-2" />
                25+ Countries
              </Badge>
              <Badge className="bg-white/20 text-white px-4 py-2">
                <Shield className="h-4 w-4 mr-2" />
                Fully Insured
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Shipping Calculator */}
      <section className="py-16 -mt-8 relative z-10">
        <div className="container mx-auto px-4">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-center flex items-center justify-center">
                <Calculator className="h-5 w-5 mr-2" />
                Shipping Calculator
              </CardTitle>
              <CardDescription className="text-center">
                Estimate shipping costs for your order
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Shipping Country
                  </label>
                  <select
                    value={selectedCountry}
                    onChange={(e) => setSelectedCountry(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    {countries.map((country) => (
                      <option key={country.code} value={country.code}>
                        {country.flag} {country.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Order Value
                  </label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      type="number"
                      value={orderValue}
                      onChange={(e) => setOrderValue(Number(e.target.value))}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                      min="0"
                      step="0.01"
                    />
                  </div>
                </div>
              </div>
              
              <div className="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <span className="font-medium text-gray-900 dark:text-white">
                    Estimated Shipping Cost:
                  </span>
                  <span className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                    {calculateShipping() === 0 ? "FREE" : formatPrice(calculateShipping())}
                  </span>
                </div>
                {selectedCountry === "US" && orderValue < 75 && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                    Add {formatPrice(75 - orderValue)} more for free shipping!
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Shipping Methods */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Shipping Options
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Choose the shipping method that best fits your needs and timeline
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {shippingMethods.map((method) => {
              const Icon = method.icon
              return (
                <Card key={method.id} className="h-full">
                  <CardContent className="p-6">
                    <div className={`w-12 h-12 ${method.color} rounded-lg flex items-center justify-center mb-4`}>
                      <Icon className="h-6 w-6" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      {method.name}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                      {method.description}
                    </p>
                    
                    <div className="space-y-3 mb-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Delivery:</span>
                        <Badge variant="outline" className="text-xs">
                          {method.timeframe}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Cost:</span>
                        <span className="font-medium text-gray-900 dark:text-white text-sm">
                          {method.cost}
                        </span>
                      </div>
                    </div>

                    <div className="space-y-2">
                      {method.features.map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <CheckCircle className="h-3 w-3 text-green-600" />
                          <span className="text-xs text-gray-600 dark:text-gray-400">
                            {feature}
                          </span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Shipping Zones */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              International Shipping Zones
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              We ship worldwide with different rates and timeframes based on your location
            </p>
          </div>

          <div className="space-y-6">
            {shippingZones.map((zone, index) => (
              <Card key={index}>
                <CardContent className="p-6">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        {zone.zone}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 text-sm mb-2">
                        {zone.countries.join(", ")}
                      </p>
                    </div>
                    <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-6 mt-4 md:mt-0">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {zone.timeframe}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <DollarSign className="h-4 w-4 text-gray-400" />
                        <span className="font-medium text-gray-900 dark:text-white">
                          {zone.startingPrice}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Shipping Restrictions */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Shipping Restrictions
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Important information about items that may have shipping limitations
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {restrictions.map((restriction, index) => {
              const Icon = restriction.icon
              return (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="w-12 h-12 bg-amber-100 dark:bg-amber-900 rounded-lg flex items-center justify-center mb-4">
                      <Icon className="h-6 w-6 text-amber-600 dark:text-amber-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      {restriction.category}
                    </h3>
                    <ul className="space-y-2">
                      {restriction.restrictions.map((item, idx) => (
                        <li key={idx} className="flex items-start space-x-2">
                          <AlertTriangle className="h-3 w-3 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {item}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Shipping FAQ */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Shipping FAQ
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Common questions about our shipping policies and procedures
            </p>
          </div>

          <div className="max-w-3xl mx-auto space-y-6">
            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  When will my order ship?
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Orders placed before 2 PM EST on business days typically ship the same day. 
                  Orders placed after 2 PM or on weekends ship the next business day.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Can I change my shipping address after placing an order?
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  You can change your shipping address within 1 hour of placing your order by contacting customer service. 
                  After that, we may not be able to modify the address as your order may already be in processing.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Do you ship to P.O. Boxes?
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  We can ship most items to P.O. Boxes using USPS. However, some large items or express shipping 
                  options may require a physical address for delivery.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  What about customs and duties for international orders?
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  International customers are responsible for any customs duties, taxes, or fees imposed by their country. 
                  These charges are not included in our shipping costs and vary by destination.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Questions About Shipping?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Our customer support team is here to help with any shipping questions or concerns
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button className="bg-white text-primary-600 hover:bg-gray-100">
              Contact Support
            </Button>
            <Button variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600">
              Live Chat
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
