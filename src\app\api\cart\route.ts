import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')

    if (!session?.user?.id && !sessionId) {
      return NextResponse.json(
        { error: 'User session or session ID required' },
        { status: 401 }
      )
    }

    const where = session?.user?.id 
      ? { userId: session.user.id }
      : { sessionId }

    const cartItems = await prisma.cartItem.findMany({
      where,
      include: {
        product: {
          include: {
            images: {
              take: 1,
              orderBy: { position: 'asc' }
            },
            category: true
          }
        },
        variant: true
      },
      orderBy: { createdAt: 'desc' }
    })

    // Calculate totals
    const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
    const tax = subtotal * 0.08 // 8% tax
    const shipping = subtotal >= 100 ? 0 : 10 // Free shipping over $100
    const total = subtotal + tax + shipping

    return NextResponse.json({
      items: cartItems,
      summary: {
        itemCount: cartItems.reduce((sum, item) => sum + item.quantity, 0),
        subtotal: Math.round(subtotal * 100) / 100,
        tax: Math.round(tax * 100) / 100,
        shipping: Math.round(shipping * 100) / 100,
        total: Math.round(total * 100) / 100
      }
    })
  } catch (error) {
    console.error('Error fetching cart:', error)
    return NextResponse.json(
      { error: 'Failed to fetch cart' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const body = await request.json()
    
    const { productId, variantId, quantity = 1, sessionId } = body

    if (!session?.user?.id && !sessionId) {
      return NextResponse.json(
        { error: 'User session or session ID required' },
        { status: 401 }
      )
    }

    // Get product details
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        variants: variantId ? {
          where: { id: variantId }
        } : false
      }
    })

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      )
    }

    // Check stock
    const variant = variantId ? product.variants?.[0] : null
    const availableQuantity = variant ? variant.quantity : product.quantity
    const price = variant ? variant.price : product.price

    if (product.trackQuantity && availableQuantity !== null && availableQuantity < quantity) {
      return NextResponse.json(
        { error: 'Insufficient stock' },
        { status: 400 }
      )
    }

    // Check if item already exists in cart
    const existingItem = await prisma.cartItem.findFirst({
      where: {
        ...(session?.user?.id ? { userId: session.user.id } : { sessionId }),
        productId,
        variantId
      }
    })

    if (existingItem) {
      // Update quantity
      const newQuantity = existingItem.quantity + quantity
      
      if (product.trackQuantity && availableQuantity !== null && availableQuantity < newQuantity) {
        return NextResponse.json(
          { error: 'Insufficient stock' },
          { status: 400 }
        )
      }

      const updatedItem = await prisma.cartItem.update({
        where: { id: existingItem.id },
        data: { quantity: newQuantity },
        include: {
          product: {
            include: {
              images: {
                take: 1,
                orderBy: { position: 'asc' }
              }
            }
          },
          variant: true
        }
      })

      return NextResponse.json(updatedItem)
    } else {
      // Create new cart item
      const cartItem = await prisma.cartItem.create({
        data: {
          userId: session?.user?.id || null,
          sessionId: sessionId || null,
          productId,
          variantId,
          quantity,
          price
        },
        include: {
          product: {
            include: {
              images: {
                take: 1,
                orderBy: { position: 'asc' }
              }
            }
          },
          variant: true
        }
      })

      return NextResponse.json(cartItem, { status: 201 })
    }
  } catch (error) {
    console.error('Error adding to cart:', error)
    return NextResponse.json(
      { error: 'Failed to add to cart' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')

    if (!session?.user?.id && !sessionId) {
      return NextResponse.json(
        { error: 'User session or session ID required' },
        { status: 401 }
      )
    }

    const where = session?.user?.id 
      ? { userId: session.user.id }
      : { sessionId }

    await prisma.cartItem.deleteMany({ where })

    return NextResponse.json({ message: 'Cart cleared successfully' })
  } catch (error) {
    console.error('Error clearing cart:', error)
    return NextResponse.json(
      { error: 'Failed to clear cart' },
      { status: 500 }
    )
  }
}
