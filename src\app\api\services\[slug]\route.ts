import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params

    const service = await prisma.service.findUnique({
      where: { slug },
      include: {
        category: true
      }
    })

    if (!service) {
      return NextResponse.json(
        { error: 'Service not found' },
        { status: 404 }
      )
    }

    // Get related services from the same category
    const relatedServices = await prisma.service.findMany({
      where: {
        categoryId: service.categoryId,
        id: { not: service.id },
        status: 'ACTIVE'
      },
      include: {
        category: true
      },
      take: 4
    })

    const serviceWithRelated = {
      ...service,
      relatedServices
    }

    return NextResponse.json(serviceWithRelated)
  } catch (error) {
    console.error('Error fetching service:', error)
    return NextResponse.json(
      { error: 'Failed to fetch service' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params
    const body = await request.json()

    const existingService = await prisma.service.findUnique({
      where: { slug }
    })

    if (!existingService) {
      return NextResponse.json(
        { error: 'Service not found' },
        { status: 404 }
      )
    }

    const {
      name,
      description,
      shortDescription,
      price,
      duration,
      categoryId,
      features,
      images,
      status,
      featured,
      seoTitle,
      seoDescription
    } = body

    const updatedService = await prisma.service.update({
      where: { slug },
      data: {
        name,
        description,
        shortDescription,
        price: price ? parseFloat(price) : null,
        duration,
        categoryId,
        features,
        images,
        status,
        featured,
        seoTitle,
        seoDescription
      },
      include: {
        category: true
      }
    })

    return NextResponse.json(updatedService)
  } catch (error) {
    console.error('Error updating service:', error)
    return NextResponse.json(
      { error: 'Failed to update service' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params

    const existingService = await prisma.service.findUnique({
      where: { slug }
    })

    if (!existingService) {
      return NextResponse.json(
        { error: 'Service not found' },
        { status: 404 }
      )
    }

    await prisma.service.delete({
      where: { slug }
    })

    return NextResponse.json({ message: 'Service deleted successfully' })
  } catch (error) {
    console.error('Error deleting service:', error)
    return NextResponse.json(
      { error: 'Failed to delete service' },
      { status: 500 }
    )
  }
}
