"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useCartStore } from "@/store/cart-store"
import { useWishlistStore } from "@/store/wishlist-store"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { useFeaturedProducts } from "@/hooks/use-products"
import { formatPrice } from "@/lib/utils"
import {
  ShoppingCart,
  Heart,
  Star,
  ArrowRight,
  Zap,
  TrendingUp,
  ChevronLeft,
  ChevronRight,
  Loader2,
} from "lucide-react"

const categories = [
  { id: "all", name: "All Products", icon: "🛍️" },
  { id: "Electronics", name: "Electronics", icon: "📱" },
  { id: "Clothing", name: "Clothing", icon: "👕" },
  { id: "Home & Garden", name: "Home & Garden", icon: "🏡" },
  { id: "Sports & Outdoors", name: "Sports & Outdoors", icon: "⚽" },
]

export function FeaturedProductsSection() {
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [currentPage, setCurrentPage] = useState(0)
  const { addItem: addToCart, openCart } = useCartStore()
  const { toggleItem: toggleWishlistItem, isInWishlist } = useWishlistStore()
  const { t } = useLanguage()
  const { toast } = useToast()

  // Fetch featured products from API
  const { data: productsData, isLoading, error } = useFeaturedProducts(8)
  const products = productsData?.products || []

  const filteredProducts = products.filter(
    product => selectedCategory === "all" || product.category?.name === selectedCategory
  )

  const productsPerPage = 4
  const totalPages = Math.ceil(filteredProducts.length / productsPerPage)
  const currentProducts = filteredProducts.slice(
    currentPage * productsPerPage,
    (currentPage + 1) * productsPerPage
  )

  const handleAddToCart = (product: any) => {
    addToCart({
      productId: product.id,
      name: product.name,
      slug: product.slug,
      price: product.price,
      image: product.images?.[0]?.url || "/placeholder-product.jpg",
      inStock: product.quantity > 0,
      maxQuantity: product.quantity || 10,
    })

    toast({
      title: "Added to Cart",
      description: `${product.name} has been added to your cart`,
    })

    setTimeout(() => openCart(), 100)
  }

  const handleToggleWishlist = (product: any) => {
    const wasAdded = toggleWishlistItem({
      productId: product.id,
      name: product.name,
      slug: product.slug,
      price: product.price,
      comparePrice: product.comparePrice,
      image: product.images?.[0]?.url || "/placeholder-product.jpg",
      inStock: product.quantity > 0,
      category: product.category?.name || "",
      rating: product.averageRating || 0,
    })

    toast({
      title: wasAdded ? "Added to Wishlist" : "Removed from Wishlist",
      description: `${product.name} has been ${wasAdded ? 'added to' : 'removed from'} your wishlist`,
    })
  }

  const getBadgeColor = (badge: string) => {
    switch (badge.toLowerCase()) {
      case "best seller": return "bg-green-500"
      case "new": return "bg-blue-500"
      case "sale": return "bg-red-500"
      case "premium": return "bg-purple-500"
      case "popular": return "bg-orange-500"
      case "trending": return "bg-pink-500"
      default: return "bg-gray-500"
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <Badge variant="secondary" className="mb-4">
              <TrendingUp className="h-4 w-4 mr-2" />
              Featured Products
            </Badge>
            <h2 className="text-3xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                Discover Amazing Products
              </span>
            </h2>
          </div>
          <div className="flex justify-center items-center py-20">
            <Loader2 className="h-8 w-8 animate-spin text-primary-600" />
            <span className="ml-2 text-lg">Loading featured products...</span>
          </div>
        </div>
      </section>
    )
  }

  // Error state
  if (error) {
    return (
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <Badge variant="secondary" className="mb-4">
              <TrendingUp className="h-4 w-4 mr-2" />
              Featured Products
            </Badge>
            <h2 className="text-3xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                Discover Amazing Products
              </span>
            </h2>
          </div>
          <div className="text-center py-20">
            <p className="text-red-600 dark:text-red-400 text-lg">
              Failed to load featured products. Please try again later.
            </p>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-800">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <Badge variant="secondary" className="mb-4">
            <TrendingUp className="h-4 w-4 mr-2" />
            Featured Products
          </Badge>
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
              Discover Amazing Products
            </span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Handpicked selection of our best products with cutting-edge features and exceptional quality.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => {
                setSelectedCategory(category.id)
                setCurrentPage(0)
              }}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                selectedCategory === category.id
                  ? "bg-primary-600 text-white shadow-lg scale-105"
                  : "bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary-50 dark:hover:bg-gray-600 shadow-md"
              }`}
            >
              <span className="mr-2">{category.icon}</span>
              {category.name}
            </button>
          ))}
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {currentProducts.map((product) => (
            <Card key={product.id} className="group hover:shadow-2xl transition-all duration-300 border-0 bg-white dark:bg-gray-900 overflow-hidden">
              <div className="relative overflow-hidden">
                {/* Product Image */}
                <div className="aspect-square bg-gray-100 dark:bg-gray-800 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                  <div className="text-6xl">📦</div>
                </div>
                
                {/* Overlay Actions */}
                <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-2">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => handleToggleWishlist(product)}
                  >
                    <Heart className={`h-4 w-4 ${
                      isInWishlist(product.id) ? 'fill-current text-red-500' : ''
                    }`} />
                  </Button>
                  <Link href={`/product/${product.slug}`}>
                    <Button size="sm">Quick View</Button>
                  </Link>
                </div>

                {/* Badge */}
                {product.featured && (
                  <div className="absolute top-3 left-3">
                    <span className="bg-primary-600 text-white px-3 py-1 text-xs font-medium rounded-full">
                      Featured
                    </span>
                  </div>
                )}
                {product.comparePrice && product.comparePrice > product.price && (
                  <div className="absolute top-3 left-3">
                    <span className="bg-red-500 text-white px-3 py-1 text-xs font-medium rounded-full">
                      Sale
                    </span>
                  </div>
                )}

                {/* Wishlist Button */}
                <button
                  onClick={() => handleToggleWishlist(product)}
                  className="absolute top-3 right-3 p-2 bg-white dark:bg-gray-800 rounded-full shadow-md hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                >
                  <Heart className={`h-4 w-4 ${
                    isInWishlist(product.id) ? 'fill-current text-red-500' : 'text-gray-400'
                  }`} />
                </button>
              </div>

              <CardContent className="p-6">
                <div className="space-y-3">
                  {/* Product Name */}
                  <h3 className="font-semibold text-lg line-clamp-2 group-hover:text-primary-600 transition-colors">
                    <Link href={`/product/${product.slug}`}>
                      {product.name}
                    </Link>
                  </h3>
                  
                  {/* Description */}
                  <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                    {product.shortDescription || product.description}
                  </p>

                  {/* Rating */}
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${
                            i < Math.floor(product.averageRating || 0)
                              ? "text-yellow-400 fill-current"
                              : "text-gray-300"
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-gray-500">
                      {product.rating} ({product.reviewCount || 0})
                    </span>
                  </div>

                  {/* Price */}
                  <div className="flex items-center space-x-2">
                    <span className="font-bold text-xl text-gray-900 dark:text-white">
                      {formatPrice(product.price)}
                    </span>
                    {product.comparePrice && (
                      <span className="text-sm text-gray-500 line-through">
                        {formatPrice(product.comparePrice)}
                      </span>
                    )}
                  </div>

                  {/* Add to Cart Button */}
                  <Button
                    className="w-full group"
                    onClick={() => handleAddToCart(product)}
                    disabled={!product.trackQuantity || (product.quantity !== null && product.quantity <= 0)}
                  >
                    <ShoppingCart className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
                    {(!product.trackQuantity || (product.quantity !== null && product.quantity > 0)) ? "Add to Cart" : "Out of Stock"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-center space-x-4 mb-12">
            <Button
              variant="outline"
              onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
              disabled={currentPage === 0}
            >
              <ChevronLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            
            <div className="flex space-x-2">
              {[...Array(totalPages)].map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentPage(index)}
                  className={`w-10 h-10 rounded-full font-medium transition-colors ${
                    index === currentPage
                      ? "bg-primary-600 text-white"
                      : "bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary-50 dark:hover:bg-gray-600"
                  }`}
                >
                  {index + 1}
                </button>
              ))}
            </div>
            
            <Button
              variant="outline"
              onClick={() => setCurrentPage(Math.min(totalPages - 1, currentPage + 1))}
              disabled={currentPage === totalPages - 1}
            >
              Next
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        )}

        {/* CTA Section */}
        <div className="text-center">
          <Link href="/shop">
            <Button size="lg" className="group px-8 py-4 text-lg font-semibold">
              <Zap className="h-5 w-5 mr-2" />
              Explore All Products
              <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}
