"use client"

import React, { createContext, useContext, useState, useEffect } from "react"
import { useToast } from "@/hooks/use-toast"

export interface WishlistItem {
  id: string
  name: string
  price: number
  originalPrice?: number
  image: string
  category?: string
  brand?: string
  rating?: number
  reviews?: number
  inStock?: boolean
  addedAt: Date
}

interface WishlistContextType {
  items: WishlistItem[]
  totalItems: number
  isLoading: boolean
  addToWishlist: (item: Omit<WishlistItem, "addedAt">) => void
  removeFromWishlist: (itemId: string) => void
  clearWishlist: () => void
  isInWishlist: (itemId: string) => boolean
  toggleWishlist: (item: Omit<WishlistItem, "addedAt">) => void
}

const WishlistContext = createContext<WishlistContextType | undefined>(undefined)

export function WishlistProvider({ children }: { children: React.ReactNode }) {
  const [items, setItems] = useState<WishlistItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  // Load wishlist from localStorage on mount
  useEffect(() => {
    try {
      const savedWishlist = localStorage.getItem("aidevcommerce_wishlist")
      if (savedWishlist) {
        const wishlistData = JSON.parse(savedWishlist)
        // Convert addedAt strings back to Date objects
        const itemsWithDates = wishlistData.map((item: any) => ({
          ...item,
          addedAt: new Date(item.addedAt)
        }))
        setItems(itemsWithDates)
      }
    } catch (error) {
      console.error("Error loading wishlist:", error)
      localStorage.removeItem("aidevcommerce_wishlist")
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Save wishlist to localStorage whenever items change
  useEffect(() => {
    if (!isLoading) {
      localStorage.setItem("aidevcommerce_wishlist", JSON.stringify(items))
    }
  }, [items, isLoading])

  const addToWishlist = (newItem: Omit<WishlistItem, "addedAt">) => {
    const itemWithDate: WishlistItem = {
      ...newItem,
      addedAt: new Date()
    }

    setItems(prevItems => {
      // Check if item already exists
      const existingItem = prevItems.find(item => item.id === newItem.id)
      if (existingItem) {
        return prevItems // Don't add duplicates
      }
      return [itemWithDate, ...prevItems] // Add to beginning of array
    })

    toast({
      title: "Added to Wishlist",
      description: `${newItem.name} has been added to your wishlist.`
    })
  }

  const removeFromWishlist = (itemId: string) => {
    const itemToRemove = items.find(item => item.id === itemId)
    
    setItems(prevItems => prevItems.filter(item => item.id !== itemId))
    
    if (itemToRemove) {
      toast({
        title: "Removed from Wishlist",
        description: `${itemToRemove.name} has been removed from your wishlist.`
      })
    }
  }

  const clearWishlist = () => {
    setItems([])
    toast({
      title: "Wishlist Cleared",
      description: "All items have been removed from your wishlist."
    })
  }

  const isInWishlist = (itemId: string): boolean => {
    return items.some(item => item.id === itemId)
  }

  const toggleWishlist = (item: Omit<WishlistItem, "addedAt">) => {
    if (isInWishlist(item.id)) {
      removeFromWishlist(item.id)
    } else {
      addToWishlist(item)
    }
  }

  const totalItems = items.length

  const value: WishlistContextType = {
    items,
    totalItems,
    isLoading,
    addToWishlist,
    removeFromWishlist,
    clearWishlist,
    isInWishlist,
    toggleWishlist
  }

  return (
    <WishlistContext.Provider value={value}>
      {children}
    </WishlistContext.Provider>
  )
}

export function useWishlist() {
  const context = useContext(WishlistContext)
  if (context === undefined) {
    throw new Error("useWishlist must be used within a WishlistProvider")
  }
  return context
}
