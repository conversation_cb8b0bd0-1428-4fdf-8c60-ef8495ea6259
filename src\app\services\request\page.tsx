"use client"

import { Suspense } from 'react'
import { ServiceRequestForm } from '@/components/services/service-request-form'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useLanguage } from '@/components/providers/language-provider'
import {
  Globe,
  Shield,
  Clock,
  Users,
  CheckCircle,
  Star,
  Award,
  Truck,
  Package,
  FileCheck,
} from 'lucide-react'

const trustIndicators = [
  {
    icon: Shield,
    title: 'Verified & Trusted',
    titleAr: 'موثق وموثوق',
    description: '500+ verified suppliers',
    descriptionAr: '500+ مورد موثق',
  },
  {
    icon: Clock,
    title: '24/7 Support',
    titleAr: 'دعم 24/7',
    description: 'Round-the-clock assistance',
    descriptionAr: 'مساعدة على مدار الساعة',
  },
  {
    icon: Users,
    title: 'Expert Team',
    titleAr: 'فريق خبراء',
    description: '15+ years experience',
    descriptionAr: '15+ سنة خبرة',
  },
  {
    icon: Award,
    title: 'Quality Guaranteed',
    titleAr: 'جودة مضمونة',
    description: '99% customer satisfaction',
    descriptionAr: '99% رضا العملاء',
  },
]

const serviceStats = [
  {
    number: '2,500+',
    label: 'Services Completed',
    labelAr: 'خدمة مكتملة',
  },
  {
    number: '50+',
    label: 'Countries Served',
    labelAr: 'دولة نخدمها',
  },
  {
    number: '98%',
    label: 'Success Rate',
    labelAr: 'معدل النجاح',
  },
  {
    number: '24h',
    label: 'Response Time',
    labelAr: 'وقت الاستجابة',
  },
]

function ServiceRequestPageContent() {
  const { language } = useLanguage()
  const isArabic = language === 'ar'

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <section className="bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 text-white py-16">
        <div className="container mx-auto px-4">
          <div className={`text-center max-w-4xl mx-auto ${isArabic ? 'rtl' : 'ltr'}`}>
            <Badge className="mb-6 bg-yellow-500 text-black">
              <Globe className="h-4 w-4 mr-2" />
              {isArabic ? 'طلب خدمة احترافية' : 'Professional Service Request'}
            </Badge>
            
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {isArabic 
                ? 'احصل على الخدمة التي تحتاجها'
                : 'Get the Service You Need'
              }
            </h1>
            
            <p className="text-xl text-blue-100 mb-8">
              {isArabic
                ? 'فريقنا من الخبراء جاهز لمساعدتك في جميع احتياجاتك التجارية في الصين'
                : 'Our team of experts is ready to help you with all your business needs in China'
              }
            </p>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12">
              {serviceStats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-yellow-400 mb-1">
                    {stat.number}
                  </div>
                  <div className="text-sm text-blue-100">
                    {isArabic ? stat.labelAr : stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Service Request Form */}
            <div className="lg:col-span-3">
              <Suspense fallback={
                <Card>
                  <CardContent className="p-12 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p>{isArabic ? 'جاري التحميل...' : 'Loading...'}</p>
                  </CardContent>
                </Card>
              }>
                <ServiceRequestForm />
              </Suspense>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Trust Indicators */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="font-semibold text-lg mb-4">
                    {isArabic ? 'لماذا تختارنا؟' : 'Why Choose Us?'}
                  </h3>
                  <div className="space-y-4">
                    {trustIndicators.map((indicator, index) => {
                      const Icon = indicator.icon
                      return (
                        <div key={index} className="flex items-start space-x-3">
                          <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg flex-shrink-0">
                            <Icon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div>
                            <h4 className="font-medium text-sm">
                              {isArabic ? indicator.titleAr : indicator.title}
                            </h4>
                            <p className="text-xs text-gray-600 dark:text-gray-400">
                              {isArabic ? indicator.descriptionAr : indicator.description}
                            </p>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>

              {/* Service Types Quick Links */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="font-semibold text-lg mb-4">
                    {isArabic ? 'خدمات شائعة' : 'Popular Services'}
                  </h3>
                  <div className="space-y-3">
                    <button className="w-full text-left p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <div className="flex items-center space-x-3">
                        <Package className="h-4 w-4 text-blue-600" />
                        <span className="text-sm">
                          {isArabic ? 'فحص المنتجات' : 'Product Inspection'}
                        </span>
                      </div>
                    </button>
                    
                    <button className="w-full text-left p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <div className="flex items-center space-x-3">
                        <Truck className="h-4 w-4 text-blue-600" />
                        <span className="text-sm">
                          {isArabic ? 'الشحن الدولي' : 'International Shipping'}
                        </span>
                      </div>
                    </button>
                    
                    <button className="w-full text-left p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <div className="flex items-center space-x-3">
                        <FileCheck className="h-4 w-4 text-blue-600" />
                        <span className="text-sm">
                          {isArabic ? 'شهادة المنتج' : 'Product Certification'}
                        </span>
                      </div>
                    </button>
                  </div>
                </CardContent>
              </Card>

              {/* Customer Reviews */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="font-semibold text-lg mb-4">
                    {isArabic ? 'آراء العملاء' : 'Customer Reviews'}
                  </h3>
                  <div className="space-y-4">
                    <div className="border-l-4 border-blue-500 pl-4">
                      <div className="flex items-center space-x-1 mb-2">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className="h-3 w-3 text-yellow-400 fill-current" />
                        ))}
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {isArabic 
                          ? '"خدمة ممتازة وسريعة. فريق محترف جداً"'
                          : '"Excellent and fast service. Very professional team"'
                        }
                      </p>
                      <p className="text-xs text-gray-500">
                        - {isArabic ? 'أحمد حسن، دبي' : 'Ahmed Hassan, Dubai'}
                      </p>
                    </div>
                    
                    <div className="border-l-4 border-green-500 pl-4">
                      <div className="flex items-center space-x-1 mb-2">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className="h-3 w-3 text-yellow-400 fill-current" />
                        ))}
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {isArabic 
                          ? '"ساعدوني في الحصول على أفضل الأسعار"'
                          : '"They helped me get the best prices"'
                        }
                      </p>
                      <p className="text-xs text-gray-500">
                        - {isArabic ? 'عمر سالم، الرياض' : 'Omar Salem, Riyadh'}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Contact Info */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="font-semibold text-lg mb-4">
                    {isArabic ? 'تواصل مباشر' : 'Direct Contact'}
                  </h3>
                  <div className="space-y-3 text-sm">
                    <div>
                      <span className="font-medium">
                        {isArabic ? 'هاتف:' : 'Phone:'}
                      </span>
                      <p className="text-blue-600">+86-20-1234-5678</p>
                    </div>
                    <div>
                      <span className="font-medium">
                        {isArabic ? 'واتساب:' : 'WhatsApp:'}
                      </span>
                      <p className="text-green-600">+86-138-1234-5678</p>
                    </div>
                    <div>
                      <span className="font-medium">
                        {isArabic ? 'إيميل:' : 'Email:'}
                      </span>
                      <p className="text-blue-600"><EMAIL></p>
                    </div>
                    <div>
                      <span className="font-medium">
                        {isArabic ? 'ساعات العمل:' : 'Working Hours:'}
                      </span>
                      <p className="text-gray-600">
                        {isArabic ? 'الاثنين - الجمعة: 9:00 - 18:00 (توقيت بكين)' : 'Mon - Fri: 9:00 - 18:00 (Beijing Time)'}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default function ServiceRequestPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading service request form...</p>
        </div>
      </div>
    }>
      <ServiceRequestPageContent />
    </Suspense>
  )
}
