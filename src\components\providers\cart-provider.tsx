"use client"

import React, { createContext, useContext, useState, useEffect } from "react"
import { useToast } from "@/hooks/use-toast"

export interface CartItem {
  id: string
  name: string
  price: number
  originalPrice?: number
  image: string
  quantity: number
  variant?: {
    size?: string
    color?: string
    style?: string
  }
  category?: string
  brand?: string
}

interface CartContextType {
  items: CartItem[]
  totalItems: number
  totalPrice: number
  isLoading: boolean
  addToCart: (item: Omit<CartItem, "quantity"> & { quantity?: number }) => void
  removeFromCart: (itemId: string) => void
  updateQuantity: (itemId: string, quantity: number) => void
  clearCart: () => void
  getItemQuantity: (itemId: string) => number
  isInCart: (itemId: string) => boolean
}

const CartContext = createContext<CartContextType | undefined>(undefined)

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [items, setItems] = useState<CartItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  // Load cart from localStorage on mount
  useEffect(() => {
    try {
      const savedCart = localStorage.getItem("aidevcommerce_cart")
      if (savedCart) {
        const cartData = JSON.parse(savedCart)
        setItems(cartData)
      }
    } catch (error) {
      console.error("Error loading cart:", error)
      localStorage.removeItem("aidevcommerce_cart")
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Save cart to localStorage whenever items change
  useEffect(() => {
    if (!isLoading) {
      localStorage.setItem("aidevcommerce_cart", JSON.stringify(items))
    }
  }, [items, isLoading])

  const addToCart = (newItem: Omit<CartItem, "quantity"> & { quantity?: number }) => {
    const quantity = newItem.quantity || 1
    
    setItems(prevItems => {
      const existingItemIndex = prevItems.findIndex(item => 
        item.id === newItem.id && 
        JSON.stringify(item.variant) === JSON.stringify(newItem.variant)
      )

      if (existingItemIndex > -1) {
        // Update existing item quantity
        const updatedItems = [...prevItems]
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: updatedItems[existingItemIndex].quantity + quantity
        }
        return updatedItems
      } else {
        // Add new item
        return [...prevItems, { ...newItem, quantity }]
      }
    })

    toast({
      title: "Added to Cart",
      description: `${newItem.name} has been added to your cart.`
    })
  }

  const removeFromCart = (itemId: string) => {
    const itemToRemove = items.find(item => item.id === itemId)
    
    setItems(prevItems => prevItems.filter(item => item.id !== itemId))
    
    if (itemToRemove) {
      toast({
        title: "Removed from Cart",
        description: `${itemToRemove.name} has been removed from your cart.`
      })
    }
  }

  const updateQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(itemId)
      return
    }

    setItems(prevItems =>
      prevItems.map(item =>
        item.id === itemId ? { ...item, quantity } : item
      )
    )
  }

  const clearCart = () => {
    setItems([])
    toast({
      title: "Cart Cleared",
      description: "All items have been removed from your cart."
    })
  }

  const getItemQuantity = (itemId: string): number => {
    const item = items.find(item => item.id === itemId)
    return item ? item.quantity : 0
  }

  const isInCart = (itemId: string): boolean => {
    return items.some(item => item.id === itemId)
  }

  // Calculate totals
  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0)
  const totalPrice = items.reduce((sum, item) => sum + (item.price * item.quantity), 0)

  const value: CartContextType = {
    items,
    totalItems,
    totalPrice,
    isLoading,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    getItemQuantity,
    isInCart
  }

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  )
}

export function useCart() {
  const context = useContext(CartContext)
  if (context === undefined) {
    throw new Error("useCart must be used within a CartProvider")
  }
  return context
}
