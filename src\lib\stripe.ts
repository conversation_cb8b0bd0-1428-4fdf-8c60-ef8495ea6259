import Stripe from 'stripe'
import { loadStripe } from '@stripe/stripe-js'

// Server-side Stripe instance
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia',
  typescript: true,
})

// Client-side Stripe instance
export const getStripe = () => {
  return loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)
}

// Types for Stripe integration
export interface PaymentIntentData {
  amount: number
  currency: string
  metadata?: Record<string, string>
  customer?: string
  description?: string
  shipping?: {
    name: string
    address: {
      line1: string
      line2?: string
      city: string
      state: string
      postal_code: string
      country: string
    }
  }
}

export interface CreatePaymentIntentResponse {
  clientSecret: string
  paymentIntentId: string
}

// Helper function to create payment intent
export async function createPaymentIntent(data: PaymentIntentData): Promise<CreatePaymentIntentResponse> {
  try {
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(data.amount * 100), // Convert to cents
      currency: data.currency,
      metadata: data.metadata || {},
      customer: data.customer,
      description: data.description,
      shipping: data.shipping,
      automatic_payment_methods: {
        enabled: true,
      },
    })

    return {
      clientSecret: paymentIntent.client_secret!,
      paymentIntentId: paymentIntent.id,
    }
  } catch (error) {
    console.error('Error creating payment intent:', error)
    throw new Error('Failed to create payment intent')
  }
}

// Helper function to confirm payment
export async function confirmPayment(paymentIntentId: string) {
  try {
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId)
    return paymentIntent
  } catch (error) {
    console.error('Error confirming payment:', error)
    throw new Error('Failed to confirm payment')
  }
}

// Helper function to create customer
export async function createStripeCustomer(data: {
  email: string
  name?: string
  phone?: string
  address?: {
    line1: string
    line2?: string
    city: string
    state: string
    postal_code: string
    country: string
  }
}) {
  try {
    const customer = await stripe.customers.create({
      email: data.email,
      name: data.name,
      phone: data.phone,
      address: data.address,
    })

    return customer
  } catch (error) {
    console.error('Error creating customer:', error)
    throw new Error('Failed to create customer')
  }
}

// Helper function to format amount for display
export function formatStripeAmount(amount: number, currency: string = 'usd'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase(),
  }).format(amount / 100)
}

// Helper function to calculate application fee (for marketplace scenarios)
export function calculateApplicationFee(amount: number, feePercentage: number = 2.9): number {
  return Math.round(amount * (feePercentage / 100))
}

// Webhook signature verification
export function verifyWebhookSignature(payload: string, signature: string): Stripe.Event {
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!
  
  try {
    return stripe.webhooks.constructEvent(payload, signature, webhookSecret)
  } catch (error) {
    console.error('Webhook signature verification failed:', error)
    throw new Error('Invalid webhook signature')
  }
}

// Product and price management
export async function createStripeProduct(data: {
  name: string
  description?: string
  images?: string[]
  metadata?: Record<string, string>
}) {
  try {
    const product = await stripe.products.create({
      name: data.name,
      description: data.description,
      images: data.images,
      metadata: data.metadata,
    })

    return product
  } catch (error) {
    console.error('Error creating product:', error)
    throw new Error('Failed to create product')
  }
}

export async function createStripePrice(data: {
  product: string
  unit_amount: number
  currency: string
  recurring?: {
    interval: 'day' | 'week' | 'month' | 'year'
    interval_count?: number
  }
}) {
  try {
    const price = await stripe.prices.create({
      product: data.product,
      unit_amount: data.unit_amount,
      currency: data.currency,
      recurring: data.recurring,
    })

    return price
  } catch (error) {
    console.error('Error creating price:', error)
    throw new Error('Failed to create price')
  }
}

// Subscription management (for future use)
export async function createSubscription(data: {
  customer: string
  items: Array<{ price: string; quantity?: number }>
  trial_period_days?: number
  metadata?: Record<string, string>
}) {
  try {
    const subscription = await stripe.subscriptions.create({
      customer: data.customer,
      items: data.items,
      trial_period_days: data.trial_period_days,
      metadata: data.metadata,
      payment_behavior: 'default_incomplete',
      payment_settings: { save_default_payment_method: 'on_subscription' },
      expand: ['latest_invoice.payment_intent'],
    })

    return subscription
  } catch (error) {
    console.error('Error creating subscription:', error)
    throw new Error('Failed to create subscription')
  }
}

// Refund management
export async function createRefund(data: {
  payment_intent: string
  amount?: number
  reason?: 'duplicate' | 'fraudulent' | 'requested_by_customer'
  metadata?: Record<string, string>
}) {
  try {
    const refund = await stripe.refunds.create({
      payment_intent: data.payment_intent,
      amount: data.amount,
      reason: data.reason,
      metadata: data.metadata,
    })

    return refund
  } catch (error) {
    console.error('Error creating refund:', error)
    throw new Error('Failed to create refund')
  }
}

// Payment method management
export async function attachPaymentMethod(paymentMethodId: string, customerId: string) {
  try {
    const paymentMethod = await stripe.paymentMethods.attach(paymentMethodId, {
      customer: customerId,
    })

    return paymentMethod
  } catch (error) {
    console.error('Error attaching payment method:', error)
    throw new Error('Failed to attach payment method')
  }
}

export async function detachPaymentMethod(paymentMethodId: string) {
  try {
    const paymentMethod = await stripe.paymentMethods.detach(paymentMethodId)
    return paymentMethod
  } catch (error) {
    console.error('Error detaching payment method:', error)
    throw new Error('Failed to detach payment method')
  }
}

// Error handling helper
export function handleStripeError(error: any): string {
  if (error.type === 'StripeCardError') {
    return error.message
  } else if (error.type === 'StripeRateLimitError') {
    return 'Too many requests made to the API too quickly'
  } else if (error.type === 'StripeInvalidRequestError') {
    return 'Invalid parameters were supplied to Stripe\'s API'
  } else if (error.type === 'StripeAPIError') {
    return 'An error occurred internally with Stripe\'s API'
  } else if (error.type === 'StripeConnectionError') {
    return 'Some kind of error occurred during the HTTPS communication'
  } else if (error.type === 'StripeAuthenticationError') {
    return 'You probably used an incorrect API key'
  } else {
    return 'An unknown error occurred'
  }
}
