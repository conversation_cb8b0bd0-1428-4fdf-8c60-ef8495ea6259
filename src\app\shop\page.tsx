"use client"

import { useState } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { useCartStore } from "@/store/cart-store"
import { useWishlistStore } from "@/store/wishlist-store"
import { useProducts } from "@/hooks/use-products"
import { formatPrice } from "@/lib/utils"
import {
  Search,
  Filter,
  Grid3X3,
  List,
  Heart,
  ShoppingCart,
  Star,
  ChevronDown,
  SlidersHorizontal,
} from "lucide-react"

// Mock product data
const mockProducts = [
  {
    id: "1",
    name: "Premium Wireless Headphones",
    slug: "premium-wireless-headphones",
    price: 199.99,
    comparePrice: 249.99,
    rating: 4.8,
    reviewCount: 124,
    image: "/placeholder-product.jpg",
    category: "Electronics",
    inStock: true,
    featured: true,
    description: "High-quality wireless headphones with noise cancellation",
  },
  {
    id: "2",
    name: "Smart Fitness Watch",
    slug: "smart-fitness-watch",
    price: 299.99,
    comparePrice: null,
    rating: 4.6,
    reviewCount: 89,
    image: "/placeholder-product.jpg",
    category: "Electronics",
    inStock: true,
    featured: false,
    description: "Advanced fitness tracking with heart rate monitoring",
  },
  {
    id: "3",
    name: "Organic Cotton T-Shirt",
    slug: "organic-cotton-t-shirt",
    price: 29.99,
    comparePrice: 39.99,
    rating: 4.4,
    reviewCount: 67,
    image: "/placeholder-product.jpg",
    category: "Clothing",
    inStock: true,
    featured: false,
    description: "Comfortable organic cotton t-shirt in various colors",
  },
  {
    id: "4",
    name: "Professional Camera Lens",
    slug: "professional-camera-lens",
    price: 899.99,
    comparePrice: null,
    rating: 4.9,
    reviewCount: 45,
    image: "/placeholder-product.jpg",
    category: "Photography",
    inStock: false,
    featured: true,
    description: "Professional grade camera lens for stunning photography",
  },
  {
    id: "5",
    name: "Ergonomic Office Chair",
    slug: "ergonomic-office-chair",
    price: 449.99,
    comparePrice: 599.99,
    rating: 4.7,
    reviewCount: 156,
    image: "/placeholder-product.jpg",
    category: "Furniture",
    inStock: true,
    featured: false,
    description: "Comfortable ergonomic chair for long work sessions",
  },
  {
    id: "6",
    name: "Wireless Charging Pad",
    slug: "wireless-charging-pad",
    price: 49.99,
    comparePrice: null,
    rating: 4.3,
    reviewCount: 203,
    image: "/placeholder-product.jpg",
    category: "Electronics",
    inStock: true,
    featured: false,
    description: "Fast wireless charging for compatible devices",
  },
]

const categories = [
  "All Categories",
  "Electronics",
  "Clothing",
  "Photography",
  "Furniture",
  "Home & Garden",
  "Sports & Outdoors",
]

const sortOptions = [
  { value: "featured", label: "Featured" },
  { value: "price-low", label: "Price: Low to High" },
  { value: "price-high", label: "Price: High to Low" },
  { value: "rating", label: "Highest Rated" },
  { value: "newest", label: "Newest" },
]

export default function ShopPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All Categories")
  const [sortBy, setSortBy] = useState("featured")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [showFilters, setShowFilters] = useState(false)
  const [priceRange, setPriceRange] = useState({ min: 0, max: 1000 })
  const [selectedRating, setSelectedRating] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)

  const { t } = useLanguage()
  const { toast } = useToast()
  const { addItem, openCart } = useCartStore()
  const { toggleItem: toggleWishlistItem, isInWishlist } = useWishlistStore()

  // Fetch products with filters
  const { data: productsData, isLoading, error } = useProducts({
    page: currentPage,
    limit: 12,
    search: searchQuery || undefined,
    category: selectedCategory !== "All Categories" ? selectedCategory : undefined,
    sortBy: sortBy === "featured" ? "createdAt" : sortBy,
    sortOrder: sortBy === "price-high" ? "desc" : "asc"
  })

  const products = productsData?.products || []
  const pagination = productsData?.pagination

  const handleAddToCart = (product: any) => {
    addItem({
      id: product.id,
      productId: product.id,
      name: product.name,
      slug: product.slug,
      price: product.price,
      image: product.images?.[0]?.url || '/placeholder-product.jpg',
      inStock: product.inStock !== false,
      maxQuantity: 10, // Default max quantity
    })

    toast({
      title: "Added to Cart",
      description: `${product.name} has been added to your cart`,
    })

    // Optionally open cart sidebar
    setTimeout(() => openCart(), 100)
  }

  const handleAddToWishlist = (product: any) => {
    const wasAdded = toggleWishlistItem({
      productId: product.id,
      name: product.name,
      slug: product.slug,
      price: product.price,
      comparePrice: product.comparePrice,
      image: product.images?.[0]?.url || '/placeholder-product.jpg',
      inStock: product.inStock !== false,
      category: product.category?.name || '',
      rating: product.averageRating || 0,
    })

    toast({
      title: wasAdded ? "Added to Wishlist" : "Removed from Wishlist",
      description: `${product.name} has been ${wasAdded ? 'added to' : 'removed from'} your wishlist`,
    })
  }

  const ProductCard = ({ product }: { product: any }) => (
    <Card className="group hover:shadow-lg transition-all duration-300">
      <div className="relative overflow-hidden">
        <div className="aspect-square bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
          <div className="text-gray-400 text-4xl">📦</div>
        </div>
        
        {/* Overlay Actions */}
        <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-2">
          <Button
            size="sm"
            variant="secondary"
            onClick={() => handleAddToWishlist(product)}
          >
            <Heart className="h-4 w-4" />
          </Button>
          <Link href={`/product/${product.slug}`}>
            <Button size="sm">Quick View</Button>
          </Link>
        </div>

        {/* Badges */}
        <div className="absolute top-2 left-2 space-y-1">
          {product.featured && (
            <span className="bg-primary-500 text-white px-2 py-1 text-xs rounded">
              Featured
            </span>
          )}
          {product.comparePrice && (
            <span className="bg-red-500 text-white px-2 py-1 text-xs rounded">
              Sale
            </span>
          )}
          {!product.inStock && (
            <span className="bg-gray-500 text-white px-2 py-1 text-xs rounded">
              Out of Stock
            </span>
          )}
        </div>
      </div>

      <CardContent className="p-4">
        <div className="space-y-2">
          <h3 className="font-semibold text-sm line-clamp-2">
            <Link 
              href={`/product/${product.slug}`}
              className="hover:text-primary-600 transition-colors"
            >
              {product.name}
            </Link>
          </h3>
          
          <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-2">
            {product.description}
          </p>

          {/* Rating */}
          <div className="flex items-center space-x-1">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`h-3 w-3 ${
                    i < Math.floor(product.averageRating || 0)
                      ? "text-yellow-400 fill-current"
                      : "text-gray-300"
                  }`}
                />
              ))}
            </div>
            <span className="text-xs text-gray-500">
              {product.rating} ({product.reviewCount || 0})
            </span>
          </div>

          {/* Price */}
          <div className="flex items-center space-x-2">
            <span className="font-bold text-lg">
              {formatPrice(product.price)}
            </span>
            {product.comparePrice && (
              <span className="text-sm text-gray-500 line-through">
                {formatPrice(product.comparePrice)}
              </span>
            )}
          </div>

          {/* Add to Cart Button */}
          <Button
            className="w-full"
            size="sm"
            onClick={() => handleAddToCart(product)}
            disabled={!product.inStock}
          >
            <ShoppingCart className="h-4 w-4 mr-2" />
            {product.inStock ? "Add to Cart" : "Out of Stock"}
          </Button>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Shop
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Discover our amazing products
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filter Bar */}
          <div className="flex flex-wrap items-center gap-4">
            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>

            {/* Sort */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              {sortOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>

            {/* Advanced Filters Toggle */}
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
            >
              <SlidersHorizontal className="h-4 w-4 mr-2" />
              Filters
            </Button>

            {/* View Mode Toggle */}
            <div className="flex border rounded-md">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="rounded-r-none"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="rounded-l-none"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>

            <div className="ml-auto text-sm text-gray-500">
              {filteredProducts.length} products found
            </div>
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <Card>
              <CardContent className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Price Range */}
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Price Range
                    </label>
                    <div className="flex items-center space-x-2">
                      <Input
                        type="number"
                        placeholder="Min"
                        value={priceRange.min}
                        onChange={(e) => setPriceRange(prev => ({ ...prev, min: Number(e.target.value) }))}
                      />
                      <span>-</span>
                      <Input
                        type="number"
                        placeholder="Max"
                        value={priceRange.max}
                        onChange={(e) => setPriceRange(prev => ({ ...prev, max: Number(e.target.value) }))}
                      />
                    </div>
                  </div>

                  {/* Rating Filter */}
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Minimum Rating
                    </label>
                    <select
                      value={selectedRating}
                      onChange={(e) => setSelectedRating(Number(e.target.value))}
                      className="w-full px-3 py-2 border rounded-md bg-background"
                    >
                      <option value={0}>Any Rating</option>
                      <option value={4}>4+ Stars</option>
                      <option value={4.5}>4.5+ Stars</option>
                    </select>
                  </div>

                  {/* Stock Filter */}
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Availability
                    </label>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input type="checkbox" className="mr-2" defaultChecked />
                        In Stock
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" className="mr-2" />
                        Out of Stock
                      </label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Products Grid */}
        <div className={`grid gap-6 ${
          viewMode === "grid" 
            ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4" 
            : "grid-cols-1"
        }`}>
          {isLoading ? (
            [...Array(8)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <div className="aspect-square bg-gray-200 dark:bg-gray-700"></div>
                <CardContent className="p-4">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
                </CardContent>
              </Card>
            ))
          ) : (
            products.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))
          )}
        </div>

        {/* Empty State */}
        {!isLoading && !error && products.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              No products found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Try adjusting your search or filter criteria
            </p>
            <Button onClick={() => {
              setSearchQuery("")
              setSelectedCategory("All Categories")
              setPriceRange({ min: 0, max: 1000 })
              setSelectedRating(0)
            }}>
              Clear Filters
            </Button>
          </div>
        )}

        {/* Load More */}
        {filteredProducts.length > 0 && (
          <div className="text-center mt-12">
            <Button variant="outline" size="lg">
              Load More Products
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
