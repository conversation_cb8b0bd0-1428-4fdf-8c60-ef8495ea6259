"use client"

import { SessionProvider } from "next-auth/react"
import { ThemeProvider } from "@/components/providers/theme-provider"
import { QueryProvider } from "@/components/providers/query-provider"
import { ToastProvider } from "@/components/providers/toast-provider"
import { LanguageProvider } from "@/components/providers/language-provider"

interface ClientProvidersProps {
  children: React.ReactNode
}

export function ClientProviders({ children }: ClientProvidersProps) {
  return (
    <QueryProvider>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
      >
        <LanguageProvider>
          <ToastProvider>
            <SessionProvider>
              {children}
            </SessionProvider>
          </ToastProvider>
        </LanguageProvider>
      </ThemeProvider>
    </QueryProvider>
  )
}
