"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { formatPrice, formatDate } from "@/lib/utils"
import {
  Package,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Plus,
  Minus,
  Edit,
  Eye,
  Search,
  Filter,
  Download,
  Upload,
  BarChart3,
  ShoppingCart,
  Truck,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw,
} from "lucide-react"

interface InventoryItem {
  id: string
  productId: string
  productName: string
  sku: string
  category: string
  brand: string
  currentStock: number
  minStock: number
  maxStock: number
  reorderPoint: number
  unitCost: number
  sellingPrice: number
  location: string
  supplier: string
  lastRestocked: Date
  status: 'in_stock' | 'low_stock' | 'out_of_stock' | 'discontinued'
  reservedStock: number
  availableStock: number
  totalValue: number
  movements: InventoryMovement[]
}

interface InventoryMovement {
  id: string
  type: 'in' | 'out' | 'adjustment' | 'transfer'
  quantity: number
  reason: string
  date: Date
  reference?: string
  user: string
  notes?: string
}

// Mock inventory data
const mockInventory: InventoryItem[] = [
  {
    id: "1",
    productId: "prod-1",
    productName: "Premium Wireless Headphones",
    sku: "PWH-001",
    category: "Electronics",
    brand: "AudioTech",
    currentStock: 45,
    minStock: 10,
    maxStock: 100,
    reorderPoint: 15,
    unitCost: 120.00,
    sellingPrice: 199.99,
    location: "Warehouse A - Section 1",
    supplier: "AudioTech Supplies",
    lastRestocked: new Date("2024-01-10"),
    status: 'in_stock',
    reservedStock: 8,
    availableStock: 37,
    totalValue: 5400.00,
    movements: [
      {
        id: "mov-1",
        type: 'in',
        quantity: 50,
        reason: 'Purchase Order #PO-2024-001',
        date: new Date("2024-01-10"),
        reference: 'PO-2024-001',
        user: 'John Doe',
        notes: 'New stock arrival'
      },
      {
        id: "mov-2",
        type: 'out',
        quantity: 5,
        reason: 'Sales Order #SO-2024-015',
        date: new Date("2024-01-12"),
        reference: 'SO-2024-015',
        user: 'System',
        notes: 'Customer order fulfillment'
      }
    ]
  },
  {
    id: "2",
    productId: "prod-2",
    productName: "Smart Fitness Watch",
    sku: "SFW-002",
    category: "Wearables",
    brand: "FitTech",
    currentStock: 8,
    minStock: 15,
    maxStock: 80,
    reorderPoint: 20,
    unitCost: 180.00,
    sellingPrice: 299.99,
    location: "Warehouse A - Section 2",
    supplier: "FitTech Direct",
    lastRestocked: new Date("2023-12-28"),
    status: 'low_stock',
    reservedStock: 3,
    availableStock: 5,
    totalValue: 1440.00,
    movements: [
      {
        id: "mov-3",
        type: 'out',
        quantity: 12,
        reason: 'Sales Order #SO-2024-020',
        date: new Date("2024-01-08"),
        reference: 'SO-2024-020',
        user: 'System',
        notes: 'Bulk order fulfillment'
      }
    ]
  },
  {
    id: "3",
    productId: "prod-3",
    productName: "Professional Camera Lens",
    sku: "PCL-003",
    category: "Photography",
    brand: "LensMaster",
    currentStock: 0,
    minStock: 5,
    maxStock: 25,
    reorderPoint: 8,
    unitCost: 650.00,
    sellingPrice: 899.99,
    location: "Warehouse B - Section 1",
    supplier: "LensMaster Pro",
    lastRestocked: new Date("2023-11-15"),
    status: 'out_of_stock',
    reservedStock: 0,
    availableStock: 0,
    totalValue: 0,
    movements: [
      {
        id: "mov-4",
        type: 'out',
        quantity: 3,
        reason: 'Sales Order #SO-2024-005',
        date: new Date("2024-01-05"),
        reference: 'SO-2024-005',
        user: 'System',
        notes: 'Last units sold'
      }
    ]
  },
  {
    id: "4",
    productId: "prod-4",
    productName: "Gaming Mechanical Keyboard",
    sku: "GMK-004",
    category: "Gaming",
    brand: "GamePro",
    currentStock: 32,
    minStock: 12,
    maxStock: 60,
    reorderPoint: 18,
    unitCost: 85.00,
    sellingPrice: 149.99,
    location: "Warehouse A - Section 3",
    supplier: "GamePro Wholesale",
    lastRestocked: new Date("2024-01-05"),
    status: 'in_stock',
    reservedStock: 5,
    availableStock: 27,
    totalValue: 2720.00,
    movements: [
      {
        id: "mov-5",
        type: 'in',
        quantity: 40,
        reason: 'Purchase Order #PO-2024-003',
        date: new Date("2024-01-05"),
        reference: 'PO-2024-003',
        user: 'Jane Smith',
        notes: 'Restocking popular item'
      }
    ]
  }
]

export default function InventoryManagementPage() {
  const [inventory, setInventory] = useState(mockInventory)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null)
  const [showMovementModal, setShowMovementModal] = useState(false)
  const [movementType, setMovementType] = useState<'in' | 'out' | 'adjustment'>('in')
  const [movementQuantity, setMovementQuantity] = useState(0)
  const [movementReason, setMovementReason] = useState("")
  const [movementNotes, setMovementNotes] = useState("")

  const { t } = useLanguage()
  const { toast } = useToast()

  // Calculate summary statistics
  const totalItems = inventory.length
  const totalValue = inventory.reduce((sum, item) => sum + item.totalValue, 0)
  const lowStockItems = inventory.filter(item => item.status === 'low_stock').length
  const outOfStockItems = inventory.filter(item => item.status === 'out_of_stock').length

  // Filter inventory
  const filteredInventory = inventory.filter(item => {
    const matchesSearch = item.productName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.sku.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.brand.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = statusFilter === "all" || item.status === statusFilter
    const matchesCategory = categoryFilter === "all" || item.category === categoryFilter

    return matchesSearch && matchesStatus && matchesCategory
  })

  const handleStockMovement = () => {
    if (!selectedItem || movementQuantity <= 0) {
      toast({
        title: "Invalid Input",
        description: "Please enter a valid quantity",
        variant: "destructive"
      })
      return
    }

    const newMovement: InventoryMovement = {
      id: `mov-${Date.now()}`,
      type: movementType,
      quantity: movementQuantity,
      reason: movementReason,
      date: new Date(),
      user: "Current User",
      notes: movementNotes
    }

    const updatedInventory = inventory.map(item => {
      if (item.id === selectedItem.id) {
        let newStock = item.currentStock
        
        if (movementType === 'in') {
          newStock += movementQuantity
        } else if (movementType === 'out') {
          newStock = Math.max(0, newStock - movementQuantity)
        } else if (movementType === 'adjustment') {
          newStock = movementQuantity
        }

        const availableStock = newStock - item.reservedStock
        let status: InventoryItem['status'] = 'in_stock'
        
        if (newStock === 0) {
          status = 'out_of_stock'
        } else if (newStock <= item.reorderPoint) {
          status = 'low_stock'
        }

        return {
          ...item,
          currentStock: newStock,
          availableStock: Math.max(0, availableStock),
          status,
          totalValue: newStock * item.unitCost,
          movements: [newMovement, ...item.movements]
        }
      }
      return item
    })

    setInventory(updatedInventory)
    setShowMovementModal(false)
    setMovementQuantity(0)
    setMovementReason("")
    setMovementNotes("")
    setSelectedItem(null)

    toast({
      title: "Stock Updated",
      description: `Successfully ${movementType === 'in' ? 'added' : movementType === 'out' ? 'removed' : 'adjusted'} ${movementQuantity} units`
    })
  }

  const getStatusColor = (status: InventoryItem['status']) => {
    switch (status) {
      case 'in_stock':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'low_stock':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'out_of_stock':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'discontinued':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: InventoryItem['status']) => {
    switch (status) {
      case 'in_stock':
        return <CheckCircle className="h-4 w-4" />
      case 'low_stock':
        return <AlertTriangle className="h-4 w-4" />
      case 'out_of_stock':
        return <XCircle className="h-4 w-4" />
      case 'discontinued':
        return <Minus className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Inventory Management</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Monitor and manage your product inventory
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline">
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Product
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Items
                </p>
                <p className="text-2xl font-bold">{totalItems}</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Value
                </p>
                <p className="text-2xl font-bold">{formatPrice(totalValue)}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Low Stock
                </p>
                <p className="text-2xl font-bold text-yellow-600">{lowStockItems}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Out of Stock
                </p>
                <p className="text-2xl font-bold text-red-600">{outOfStockItems}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search products, SKU, or brand..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
              >
                <option value="all">All Status</option>
                <option value="in_stock">In Stock</option>
                <option value="low_stock">Low Stock</option>
                <option value="out_of_stock">Out of Stock</option>
                <option value="discontinued">Discontinued</option>
              </select>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
              >
                <option value="all">All Categories</option>
                <option value="Electronics">Electronics</option>
                <option value="Wearables">Wearables</option>
                <option value="Photography">Photography</option>
                <option value="Gaming">Gaming</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Inventory Table */}
      <Card>
        <CardHeader>
          <CardTitle>Inventory Items</CardTitle>
          <CardDescription>
            Showing {filteredInventory.length} of {totalItems} items
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium">Product</th>
                  <th className="text-left py-3 px-4 font-medium">SKU</th>
                  <th className="text-left py-3 px-4 font-medium">Status</th>
                  <th className="text-left py-3 px-4 font-medium">Stock</th>
                  <th className="text-left py-3 px-4 font-medium">Available</th>
                  <th className="text-left py-3 px-4 font-medium">Value</th>
                  <th className="text-left py-3 px-4 font-medium">Last Restocked</th>
                  <th className="text-left py-3 px-4 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredInventory.map((item) => (
                  <tr key={item.id} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td className="py-3 px-4">
                      <div>
                        <p className="font-medium">{item.productName}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {item.brand} • {item.category}
                        </p>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <code className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm">
                        {item.sku}
                      </code>
                    </td>
                    <td className="py-3 px-4">
                      <Badge className={getStatusColor(item.status)}>
                        {getStatusIcon(item.status)}
                        <span className="ml-1 capitalize">{item.status.replace('_', ' ')}</span>
                      </Badge>
                    </td>
                    <td className="py-3 px-4">
                      <div className="text-sm">
                        <p className="font-medium">{item.currentStock}</p>
                        <p className="text-gray-600 dark:text-gray-400">
                          Min: {item.minStock} • Max: {item.maxStock}
                        </p>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="text-sm">
                        <p className="font-medium">{item.availableStock}</p>
                        <p className="text-gray-600 dark:text-gray-400">
                          Reserved: {item.reservedStock}
                        </p>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <p className="font-medium">{formatPrice(item.totalValue)}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        @ {formatPrice(item.unitCost)}
                      </p>
                    </td>
                    <td className="py-3 px-4">
                      <p className="text-sm">{formatDate(item.lastRestocked)}</p>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setSelectedItem(item)
                            setMovementType('in')
                            setShowMovementModal(true)
                          }}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setSelectedItem(item)
                            setMovementType('out')
                            setShowMovementModal(true)
                          }}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Eye className="h-3 w-3" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Stock Movement Modal */}
      {showMovementModal && selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardHeader>
              <CardTitle>Stock Movement</CardTitle>
              <CardDescription>
                {selectedItem.productName} ({selectedItem.sku})
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Movement Type</label>
                <select
                  value={movementType}
                  onChange={(e) => setMovementType(e.target.value as 'in' | 'out' | 'adjustment')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                >
                  <option value="in">Stock In</option>
                  <option value="out">Stock Out</option>
                  <option value="adjustment">Adjustment</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  {movementType === 'adjustment' ? 'New Stock Level' : 'Quantity'}
                </label>
                <input
                  type="number"
                  value={movementQuantity}
                  onChange={(e) => setMovementQuantity(Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                  min="0"
                />
                {movementType !== 'adjustment' && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Current stock: {selectedItem.currentStock}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Reason</label>
                <input
                  type="text"
                  value={movementReason}
                  onChange={(e) => setMovementReason(e.target.value)}
                  placeholder="e.g., Purchase Order #PO-2024-001"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Notes (Optional)</label>
                <textarea
                  value={movementNotes}
                  onChange={(e) => setMovementNotes(e.target.value)}
                  placeholder="Additional notes..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                />
              </div>

              <div className="flex space-x-2">
                <Button onClick={handleStockMovement} className="flex-1">
                  Update Stock
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowMovementModal(false)
                    setSelectedItem(null)
                    setMovementQuantity(0)
                    setMovementReason("")
                    setMovementNotes("")
                  }}
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
