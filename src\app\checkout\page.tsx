"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { useCartStore } from "@/store/cart-store"
import { useSession } from "next-auth/react"
import { formatPrice } from "@/lib/utils"
import {
  CreditCard,
  Truck,
  MapPin,
  User,
  Mail,
  Phone,
  Lock,
  CheckCircle,
  AlertCircle,
  ArrowLeft,
  ArrowRight,
  Package,
  Calendar,
  Shield,
  Percent,
} from "lucide-react"

// Mock cart data - in real app, this would come from cart context/state
const mockCartItems = [
  {
    id: "1",
    name: "Premium Wireless Headphones",
    price: 199.99,
    quantity: 1,
    image: "/product-1.jpg",
    variant: "Black"
  },
  {
    id: "2",
    name: "Smart Fitness Watch",
    price: 299.99,
    quantity: 2,
    image: "/product-2.jpg",
    variant: "Silver"
  }
]

const mockShippingMethods = [
  {
    id: "standard",
    name: "Standard Shipping",
    description: "5-7 business days",
    price: 9.99,
    estimatedDays: "5-7"
  },
  {
    id: "express",
    name: "Express Shipping",
    description: "2-3 business days",
    price: 19.99,
    estimatedDays: "2-3"
  },
  {
    id: "overnight",
    name: "Overnight Shipping",
    description: "Next business day",
    price: 39.99,
    estimatedDays: "1"
  }
]

const mockPaymentMethods = [
  {
    id: "card",
    name: "Credit/Debit Card",
    description: "Visa, Mastercard, American Express",
    icon: CreditCard,
    fees: 0
  },
  {
    id: "paypal",
    name: "PayPal",
    description: "Pay with your PayPal account",
    icon: Shield,
    fees: 0
  },
  {
    id: "apple_pay",
    name: "Apple Pay",
    description: "Pay with Touch ID or Face ID",
    icon: Shield,
    fees: 0
  }
]

interface CheckoutStep {
  id: string
  title: string
  description: string
  completed: boolean
}

export default function CheckoutPage() {
  const [currentStep, setCurrentStep] = useState(0)
  const [shippingInfo, setShippingInfo] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    country: "US"
  })
  const [selectedShipping, setSelectedShipping] = useState("standard")
  const [selectedPayment, setSelectedPayment] = useState("card")
  const [paymentInfo, setPaymentInfo] = useState({
    cardNumber: "",
    expiryDate: "",
    cvv: "",
    cardName: ""
  })
  const [couponCode, setCouponCode] = useState("")
  const [appliedCoupon, setAppliedCoupon] = useState<any>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  
  const router = useRouter()
  const { t } = useLanguage()
  const { toast } = useToast()
  const { data: session } = useSession()
  const { items, getTotalPrice, clearCart } = useCartStore()

  const steps: CheckoutStep[] = [
    {
      id: "shipping",
      title: "Shipping Information",
      description: "Enter your delivery details",
      completed: false
    },
    {
      id: "delivery",
      title: "Delivery Method",
      description: "Choose your shipping option",
      completed: false
    },
    {
      id: "payment",
      title: "Payment Information",
      description: "Enter your payment details",
      completed: false
    },
    {
      id: "review",
      title: "Review Order",
      description: "Confirm your purchase",
      completed: false
    }
  ]

  // Calculate totals
  const subtotal = getTotalPrice()
  const shippingCost = mockShippingMethods.find(m => m.id === selectedShipping)?.price || 0
  const taxRate = 0.08 // 8% tax
  const taxAmount = subtotal * taxRate
  const discountAmount = appliedCoupon ? (subtotal * appliedCoupon.discount) : 0
  const total = subtotal + shippingCost + taxAmount - discountAmount

  const handleApplyCoupon = () => {
    // Mock coupon validation
    if (couponCode.toLowerCase() === "save10") {
      setAppliedCoupon({
        code: "SAVE10",
        discount: 0.1,
        description: "10% off your order"
      })
      toast({
        title: "Coupon Applied!",
        description: "You saved 10% on your order"
      })
    } else if (couponCode.toLowerCase() === "freeship") {
      setAppliedCoupon({
        code: "FREESHIP",
        discount: 0,
        freeShipping: true,
        description: "Free shipping on your order"
      })
      toast({
        title: "Coupon Applied!",
        description: "Free shipping applied to your order"
      })
    } else {
      toast({
        title: "Invalid Coupon",
        description: "The coupon code you entered is not valid",
        variant: "destructive"
      })
    }
  }

  const handleNextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handlePlaceOrder = async () => {
    setIsProcessing(true)
    
    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      // Mock order creation
      const orderId = `ORD-${Date.now()}`
      
      toast({
        title: "Order Placed Successfully!",
        description: `Your order ${orderId} has been confirmed`
      })

      // Clear the cart
      clearCart()

      // Redirect to order confirmation
      router.push(`/order-confirmation/${orderId}`)
    } catch (error) {
      toast({
        title: "Payment Failed",
        description: "There was an error processing your payment. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const ShippingForm = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-2">First Name</label>
          <input
            type="text"
            value={shippingInfo.firstName}
            onChange={(e) => setShippingInfo({...shippingInfo, firstName: e.target.value})}
            className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Last Name</label>
          <input
            type="text"
            value={shippingInfo.lastName}
            onChange={(e) => setShippingInfo({...shippingInfo, lastName: e.target.value})}
            className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500"
            required
          />
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-2">Email</label>
          <input
            type="email"
            value={shippingInfo.email}
            onChange={(e) => setShippingInfo({...shippingInfo, email: e.target.value})}
            className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Phone</label>
          <input
            type="tel"
            value={shippingInfo.phone}
            onChange={(e) => setShippingInfo({...shippingInfo, phone: e.target.value})}
            className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500"
            required
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">Address</label>
        <input
          type="text"
          value={shippingInfo.address}
          onChange={(e) => setShippingInfo({...shippingInfo, address: e.target.value})}
          className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500"
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium mb-2">City</label>
          <input
            type="text"
            value={shippingInfo.city}
            onChange={(e) => setShippingInfo({...shippingInfo, city: e.target.value})}
            className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">State</label>
          <input
            type="text"
            value={shippingInfo.state}
            onChange={(e) => setShippingInfo({...shippingInfo, state: e.target.value})}
            className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">ZIP Code</label>
          <input
            type="text"
            value={shippingInfo.zipCode}
            onChange={(e) => setShippingInfo({...shippingInfo, zipCode: e.target.value})}
            className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500"
            required
          />
        </div>
      </div>
    </div>
  )

  const DeliveryOptions = () => (
    <div className="space-y-4">
      {mockShippingMethods.map((method) => (
        <div
          key={method.id}
          className={`p-4 border rounded-lg cursor-pointer transition-colors ${
            selectedShipping === method.id
              ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
              : 'border-gray-200 hover:border-gray-300'
          }`}
          onClick={() => setSelectedShipping(method.id)}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <input
                type="radio"
                checked={selectedShipping === method.id}
                onChange={() => setSelectedShipping(method.id)}
                className="text-primary-600"
              />
              <div>
                <h3 className="font-medium">{method.name}</h3>
                <p className="text-sm text-gray-600">{method.description}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="font-medium">{formatPrice(method.price)}</p>
              <p className="text-sm text-gray-600">{method.estimatedDays} days</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  )

  const PaymentForm = () => (
    <div className="space-y-6">
      {/* Payment Methods */}
      <div className="space-y-4">
        <h3 className="font-medium">Payment Method</h3>
        {mockPaymentMethods.map((method) => {
          const Icon = method.icon
          return (
            <div
              key={method.id}
              className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                selectedPayment === method.id
                  ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setSelectedPayment(method.id)}
            >
              <div className="flex items-center space-x-3">
                <input
                  type="radio"
                  checked={selectedPayment === method.id}
                  onChange={() => setSelectedPayment(method.id)}
                  className="text-primary-600"
                />
                <Icon className="h-5 w-5" />
                <div>
                  <h4 className="font-medium">{method.name}</h4>
                  <p className="text-sm text-gray-600">{method.description}</p>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Card Details */}
      {selectedPayment === 'card' && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Card Number</label>
            <input
              type="text"
              value={paymentInfo.cardNumber}
              onChange={(e) => setPaymentInfo({...paymentInfo, cardNumber: e.target.value})}
              placeholder="1234 5678 9012 3456"
              className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500"
              required
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Expiry Date</label>
              <input
                type="text"
                value={paymentInfo.expiryDate}
                onChange={(e) => setPaymentInfo({...paymentInfo, expiryDate: e.target.value})}
                placeholder="MM/YY"
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">CVV</label>
              <input
                type="text"
                value={paymentInfo.cvv}
                onChange={(e) => setPaymentInfo({...paymentInfo, cvv: e.target.value})}
                placeholder="123"
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Cardholder Name</label>
            <input
              type="text"
              value={paymentInfo.cardName}
              onChange={(e) => setPaymentInfo({...paymentInfo, cardName: e.target.value})}
              className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500"
              required
            />
          </div>
        </div>
      )}
    </div>
  )

  const OrderReview = () => (
    <div className="space-y-6">
      {/* Order Items */}
      <div>
        <h3 className="font-medium mb-4">Order Items</h3>
        <div className="space-y-4">
          {items.map((item) => (
            <div key={`${item.id}-${item.variant?.id || 'default'}`} className="flex items-center space-x-4 p-4 border rounded-lg">
              <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                <Package className="h-8 w-8 text-gray-400" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium">{item.name}</h4>
                {item.variant && (
                  <p className="text-sm text-gray-600">Variant: {item.variant.name}</p>
                )}
                <p className="text-sm text-gray-600">Quantity: {item.quantity}</p>
              </div>
              <div className="text-right">
                <p className="font-medium">{formatPrice(item.price * item.quantity)}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Shipping Info */}
      <div>
        <h3 className="font-medium mb-4">Shipping Information</h3>
        <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <p className="font-medium">{shippingInfo.firstName} {shippingInfo.lastName}</p>
          <p className="text-sm text-gray-600">{shippingInfo.address}</p>
          <p className="text-sm text-gray-600">{shippingInfo.city}, {shippingInfo.state} {shippingInfo.zipCode}</p>
          <p className="text-sm text-gray-600">{shippingInfo.email}</p>
          <p className="text-sm text-gray-600">{shippingInfo.phone}</p>
        </div>
      </div>

      {/* Payment Method */}
      <div>
        <h3 className="font-medium mb-4">Payment Method</h3>
        <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <p className="font-medium">
            {mockPaymentMethods.find(m => m.id === selectedPayment)?.name}
          </p>
          {selectedPayment === 'card' && paymentInfo.cardNumber && (
            <p className="text-sm text-gray-600">
              **** **** **** {paymentInfo.cardNumber.slice(-4)}
            </p>
          )}
        </div>
      </div>
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Cart
          </Button>
          <h1 className="text-3xl font-bold">Checkout</h1>
          <p className="text-gray-600 dark:text-gray-400">Complete your purchase</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Progress Steps */}
            <div className="mb-8">
              <div className="flex items-center justify-between">
                {steps.map((step, index) => (
                  <div key={step.id} className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      index <= currentStep
                        ? 'bg-primary-600 text-white'
                        : 'bg-gray-200 text-gray-600'
                    }`}>
                      {index < currentStep ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        index + 1
                      )}
                    </div>
                    {index < steps.length - 1 && (
                      <div className={`w-16 h-0.5 mx-2 ${
                        index < currentStep ? 'bg-primary-600' : 'bg-gray-200'
                      }`} />
                    )}
                  </div>
                ))}
              </div>
              <div className="mt-4">
                <h2 className="text-xl font-semibold">{steps[currentStep].title}</h2>
                <p className="text-gray-600 dark:text-gray-400">{steps[currentStep].description}</p>
              </div>
            </div>

            {/* Step Content */}
            <Card>
              <CardContent className="p-6">
                {currentStep === 0 && <ShippingForm />}
                {currentStep === 1 && <DeliveryOptions />}
                {currentStep === 2 && <PaymentForm />}
                {currentStep === 3 && <OrderReview />}
              </CardContent>
            </Card>

            {/* Navigation */}
            <div className="flex justify-between mt-6">
              <Button
                variant="outline"
                onClick={handlePrevStep}
                disabled={currentStep === 0}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>
              
              {currentStep < steps.length - 1 ? (
                <Button onClick={handleNextStep}>
                  Next
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              ) : (
                <Button
                  onClick={handlePlaceOrder}
                  disabled={isProcessing}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      <Lock className="h-4 w-4 mr-2" />
                      Place Order
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8">
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Items */}
                <div className="space-y-2">
                  {items.map((item) => (
                    <div key={`${item.id}-${item.variant?.id || 'default'}`} className="flex justify-between text-sm">
                      <span>{item.name} × {item.quantity}</span>
                      <span>{formatPrice(item.price * item.quantity)}</span>
                    </div>
                  ))}
                </div>

                <div className="border-t pt-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal</span>
                    <span>{formatPrice(subtotal)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Shipping</span>
                    <span>{appliedCoupon?.freeShipping ? 'Free' : formatPrice(shippingCost)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Tax</span>
                    <span>{formatPrice(taxAmount)}</span>
                  </div>
                  {appliedCoupon && (
                    <div className="flex justify-between text-sm text-green-600">
                      <span>Discount ({appliedCoupon.code})</span>
                      <span>-{formatPrice(discountAmount)}</span>
                    </div>
                  )}
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-semibold">
                      <span>Total</span>
                      <span>{formatPrice(total)}</span>
                    </div>
                  </div>
                </div>

                {/* Coupon Code */}
                <div className="border-t pt-4">
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={couponCode}
                      onChange={(e) => setCouponCode(e.target.value)}
                      placeholder="Coupon code"
                      className="flex-1 px-3 py-2 text-sm border rounded-lg focus:ring-2 focus:ring-primary-500"
                    />
                    <Button size="sm" onClick={handleApplyCoupon}>
                      Apply
                    </Button>
                  </div>
                  {appliedCoupon && (
                    <div className="mt-2 p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="text-sm text-green-600">{appliedCoupon.description}</span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Security Badge */}
                <div className="border-t pt-4">
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Shield className="h-4 w-4" />
                    <span>Secure 256-bit SSL encryption</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
