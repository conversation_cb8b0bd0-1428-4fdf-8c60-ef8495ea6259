"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { formatDate, formatRelativeTime } from "@/lib/utils"
import {
  Bell,
  X,
  Check,
  CheckCheck,
  Trash2,
  Settings,
  Filter,
  Search,
  Package,
  ShoppingCart,
  CreditCard,
  User,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  Clock,
  Star,
  Gift,
  TrendingUp,
  MessageCircle,
} from "lucide-react"

interface Notification {
  id: string
  type: 'order' | 'payment' | 'user' | 'system' | 'review' | 'promotion' | 'message'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  title: string
  message: string
  timestamp: Date
  read: boolean
  actionUrl?: string
  actionLabel?: string
  data?: Record<string, any>
  userId?: string
  userName?: string
}

// Mock notifications data
const mockNotifications: Notification[] = [
  {
    id: "1",
    type: 'order',
    priority: 'high',
    title: 'New Order Received',
    message: 'Order #ORD-2024-001 has been placed by John Doe for $299.99',
    timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
    read: false,
    actionUrl: '/admin/orders/ORD-2024-001',
    actionLabel: 'View Order',
    data: { orderId: 'ORD-2024-001', amount: 299.99 },
    userId: 'user-1',
    userName: 'John Doe'
  },
  {
    id: "2",
    type: 'payment',
    priority: 'urgent',
    title: 'Payment Failed',
    message: 'Payment for order #ORD-2024-002 failed. Customer needs assistance.',
    timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
    read: false,
    actionUrl: '/admin/orders/ORD-2024-002',
    actionLabel: 'Handle Payment',
    data: { orderId: 'ORD-2024-002', reason: 'insufficient_funds' },
    userId: 'user-2',
    userName: 'Sarah Johnson'
  },
  {
    id: "3",
    type: 'user',
    priority: 'medium',
    title: 'New User Registration',
    message: 'Mike Chen has created a new account and completed email verification.',
    timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    read: true,
    actionUrl: '/admin/users/user-3',
    actionLabel: 'View Profile',
    userId: 'user-3',
    userName: 'Mike Chen'
  },
  {
    id: "4",
    type: 'review',
    priority: 'low',
    title: 'New Product Review',
    message: 'Emily Rodriguez left a 5-star review for "Premium Wireless Headphones"',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    read: true,
    actionUrl: '/admin/products/prod-1#reviews',
    actionLabel: 'View Review',
    data: { productId: 'prod-1', rating: 5 },
    userId: 'user-4',
    userName: 'Emily Rodriguez'
  },
  {
    id: "5",
    type: 'system',
    priority: 'medium',
    title: 'Low Stock Alert',
    message: 'Smart Fitness Watch is running low on stock (8 units remaining)',
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
    read: false,
    actionUrl: '/admin/inventory',
    actionLabel: 'Manage Inventory',
    data: { productId: 'prod-2', stock: 8, threshold: 15 }
  },
  {
    id: "6",
    type: 'promotion',
    priority: 'low',
    title: 'Coupon Usage Milestone',
    message: 'Coupon "SAVE20" has been used 100 times! Consider creating similar offers.',
    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
    read: true,
    actionUrl: '/admin/coupons',
    actionLabel: 'View Coupons',
    data: { couponCode: 'SAVE20', usageCount: 100 }
  },
  {
    id: "7",
    type: 'message',
    priority: 'medium',
    title: 'Customer Support Message',
    message: 'David Thompson sent a message about order delivery status.',
    timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago
    read: false,
    actionUrl: '/admin/support/msg-123',
    actionLabel: 'Reply',
    userId: 'user-5',
    userName: 'David Thompson'
  }
]

interface NotificationCenterProps {
  isOpen: boolean
  onClose: () => void
}

export default function NotificationCenter({ isOpen, onClose }: NotificationCenterProps) {
  const [notifications, setNotifications] = useState(mockNotifications)
  const [filter, setFilter] = useState<'all' | 'unread' | 'high' | 'urgent'>('all')
  const [searchQuery, setSearchQuery] = useState("")
  
  const { t } = useLanguage()
  const { toast } = useToast()

  // Filter notifications
  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         notification.message.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesFilter = filter === 'all' || 
                         (filter === 'unread' && !notification.read) ||
                         (filter === 'high' && notification.priority === 'high') ||
                         (filter === 'urgent' && notification.priority === 'urgent')

    return matchesSearch && matchesFilter
  })

  // Count unread notifications
  const unreadCount = notifications.filter(n => !n.read).length

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'order':
        return <ShoppingCart className="h-5 w-5 text-blue-600" />
      case 'payment':
        return <CreditCard className="h-5 w-5 text-green-600" />
      case 'user':
        return <User className="h-5 w-5 text-purple-600" />
      case 'system':
        return <AlertTriangle className="h-5 w-5 text-orange-600" />
      case 'review':
        return <Star className="h-5 w-5 text-yellow-600" />
      case 'promotion':
        return <Gift className="h-5 w-5 text-pink-600" />
      case 'message':
        return <MessageCircle className="h-5 w-5 text-indigo-600" />
      default:
        return <Bell className="h-5 w-5 text-gray-600" />
    }
  }

  const getPriorityColor = (priority: Notification['priority']) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      case 'medium':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'low':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const markAsRead = (notificationId: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      )
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    )
    toast({
      title: "All notifications marked as read",
      description: `${unreadCount} notifications marked as read`
    })
  }

  const deleteNotification = (notificationId: string) => {
    setNotifications(prev =>
      prev.filter(notification => notification.id !== notificationId)
    )
    toast({
      title: "Notification deleted",
      description: "Notification has been removed"
    })
  }

  const clearAllNotifications = () => {
    setNotifications([])
    toast({
      title: "All notifications cleared",
      description: "All notifications have been removed"
    })
  }

  // Simulate real-time notifications
  useEffect(() => {
    const interval = setInterval(() => {
      // Randomly add new notifications (for demo purposes)
      if (Math.random() < 0.1) { // 10% chance every 30 seconds
        const newNotification: Notification = {
          id: `new-${Date.now()}`,
          type: ['order', 'payment', 'user', 'system', 'review'][Math.floor(Math.random() * 5)] as Notification['type'],
          priority: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as Notification['priority'],
          title: 'New Activity',
          message: 'A new event has occurred in your system.',
          timestamp: new Date(),
          read: false
        }
        
        setNotifications(prev => [newNotification, ...prev])
        
        // Show toast for new notification
        toast({
          title: newNotification.title,
          description: newNotification.message
        })
      }
    }, 30000) // Check every 30 seconds

    return () => clearInterval(interval)
  }, [toast])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      <div className="absolute right-0 top-0 h-full w-full max-w-md bg-white dark:bg-gray-800 shadow-xl">
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 p-4">
            <div className="flex items-center space-x-2">
              <Bell className="h-5 w-5" />
              <h2 className="text-lg font-semibold">Notifications</h2>
              {unreadCount > 0 && (
                <Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                  {unreadCount}
                </Badge>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" onClick={markAllAsRead}>
                <CheckCheck className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Filters and Search */}
          <div className="border-b border-gray-200 dark:border-gray-700 p-4 space-y-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search notifications..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div className="flex space-x-2">
              <Button
                size="sm"
                variant={filter === 'all' ? 'default' : 'outline'}
                onClick={() => setFilter('all')}
              >
                All
              </Button>
              <Button
                size="sm"
                variant={filter === 'unread' ? 'default' : 'outline'}
                onClick={() => setFilter('unread')}
              >
                Unread
              </Button>
              <Button
                size="sm"
                variant={filter === 'high' ? 'default' : 'outline'}
                onClick={() => setFilter('high')}
              >
                High
              </Button>
              <Button
                size="sm"
                variant={filter === 'urgent' ? 'default' : 'outline'}
                onClick={() => setFilter('urgent')}
              >
                Urgent
              </Button>
            </div>
          </div>

          {/* Notifications List */}
          <div className="flex-1 overflow-y-auto">
            {filteredNotifications.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-center p-8">
                <Bell className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  No notifications
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {searchQuery || filter !== 'all'
                    ? 'No notifications match your current filter'
                    : 'You\'re all caught up!'}
                </p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {filteredNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                      !notification.read ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {notification.title}
                          </p>
                          <div className="flex items-center space-x-2">
                            <Badge className={getPriorityColor(notification.priority)}>
                              {notification.priority}
                            </Badge>
                            {!notification.read && (
                              <div className="w-2 h-2 bg-blue-600 rounded-full" />
                            )}
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          {notification.message}
                        </p>
                        <div className="flex items-center justify-between">
                          <p className="text-xs text-gray-500">
                            {formatRelativeTime(notification.timestamp)}
                          </p>
                          <div className="flex items-center space-x-2">
                            {notification.actionUrl && (
                              <Button size="sm" variant="outline" className="text-xs">
                                {notification.actionLabel || 'View'}
                              </Button>
                            )}
                            {!notification.read && (
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => markAsRead(notification.id)}
                              >
                                <Check className="h-3 w-3" />
                              </Button>
                            )}
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => deleteNotification(notification.id)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          {notifications.length > 0 && (
            <div className="border-t border-gray-200 dark:border-gray-700 p-4">
              <Button
                variant="outline"
                size="sm"
                onClick={clearAllNotifications}
                className="w-full"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Clear All Notifications
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
