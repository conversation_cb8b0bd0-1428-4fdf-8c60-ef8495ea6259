"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import {
  Search,
  ChevronDown,
  ChevronUp,
  HelpCircle,
  ShoppingCart,
  CreditCard,
  Truck,
  RotateCcw,
  User,
  Settings,
  MessageCircle,
  Phone,
  Mail,
  Star,
  Clock,
  CheckCircle,
} from "lucide-react"

interface FAQ {
  id: string
  question: string
  answer: string
  category: string
  helpful: number
  views: number
  popular: boolean
}

export default function FAQPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null)
  const { t } = useLanguage()

  const categories = [
    { id: "all", name: "All Categories", icon: HelpCircle, count: 45 },
    { id: "orders", name: "Orders & Checkout", icon: ShoppingCart, count: 12 },
    { id: "shipping", name: "Shipping & Delivery", icon: Truck, count: 8 },
    { id: "returns", name: "Returns & Refunds", icon: RotateCcw, count: 7 },
    { id: "payments", name: "Payment & Billing", icon: CreditCard, count: 6 },
    { id: "account", name: "Account & Profile", icon: User, count: 8 },
    { id: "technical", name: "Technical Support", icon: Settings, count: 4 }
  ]

  const faqs: FAQ[] = [
    {
      id: "1",
      question: "How do I track my order?",
      answer: "You can track your order in several ways: 1) Use the tracking number sent to your email, 2) Log into your account and visit 'My Orders', 3) Use our order tracking page by entering your order number. You'll receive real-time updates on your package's location and estimated delivery time.",
      category: "shipping",
      helpful: 98,
      views: 15200,
      popular: true
    },
    {
      id: "2",
      question: "What payment methods do you accept?",
      answer: "We accept all major credit cards (Visa, Mastercard, American Express, Discover), PayPal, Apple Pay, Google Pay, and bank transfers. All payments are processed securely with 256-bit SSL encryption. We also offer buy-now-pay-later options through select partners.",
      category: "payments",
      helpful: 95,
      views: 12800,
      popular: true
    },
    {
      id: "3",
      question: "How do I return or exchange an item?",
      answer: "Returns are easy! You have 30 days from delivery to return most items. Simply visit our returns page, enter your order number, select the items to return, and choose your reason. We'll email you a prepaid return label. Once we receive and inspect your return, we'll process your refund within 3-5 business days.",
      category: "returns",
      helpful: 92,
      views: 11400,
      popular: true
    },
    {
      id: "4",
      question: "How long does shipping take?",
      answer: "Shipping times depend on your location and chosen method: Standard shipping (5-7 business days), Express shipping (2-3 business days), Overnight shipping (1 business day). International shipping takes 7-14 business days. Orders placed before 2 PM EST ship the same day.",
      category: "shipping",
      helpful: 89,
      views: 9700,
      popular: true
    },
    {
      id: "5",
      question: "Can I change or cancel my order?",
      answer: "You can modify or cancel your order within 1 hour of placing it by contacting customer service or using your account dashboard. After 1 hour, your order may already be in processing and changes may not be possible. Contact us immediately if you need to make changes.",
      category: "orders",
      helpful: 94,
      views: 8300,
      popular: false
    },
    {
      id: "6",
      question: "Do you offer international shipping?",
      answer: "Yes! We ship to over 25 countries worldwide. International shipping costs and delivery times vary by destination. Customers are responsible for any customs duties, taxes, or fees imposed by their country. These charges are not included in our shipping costs.",
      category: "shipping",
      helpful: 87,
      views: 7900,
      popular: false
    },
    {
      id: "7",
      question: "How do I create an account?",
      answer: "Creating an account is simple! Click 'Sign Up' in the top right corner, enter your email and create a password, then verify your email address. With an account, you can track orders, save items to your wishlist, manage addresses, and enjoy faster checkout.",
      category: "account",
      helpful: 91,
      views: 6500,
      popular: false
    },
    {
      id: "8",
      question: "What is your price matching policy?",
      answer: "We offer price matching on identical items from authorized retailers. The item must be in stock and available for immediate purchase. Contact customer service with the competitor's price and we'll match it. Price matching applies to the item price only, not including shipping or taxes.",
      category: "orders",
      helpful: 85,
      views: 5800,
      popular: false
    },
    {
      id: "9",
      question: "How does the AI recommendation system work?",
      answer: "Our AI analyzes your browsing history, purchase patterns, preferences, and similar customer behaviors to suggest products you might like. The more you interact with our site, the better our recommendations become. You can always adjust your preferences in your account settings.",
      category: "technical",
      helpful: 88,
      views: 5200,
      popular: false
    },
    {
      id: "10",
      question: "What if I receive a damaged item?",
      answer: "If your item arrives damaged, please contact us immediately with photos of the damage and packaging. We'll arrange a replacement or full refund at no cost to you. For damaged items, we provide prepaid return labels and expedited replacement shipping.",
      category: "returns",
      helpful: 96,
      views: 4900,
      popular: false
    }
  ]

  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === "all" || faq.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const popularFAQs = faqs.filter(faq => faq.popular).slice(0, 6)

  const toggleFAQ = (faqId: string) => {
    setExpandedFAQ(expandedFAQ === faqId ? null : faqId)
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-600 to-primary-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Frequently Asked Questions
            </h1>
            <p className="text-xl text-primary-100 mb-8">
              Find quick answers to common questions about shopping, shipping, returns, and more
            </p>
            
            {/* Search Bar */}
            <div className="relative max-w-2xl mx-auto">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search for answers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-4 text-gray-900 bg-white rounded-lg shadow-lg focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Popular FAQs */}
      <section className="py-16 -mt-8 relative z-10">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Most Popular Questions
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Quick answers to the questions we hear most often
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {popularFAQs.map((faq) => (
              <Card key={faq.id} className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => toggleFAQ(faq.id)}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="font-semibold text-gray-900 dark:text-white text-sm leading-tight">
                      {faq.question}
                    </h3>
                    <Badge className="bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 text-xs ml-2">
                      Popular
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span className="flex items-center space-x-1">
                      <Star className="h-3 w-3" />
                      <span>{faq.helpful}% helpful</span>
                    </span>
                    <span>{(faq.views / 1000).toFixed(1)}k views</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Categories and FAQs */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Categories Sidebar */}
            <div className="lg:col-span-1">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Categories
              </h3>
              <div className="space-y-2">
                {categories.map((category) => {
                  const Icon = category.icon
                  return (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`w-full flex items-center justify-between p-3 rounded-lg text-left transition-colors ${
                        selectedCategory === category.id
                          ? 'bg-primary-100 text-primary-900 dark:bg-primary-900 dark:text-primary-100'
                          : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <Icon className="h-4 w-4" />
                        <span className="text-sm font-medium">{category.name}</span>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {category.count}
                      </Badge>
                    </button>
                  )
                })}
              </div>
            </div>

            {/* FAQ List */}
            <div className="lg:col-span-3">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {selectedCategory === "all" ? "All Questions" : categories.find(c => c.id === selectedCategory)?.name}
                </h3>
                <span className="text-sm text-gray-500">
                  {filteredFAQs.length} question{filteredFAQs.length !== 1 ? 's' : ''}
                </span>
              </div>

              <div className="space-y-4">
                {filteredFAQs.map((faq) => (
                  <Card key={faq.id}>
                    <CardContent className="p-0">
                      <button
                        onClick={() => toggleFAQ(faq.id)}
                        className="w-full p-6 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                              {faq.question}
                            </h4>
                            <div className="flex items-center space-x-4 text-sm text-gray-500">
                              <span className="flex items-center space-x-1">
                                <Star className="h-3 w-3" />
                                <span>{faq.helpful}% helpful</span>
                              </span>
                              <span>{(faq.views / 1000).toFixed(1)}k views</span>
                              {faq.popular && (
                                <Badge className="bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 text-xs">
                                  Popular
                                </Badge>
                              )}
                            </div>
                          </div>
                          <div className="ml-4">
                            {expandedFAQ === faq.id ? (
                              <ChevronUp className="h-5 w-5 text-gray-400" />
                            ) : (
                              <ChevronDown className="h-5 w-5 text-gray-400" />
                            )}
                          </div>
                        </div>
                      </button>
                      
                      {expandedFAQ === faq.id && (
                        <div className="px-6 pb-6 border-t border-gray-200 dark:border-gray-700">
                          <div className="pt-4">
                            <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                              {faq.answer}
                            </p>
                            <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100 dark:border-gray-800">
                              <span className="text-sm text-gray-500">Was this helpful?</span>
                              <div className="flex items-center space-x-2">
                                <Button size="sm" variant="outline">
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  Yes
                                </Button>
                                <Button size="sm" variant="outline">
                                  No
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>

              {filteredFAQs.length === 0 && (
                <div className="text-center py-12">
                  <HelpCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No questions found
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    Try adjusting your search or browse different categories
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Contact Support */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Still Need Help?
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Can't find the answer you're looking for? Our support team is here to help
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <Card className="text-center">
              <CardContent className="p-6">
                <MessageCircle className="h-12 w-12 text-primary-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Live Chat
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  Chat with our AI assistant for instant help
                </p>
                <Button className="w-full">
                  Start Chat
                </Button>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="p-6">
                <Phone className="h-12 w-12 text-primary-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Phone Support
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  Mon-Fri, 9AM-6PM EST
                </p>
                <Button variant="outline" className="w-full">
                  +****************
                </Button>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="p-6">
                <Mail className="h-12 w-12 text-primary-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Email Support
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  Response within 4 hours
                </p>
                <Button variant="outline" className="w-full">
                  Send Email
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Helpful Resources */}
      <section className="py-16 bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">
              Helpful Resources
            </h2>
            <p className="text-primary-100 max-w-2xl mx-auto">
              Explore additional resources to get the most out of your AIDEVCOMMERCE experience
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <Card className="bg-white/10 border-white/20 text-white text-center">
              <CardContent className="p-6">
                <HelpCircle className="h-8 w-8 mx-auto mb-3" />
                <h3 className="font-semibold mb-2">Help Center</h3>
                <p className="text-sm text-primary-100">Comprehensive guides and tutorials</p>
              </CardContent>
            </Card>

            <Card className="bg-white/10 border-white/20 text-white text-center">
              <CardContent className="p-6">
                <Truck className="h-8 w-8 mx-auto mb-3" />
                <h3 className="font-semibold mb-2">Track Order</h3>
                <p className="text-sm text-primary-100">Real-time order tracking</p>
              </CardContent>
            </Card>

            <Card className="bg-white/10 border-white/20 text-white text-center">
              <CardContent className="p-6">
                <RotateCcw className="h-8 w-8 mx-auto mb-3" />
                <h3 className="font-semibold mb-2">Returns</h3>
                <p className="text-sm text-primary-100">Easy return process</p>
              </CardContent>
            </Card>

            <Card className="bg-white/10 border-white/20 text-white text-center">
              <CardContent className="p-6">
                <User className="h-8 w-8 mx-auto mb-3" />
                <h3 className="font-semibold mb-2">My Account</h3>
                <p className="text-sm text-primary-100">Manage your profile and orders</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  )
}
