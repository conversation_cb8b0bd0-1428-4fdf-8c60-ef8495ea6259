"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import {
  RotateCcw,
  Package,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  ArrowRight,
  Calendar,
  CreditCard,
  Truck,
  Shield,
  FileText,
  Mail,
  Phone,
  HelpCircle,
} from "lucide-react"

export default function ReturnsPage() {
  const [selectedReason, setSelectedReason] = useState("")
  const [orderNumber, setOrderNumber] = useState("")
  const { t } = useLanguage()
  const { toast } = useToast()

  const returnReasons = [
    { id: "defective", label: "Defective or damaged item", description: "Item arrived broken or not working" },
    { id: "wrong_item", label: "Wrong item received", description: "Received different item than ordered" },
    { id: "not_as_described", label: "Not as described", description: "Item doesn't match description or photos" },
    { id: "size_fit", label: "Size or fit issues", description: "Item doesn't fit as expected" },
    { id: "quality", label: "Quality concerns", description: "Item quality below expectations" },
    { id: "changed_mind", label: "Changed my mind", description: "No longer need the item" },
    { id: "other", label: "Other reason", description: "Different reason not listed above" }
  ]

  const returnProcess = [
    {
      step: 1,
      title: "Initiate Return",
      description: "Start your return request online or contact customer service",
      icon: FileText,
      timeframe: "Within 30 days"
    },
    {
      step: 2,
      title: "Package Item",
      description: "Pack the item in original packaging with all accessories",
      icon: Package,
      timeframe: "Same day"
    },
    {
      step: 3,
      title: "Ship Back",
      description: "Use the prepaid return label we provide",
      icon: Truck,
      timeframe: "Within 7 days"
    },
    {
      step: 4,
      title: "Processing",
      description: "We inspect the item and process your return",
      icon: Clock,
      timeframe: "3-5 business days"
    },
    {
      step: 5,
      title: "Refund Issued",
      description: "Refund processed to your original payment method",
      icon: CreditCard,
      timeframe: "5-7 business days"
    }
  ]

  const returnPolicies = [
    {
      category: "Electronics",
      timeframe: "30 days",
      condition: "Original packaging required",
      restockingFee: "None",
      icon: Package,
      color: "bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400"
    },
    {
      category: "Clothing & Accessories",
      timeframe: "60 days",
      condition: "Unworn with tags",
      restockingFee: "None",
      icon: Package,
      color: "bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400"
    },
    {
      category: "Home & Garden",
      timeframe: "30 days",
      condition: "Unused condition",
      restockingFee: "None",
      icon: Package,
      color: "bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-400"
    },
    {
      category: "Custom Items",
      timeframe: "No returns",
      condition: "Final sale",
      restockingFee: "N/A",
      icon: XCircle,
      color: "bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400"
    }
  ]

  const faqs = [
    {
      question: "How long do I have to return an item?",
      answer: "Most items can be returned within 30 days of delivery. Some categories like clothing have extended 60-day return windows. Custom or personalized items are final sale."
    },
    {
      question: "Do I need to pay for return shipping?",
      answer: "We provide free return shipping labels for defective items, wrong items, or items not as described. For other reasons, a small return shipping fee may apply."
    },
    {
      question: "When will I receive my refund?",
      answer: "Once we receive and inspect your return, refunds are processed within 3-5 business days. It may take an additional 5-7 business days to appear on your statement."
    },
    {
      question: "Can I exchange an item instead of returning it?",
      answer: "Yes! You can request an exchange for a different size, color, or similar item. Exchanges are processed faster than returns and refunds."
    },
    {
      question: "What if my item was damaged during shipping?",
      answer: "If your item arrived damaged, please contact us immediately with photos. We'll arrange a replacement or full refund at no cost to you."
    }
  ]

  const handleStartReturn = () => {
    if (!orderNumber.trim()) {
      toast({
        title: "Order Number Required",
        description: "Please enter your order number to start the return process.",
        variant: "destructive"
      })
      return
    }

    if (!selectedReason) {
      toast({
        title: "Return Reason Required",
        description: "Please select a reason for your return.",
        variant: "destructive"
      })
      return
    }

    toast({
      title: "Return Request Started",
      description: "We'll send you return instructions via email within 24 hours."
    })

    // Reset form
    setOrderNumber("")
    setSelectedReason("")
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-600 to-primary-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Returns & Exchanges
            </h1>
            <p className="text-xl text-primary-100 mb-8">
              Easy returns and exchanges with our hassle-free 30-day return policy
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Badge className="bg-white/20 text-white px-4 py-2">
                <Shield className="h-4 w-4 mr-2" />
                30-Day Returns
              </Badge>
              <Badge className="bg-white/20 text-white px-4 py-2">
                <Truck className="h-4 w-4 mr-2" />
                Free Return Shipping
              </Badge>
              <Badge className="bg-white/20 text-white px-4 py-2">
                <CheckCircle className="h-4 w-4 mr-2" />
                Easy Process
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Start Return Form */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl text-center">Start Your Return</CardTitle>
                <CardDescription className="text-center">
                  Fill out the form below to begin your return process
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Order Number */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Order Number *
                  </label>
                  <input
                    type="text"
                    value={orderNumber}
                    onChange={(e) => setOrderNumber(e.target.value)}
                    placeholder="Enter your order number (e.g., ORD-2024-001)"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>

                {/* Return Reason */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    Reason for Return *
                  </label>
                  <div className="space-y-3">
                    {returnReasons.map((reason) => (
                      <div
                        key={reason.id}
                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                          selectedReason === reason.id
                            ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                            : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                        }`}
                        onClick={() => setSelectedReason(reason.id)}
                      >
                        <div className="flex items-center space-x-3">
                          <input
                            type="radio"
                            checked={selectedReason === reason.id}
                            onChange={() => setSelectedReason(reason.id)}
                            className="text-primary-600 focus:ring-primary-500"
                          />
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">
                              {reason.label}
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {reason.description}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <Button onClick={handleStartReturn} className="w-full">
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Start Return Process
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Return Process */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              How Returns Work
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Our simple 5-step return process makes it easy to return items you're not satisfied with
            </p>
          </div>

          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
              {returnProcess.map((step, index) => {
                const Icon = step.icon
                return (
                  <div key={step.step} className="text-center">
                    <div className="relative">
                      <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Icon className="h-8 w-8 text-primary-600 dark:text-primary-400" />
                      </div>
                      <div className="absolute -top-2 -right-2 w-6 h-6 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                        {step.step}
                      </div>
                      {index < returnProcess.length - 1 && (
                        <div className="hidden md:block absolute top-8 left-full w-full h-0.5 bg-gray-200 dark:bg-gray-700 -translate-x-1/2" />
                      )}
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      {step.title}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      {step.description}
                    </p>
                    <Badge variant="outline" className="text-xs">
                      {step.timeframe}
                    </Badge>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Return Policies by Category */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Return Policies by Category
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Different product categories have specific return timeframes and conditions
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {returnPolicies.map((policy) => {
              const Icon = policy.icon
              return (
                <Card key={policy.category}>
                  <CardContent className="p-6 text-center">
                    <div className={`w-12 h-12 ${policy.color} rounded-lg flex items-center justify-center mx-auto mb-4`}>
                      <Icon className="h-6 w-6" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      {policy.category}
                    </h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Timeframe:</span>
                        <span className="font-medium">{policy.timeframe}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Condition:</span>
                        <span className="font-medium">{policy.condition}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Fee:</span>
                        <span className="font-medium">{policy.restockingFee}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Common questions about our return and exchange policy
            </p>
          </div>

          <div className="max-w-3xl mx-auto space-y-4">
            {faqs.map((faq, index) => (
              <Card key={index}>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                    <HelpCircle className="h-5 w-5 text-primary-600 mr-2" />
                    {faq.question}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                    {faq.answer}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Support */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Need Help with Your Return?
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Our customer support team is here to help with any questions about returns or exchanges
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <Card className="text-center">
              <CardContent className="p-6">
                <Phone className="h-12 w-12 text-primary-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Call Support
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  Mon-Fri, 9AM-6PM EST
                </p>
                <Button variant="outline" className="w-full">
                  +****************
                </Button>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="p-6">
                <Mail className="h-12 w-12 text-primary-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Email Support
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  Response within 4 hours
                </p>
                <Button variant="outline" className="w-full">
                  <EMAIL>
                </Button>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="p-6">
                <Package className="h-12 w-12 text-primary-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Live Chat
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  24/7 AI Assistant
                </p>
                <Button variant="outline" className="w-full">
                  Start Chat
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Important Notes */}
      <section className="py-16 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-start space-x-4">
              <AlertTriangle className="h-6 w-6 text-amber-600 dark:text-amber-400 flex-shrink-0 mt-1" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Important Return Information
                </h3>
                <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <li>• Items must be returned in original condition with all packaging and accessories</li>
                  <li>• Custom or personalized items cannot be returned unless defective</li>
                  <li>• Software and digital products are non-returnable once downloaded</li>
                  <li>• Return shipping is free for defective items or our errors</li>
                  <li>• Refunds are processed to the original payment method within 5-7 business days</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
