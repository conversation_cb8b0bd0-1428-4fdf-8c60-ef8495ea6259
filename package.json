{"name": "aidevcommerce", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "dev:no-lint": "DISABLE_ESLINT_PLUGIN=true next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma migrate reset --force", "db:studio": "prisma studio"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@hookform/resolvers": "^5.2.0", "@prisma/client": "^6.12.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/stripe-js": "^7.7.0", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^6.4.17", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.11", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.533.0", "next": "^15.4.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "nodemailer": "^6.10.1", "postcss": "^8.5.6", "prisma": "^6.12.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.61.1", "resend": "^4.7.0", "stripe": "^18.3.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.13", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@types/node": "^20.19.9", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "eslint": "^9.32.0", "eslint-config-next": "^15.4.4", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "description": "This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).", "main": "index.js", "keywords": [], "author": "", "license": "ISC"}