"use client"

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useLanguage } from '@/components/providers/language-provider'
import { useToast } from '@/hooks/use-toast'
import {
  MessageCircle,
  Phone,
  Mail,
  Globe,
  Clock,
  MapPin,
  Star,
  Shield,
  Send,
  Copy,
  ExternalLink,
  Users,
  Package,
  Award,
  CheckCircle,
} from 'lucide-react'

interface Supplier {
  id: string
  name: string
  nameAr?: string
  companyName: string
  contactPerson: string
  email: string
  phone: string
  whatsapp?: string
  wechat?: string
  address: string
  city: string
  province: string
  country: string
  rating: number
  verified: boolean
  trustScore: number
  yearsInBusiness: number
  productsCount: number
  specialties: string[]
  specialtiesAr?: string[]
  responseTime: string
  languages: string[]
  certifications: string[]
  businessHours: string
  timezone: string
}

interface ContactSupplierProps {
  supplier: Supplier
  productId?: string
  productName?: string
  onClose?: () => void
}

export function ContactSupplier({ supplier, productId, productName, onClose }: ContactSupplierProps) {
  const { language, t } = useLanguage()
  const { toast } = useToast()
  const isArabic = language === 'ar'

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    subject: productName ? `Inquiry about ${productName}` : '',
    message: '',
    quantity: '',
    targetPrice: '',
    urgency: 'normal',
  })

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const response = await fetch('/api/communication/contact-supplier', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          supplierId: supplier.id,
          productId,
          ...formData,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to send message')
      }

      toast({
        title: isArabic ? 'تم إرسال الرسالة' : 'Message Sent',
        description: isArabic 
          ? 'تم إرسال رسالتك بنجاح. سيتواصل معك المورد قريباً.'
          : 'Your message has been sent successfully. The supplier will contact you soon.',
      })

      // Reset form
      setFormData({
        name: '',
        email: '',
        phone: '',
        company: '',
        subject: productName ? `Inquiry about ${productName}` : '',
        message: '',
        quantity: '',
        targetPrice: '',
        urgency: 'normal',
      })

      if (onClose) onClose()

    } catch (error) {
      toast({
        title: isArabic ? 'خطأ في الإرسال' : 'Send Error',
        description: isArabic 
          ? 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.'
          : 'An error occurred while sending the message. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const openWhatsApp = () => {
    if (supplier.whatsapp) {
      const message = encodeURIComponent(
        `Hello ${supplier.contactPerson}, I'm interested in your products on Sino-Arab Trade platform. ${
          productName ? `Specifically about: ${productName}` : ''
        }`
      )
      window.open(`https://wa.me/${supplier.whatsapp.replace(/[^0-9]/g, '')}?text=${message}`, '_blank')
    }
  }

  const copyWeChatId = () => {
    if (supplier.wechat) {
      navigator.clipboard.writeText(supplier.wechat)
      toast({
        title: isArabic ? 'تم النسخ' : 'Copied',
        description: isArabic ? 'تم نسخ معرف WeChat' : 'WeChat ID copied to clipboard',
      })
    }
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating)
            ? 'text-yellow-400 fill-current'
            : 'text-gray-300'
        }`}
      />
    ))
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Supplier Information */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="flex items-center">
                {supplier.verified && (
                  <Shield className="h-5 w-5 text-green-500 mr-2" />
                )}
                {isArabic && supplier.nameAr ? supplier.nameAr : supplier.name}
              </CardTitle>
              <CardDescription className="mt-1">
                {supplier.companyName}
              </CardDescription>
              <div className="flex items-center space-x-4 mt-2">
                <div className="flex items-center space-x-1">
                  {renderStars(supplier.rating)}
                  <span className="text-sm font-medium ml-1">{supplier.rating}</span>
                </div>
                <Badge variant="outline">
                  {supplier.yearsInBusiness} {isArabic ? 'سنة خبرة' : 'years experience'}
                </Badge>
                <Badge variant="outline">
                  {supplier.productsCount} {isArabic ? 'منتج' : 'products'}
                </Badge>
              </div>
            </div>
            <div className="text-right">
              <Badge className={supplier.verified ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                {supplier.verified ? (isArabic ? 'موثق' : 'Verified') : (isArabic ? 'غير موثق' : 'Unverified')}
              </Badge>
              <div className="text-sm text-gray-600 mt-1">
                {isArabic ? 'نقاط الثقة:' : 'Trust Score:'} {supplier.trustScore}/100
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Location & Contact Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-sm">
                <MapPin className="h-4 w-4 text-gray-500" />
                <span>{supplier.city}, {supplier.province}, {supplier.country}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                <Clock className="h-4 w-4 text-gray-500" />
                <span>
                  {isArabic ? 'يستجيب خلال:' : 'Responds within:'} {supplier.responseTime}
                </span>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                <Users className="h-4 w-4 text-gray-500" />
                <span>
                  {isArabic ? 'اللغات:' : 'Languages:'} {supplier.languages.join(', ')}
                </span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-sm">
                <Package className="h-4 w-4 text-gray-500" />
                <span>
                  {isArabic ? 'التخصصات:' : 'Specialties:'} 
                  {(isArabic && supplier.specialtiesAr ? supplier.specialtiesAr : supplier.specialties).slice(0, 2).join(', ')}
                </span>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                <Award className="h-4 w-4 text-gray-500" />
                <span>
                  {isArabic ? 'الشهادات:' : 'Certifications:'} {supplier.certifications.slice(0, 3).join(', ')}
                </span>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                <Clock className="h-4 w-4 text-gray-500" />
                <span>
                  {isArabic ? 'ساعات العمل:' : 'Business Hours:'} {supplier.businessHours} ({supplier.timezone})
                </span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Quick Contact Options */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* WhatsApp */}
            {supplier.whatsapp && (
              <Button
                onClick={openWhatsApp}
                className="bg-green-500 hover:bg-green-600 text-white"
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                {isArabic ? 'واتساب' : 'WhatsApp'}
                <ExternalLink className="h-3 w-3 ml-2" />
              </Button>
            )}

            {/* WeChat */}
            {supplier.wechat && (
              <Button
                onClick={copyWeChatId}
                variant="outline"
                className="border-green-500 text-green-600 hover:bg-green-50"
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                WeChat: {supplier.wechat}
                <Copy className="h-3 w-3 ml-2" />
              </Button>
            )}

            {/* Email */}
            <Button
              onClick={() => window.open(`mailto:${supplier.email}`, '_blank')}
              variant="outline"
            >
              <Mail className="h-4 w-4 mr-2" />
              {isArabic ? 'إيميل' : 'Email'}
              <ExternalLink className="h-3 w-3 ml-2" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Contact Form */}
      <Card>
        <CardHeader>
          <CardTitle>
            {isArabic ? 'أرسل استفساراً مفصلاً' : 'Send Detailed Inquiry'}
          </CardTitle>
          <CardDescription>
            {isArabic 
              ? 'املأ النموذج أدناه للحصول على عرض سعر مخصص وتفاصيل المنتج'
              : 'Fill out the form below to get a customized quote and product details'
            }
          </CardDescription>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Personal Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">{isArabic ? 'الاسم الكامل' : 'Full Name'} *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="email">{isArabic ? 'البريد الإلكتروني' : 'Email Address'} *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="phone">{isArabic ? 'رقم الهاتف' : 'Phone Number'}</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="+****************"
                />
              </div>
              <div>
                <Label htmlFor="company">{isArabic ? 'اسم الشركة' : 'Company Name'}</Label>
                <Input
                  id="company"
                  value={formData.company}
                  onChange={(e) => handleInputChange('company', e.target.value)}
                />
              </div>
            </div>

            {/* Inquiry Details */}
            <div>
              <Label htmlFor="subject">{isArabic ? 'موضوع الاستفسار' : 'Inquiry Subject'} *</Label>
              <Input
                id="subject"
                value={formData.subject}
                onChange={(e) => handleInputChange('subject', e.target.value)}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="quantity">{isArabic ? 'الكمية المطلوبة' : 'Required Quantity'}</Label>
                <Input
                  id="quantity"
                  value={formData.quantity}
                  onChange={(e) => handleInputChange('quantity', e.target.value)}
                  placeholder={isArabic ? 'مثال: 1000 قطعة' : 'e.g., 1000 pieces'}
                />
              </div>
              <div>
                <Label htmlFor="targetPrice">{isArabic ? 'السعر المستهدف' : 'Target Price'}</Label>
                <Input
                  id="targetPrice"
                  value={formData.targetPrice}
                  onChange={(e) => handleInputChange('targetPrice', e.target.value)}
                  placeholder={isArabic ? 'مثال: $5.00 للقطعة' : 'e.g., $5.00 per piece'}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="message">{isArabic ? 'تفاصيل الاستفسار' : 'Inquiry Details'} *</Label>
              <Textarea
                id="message"
                value={formData.message}
                onChange={(e) => handleInputChange('message', e.target.value)}
                placeholder={isArabic 
                  ? 'يرجى تقديم تفاصيل حول متطلباتك، المواصفات، الجودة، التعبئة، الشحن، إلخ...'
                  : 'Please provide details about your requirements, specifications, quality, packaging, shipping, etc...'
                }
                rows={6}
                required
              />
            </div>

            {/* Submit Button */}
            <div className="flex items-center justify-between pt-4 border-t">
              <div className="text-sm text-gray-600">
                <CheckCircle className="h-4 w-4 inline mr-1 text-green-500" />
                {isArabic 
                  ? 'سيتم إرسال استفسارك مباشرة للمورد'
                  : 'Your inquiry will be sent directly to the supplier'
                }
              </div>
              <div className="flex space-x-3">
                {onClose && (
                  <Button type="button" variant="outline" onClick={onClose}>
                    {isArabic ? 'إلغاء' : 'Cancel'}
                  </Button>
                )}
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="min-w-[120px]"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      {isArabic ? 'جاري الإرسال...' : 'Sending...'}
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      {isArabic ? 'إرسال الاستفسار' : 'Send Inquiry'}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Response Time Notice */}
      <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <Clock className="h-5 w-5 text-blue-600" />
            <div>
              <h4 className="font-medium text-blue-900 dark:text-blue-100">
                {isArabic ? 'وقت الاستجابة المتوقع' : 'Expected Response Time'}
              </h4>
              <p className="text-sm text-blue-700 dark:text-blue-200">
                {isArabic 
                  ? `عادة ما يستجيب ${supplier.contactPerson} خلال ${supplier.responseTime}. ساعات العمل: ${supplier.businessHours} (${supplier.timezone})`
                  : `${supplier.contactPerson} typically responds within ${supplier.responseTime}. Business hours: ${supplier.businessHours} (${supplier.timezone})`
                }
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
