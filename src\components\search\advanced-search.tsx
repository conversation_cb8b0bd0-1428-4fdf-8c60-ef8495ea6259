"use client"

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Slider } from '@/components/ui/slider'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useLanguage } from '@/components/providers/language-provider'
import {
  Search,
  Filter,
  X,
  MapPin,
  Star,
  Package,
  Truck,
  Shield,
  Factory,
  Globe,
  DollarSign,
  Clock,
  Users,
} from 'lucide-react'

interface SearchFilters {
  query: string
  category: string
  priceRange: [number, number]
  moqRange: [number, number]
  productType: string[]
  supplierLocation: string
  supplierRating: number
  inStock: boolean
  verified: boolean
  leadTime: string
  certifications: string[]
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

const defaultFilters: SearchFilters = {
  query: '',
  category: '',
  priceRange: [0, 10000],
  moqRange: [1, 1000],
  productType: [],
  supplierLocation: '',
  supplierRating: 0,
  inStock: false,
  verified: false,
  leadTime: '',
  certifications: [],
  sortBy: 'relevance',
  sortOrder: 'desc',
}

const categories = [
  { id: 'electronics-technology', name: 'Electronics & Technology', nameAr: 'الإلكترونيات والتكنولوجيا' },
  { id: 'textiles-apparel', name: 'Textiles & Apparel', nameAr: 'المنسوجات والملابس' },
  { id: 'home-living', name: 'Home & Living', nameAr: 'المنزل والمعيشة' },
  { id: 'industrial-equipment', name: 'Industrial Equipment', nameAr: 'المعدات الصناعية' },
  { id: 'raw-materials', name: 'Raw Materials', nameAr: 'المواد الخام' },
  { id: 'automotive-parts', name: 'Automotive Parts', nameAr: 'قطع غيار السيارات' },
  { id: 'construction-materials', name: 'Construction Materials', nameAr: 'مواد البناء' },
  { id: 'stock-liquidation', name: 'Stock Liquidation', nameAr: 'تصفية المخزون' },
]

const productTypes = [
  { id: 'retail', name: 'Retail Products', nameAr: 'منتجات التجزئة' },
  { id: 'wholesale', name: 'Wholesale Only', nameAr: 'جملة فقط' },
  { id: 'affiliate', name: 'Affiliate Products', nameAr: 'منتجات التسويق بالعمولة' },
  { id: 'liquidation', name: 'Liquidation Deals', nameAr: 'عروض التصفية' },
  { id: 'machinery', name: 'Machinery & Equipment', nameAr: 'الآلات والمعدات' },
  { id: 'raw_material', name: 'Raw Materials', nameAr: 'المواد الخام' },
]

const supplierLocations = [
  { id: 'guangdong', name: 'Guangdong Province', nameAr: 'مقاطعة قوانغدونغ' },
  { id: 'zhejiang', name: 'Zhejiang Province', nameAr: 'مقاطعة تشجيانغ' },
  { id: 'jiangsu', name: 'Jiangsu Province', nameAr: 'مقاطعة جيانغسو' },
  { id: 'shandong', name: 'Shandong Province', nameAr: 'مقاطعة شاندونغ' },
  { id: 'fujian', name: 'Fujian Province', nameAr: 'مقاطعة فوجيان' },
  { id: 'hebei', name: 'Hebei Province', nameAr: 'مقاطعة خبي' },
]

const certifications = [
  { id: 'ce', name: 'CE Certification', nameAr: 'شهادة CE' },
  { id: 'iso9001', name: 'ISO 9001', nameAr: 'ISO 9001' },
  { id: 'iso14001', name: 'ISO 14001', nameAr: 'ISO 14001' },
  { id: 'fda', name: 'FDA Approved', nameAr: 'موافقة FDA' },
  { id: 'saso', name: 'SASO Certified', nameAr: 'شهادة SASO' },
  { id: 'rohs', name: 'RoHS Compliant', nameAr: 'متوافق مع RoHS' },
  { id: 'fcc', name: 'FCC Certified', nameAr: 'شهادة FCC' },
]

const leadTimeOptions = [
  { id: '1-7', name: '1-7 days', nameAr: '1-7 أيام' },
  { id: '8-15', name: '8-15 days', nameAr: '8-15 يوم' },
  { id: '16-30', name: '16-30 days', nameAr: '16-30 يوم' },
  { id: '31-60', name: '31-60 days', nameAr: '31-60 يوم' },
  { id: '60+', name: '60+ days', nameAr: '60+ يوم' },
]

const sortOptions = [
  { id: 'relevance', name: 'Relevance', nameAr: 'الصلة' },
  { id: 'price_low', name: 'Price: Low to High', nameAr: 'السعر: من الأقل للأعلى' },
  { id: 'price_high', name: 'Price: High to Low', nameAr: 'السعر: من الأعلى للأقل' },
  { id: 'moq_low', name: 'MOQ: Low to High', nameAr: 'الحد الأدنى: من الأقل للأعلى' },
  { id: 'rating', name: 'Highest Rated', nameAr: 'الأعلى تقييماً' },
  { id: 'newest', name: 'Newest First', nameAr: 'الأحدث أولاً' },
  { id: 'popular', name: 'Most Popular', nameAr: 'الأكثر شعبية' },
]

interface AdvancedSearchProps {
  onFiltersChange: (filters: SearchFilters) => void
  initialFilters?: Partial<SearchFilters>
}

export function AdvancedSearch({ onFiltersChange, initialFilters }: AdvancedSearchProps) {
  const { language, t } = useLanguage()
  const router = useRouter()
  const searchParams = useSearchParams()
  const isArabic = language === 'ar'

  const [filters, setFilters] = useState<SearchFilters>({
    ...defaultFilters,
    ...initialFilters,
  })

  const [isExpanded, setIsExpanded] = useState(false)

  useEffect(() => {
    // Load filters from URL params
    const urlFilters: Partial<SearchFilters> = {}
    
    if (searchParams.get('q')) urlFilters.query = searchParams.get('q') || ''
    if (searchParams.get('category')) urlFilters.category = searchParams.get('category') || ''
    if (searchParams.get('type')) urlFilters.productType = searchParams.get('type')?.split(',') || []
    if (searchParams.get('location')) urlFilters.supplierLocation = searchParams.get('location') || ''
    if (searchParams.get('verified')) urlFilters.verified = searchParams.get('verified') === 'true'
    if (searchParams.get('instock')) urlFilters.inStock = searchParams.get('instock') === 'true'
    if (searchParams.get('sort')) urlFilters.sortBy = searchParams.get('sort') || 'relevance'

    setFilters(prev => ({ ...prev, ...urlFilters }))
  }, [searchParams])

  const updateFilters = (newFilters: Partial<SearchFilters>) => {
    const updatedFilters = { ...filters, ...newFilters }
    setFilters(updatedFilters)
    onFiltersChange(updatedFilters)
    
    // Update URL
    const params = new URLSearchParams()
    if (updatedFilters.query) params.set('q', updatedFilters.query)
    if (updatedFilters.category) params.set('category', updatedFilters.category)
    if (updatedFilters.productType.length > 0) params.set('type', updatedFilters.productType.join(','))
    if (updatedFilters.supplierLocation) params.set('location', updatedFilters.supplierLocation)
    if (updatedFilters.verified) params.set('verified', 'true')
    if (updatedFilters.inStock) params.set('instock', 'true')
    if (updatedFilters.sortBy !== 'relevance') params.set('sort', updatedFilters.sortBy)

    router.push(`?${params.toString()}`, { scroll: false })
  }

  const clearFilters = () => {
    setFilters(defaultFilters)
    onFiltersChange(defaultFilters)
    router.push('?', { scroll: false })
  }

  const activeFiltersCount = Object.entries(filters).filter(([key, value]) => {
    if (key === 'query' && value) return true
    if (key === 'category' && value) return true
    if (key === 'productType' && Array.isArray(value) && value.length > 0) return true
    if (key === 'supplierLocation' && value) return true
    if (key === 'verified' && value) return true
    if (key === 'inStock' && value) return true
    if (key === 'leadTime' && value) return true
    if (key === 'certifications' && Array.isArray(value) && value.length > 0) return true
    return false
  }).length

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <Search className="h-5 w-5 mr-2" />
            {isArabic ? 'بحث متقدم' : 'Advanced Search'}
          </CardTitle>
          <div className="flex items-center space-x-2">
            {activeFiltersCount > 0 && (
              <Badge variant="secondary">
                {activeFiltersCount} {isArabic ? 'مرشح نشط' : 'active filters'}
              </Badge>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <Filter className="h-4 w-4 mr-2" />
              {isExpanded ? (isArabic ? 'إخفاء' : 'Hide') : (isArabic ? 'إظهار' : 'Show')}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Main Search */}
        <div className="flex space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder={isArabic ? 'ابحث عن المنتجات، الموردين، أو الكلمات المفتاحية...' : 'Search products, suppliers, or keywords...'}
              value={filters.query}
              onChange={(e) => updateFilters({ query: e.target.value })}
              className="pl-10"
            />
          </div>
          <Select value={filters.sortBy} onValueChange={(value) => updateFilters({ sortBy: value })}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map((option) => (
                <SelectItem key={option.id} value={option.id}>
                  {isArabic ? option.nameAr : option.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Quick Filters */}
        <div className="flex flex-wrap gap-2">
          <Button
            variant={filters.verified ? "default" : "outline"}
            size="sm"
            onClick={() => updateFilters({ verified: !filters.verified })}
          >
            <Shield className="h-3 w-3 mr-1" />
            {isArabic ? 'موردين موثقين' : 'Verified Suppliers'}
          </Button>
          <Button
            variant={filters.inStock ? "default" : "outline"}
            size="sm"
            onClick={() => updateFilters({ inStock: !filters.inStock })}
          >
            <Package className="h-3 w-3 mr-1" />
            {isArabic ? 'متوفر في المخزون' : 'In Stock'}
          </Button>
          <Button
            variant={filters.productType.includes('liquidation') ? "default" : "outline"}
            size="sm"
            onClick={() => {
              const newTypes = filters.productType.includes('liquidation')
                ? filters.productType.filter(t => t !== 'liquidation')
                : [...filters.productType, 'liquidation']
              updateFilters({ productType: newTypes })
            }}
          >
            <DollarSign className="h-3 w-3 mr-1" />
            {isArabic ? 'عروض التصفية' : 'Liquidation Deals'}
          </Button>
        </div>

        {/* Expanded Filters */}
        {isExpanded && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 pt-4 border-t">
            {/* Category */}
            <div>
              <Label className="text-sm font-medium mb-2 block">
                {isArabic ? 'الفئة' : 'Category'}
              </Label>
              <Select value={filters.category} onValueChange={(value) => updateFilters({ category: value })}>
                <SelectTrigger>
                  <SelectValue placeholder={isArabic ? 'اختر الفئة' : 'Select category'} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">{isArabic ? 'جميع الفئات' : 'All Categories'}</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {isArabic ? category.nameAr : category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Supplier Location */}
            <div>
              <Label className="text-sm font-medium mb-2 block">
                <MapPin className="h-4 w-4 inline mr-1" />
                {isArabic ? 'موقع المورد' : 'Supplier Location'}
              </Label>
              <Select value={filters.supplierLocation} onValueChange={(value) => updateFilters({ supplierLocation: value })}>
                <SelectTrigger>
                  <SelectValue placeholder={isArabic ? 'اختر الموقع' : 'Select location'} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">{isArabic ? 'جميع المواقع' : 'All Locations'}</SelectItem>
                  {supplierLocations.map((location) => (
                    <SelectItem key={location.id} value={location.id}>
                      {isArabic ? location.nameAr : location.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Lead Time */}
            <div>
              <Label className="text-sm font-medium mb-2 block">
                <Clock className="h-4 w-4 inline mr-1" />
                {isArabic ? 'وقت التسليم' : 'Lead Time'}
              </Label>
              <Select value={filters.leadTime} onValueChange={(value) => updateFilters({ leadTime: value })}>
                <SelectTrigger>
                  <SelectValue placeholder={isArabic ? 'اختر المدة' : 'Select timeframe'} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">{isArabic ? 'أي مدة' : 'Any timeframe'}</SelectItem>
                  {leadTimeOptions.map((option) => (
                    <SelectItem key={option.id} value={option.id}>
                      {isArabic ? option.nameAr : option.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Price Range */}
            <div>
              <Label className="text-sm font-medium mb-2 block">
                <DollarSign className="h-4 w-4 inline mr-1" />
                {isArabic ? 'نطاق السعر' : 'Price Range'} (${filters.priceRange[0]} - ${filters.priceRange[1]})
              </Label>
              <Slider
                value={filters.priceRange}
                onValueChange={(value) => updateFilters({ priceRange: value as [number, number] })}
                max={10000}
                min={0}
                step={50}
                className="mt-2"
              />
            </div>

            {/* MOQ Range */}
            <div>
              <Label className="text-sm font-medium mb-2 block">
                <Package className="h-4 w-4 inline mr-1" />
                {isArabic ? 'نطاق الحد الأدنى' : 'MOQ Range'} ({filters.moqRange[0]} - {filters.moqRange[1]} {isArabic ? 'قطعة' : 'pcs'})
              </Label>
              <Slider
                value={filters.moqRange}
                onValueChange={(value) => updateFilters({ moqRange: value as [number, number] })}
                max={1000}
                min={1}
                step={10}
                className="mt-2"
              />
            </div>

            {/* Supplier Rating */}
            <div>
              <Label className="text-sm font-medium mb-2 block">
                <Star className="h-4 w-4 inline mr-1" />
                {isArabic ? 'تقييم المورد' : 'Supplier Rating'} ({filters.supplierRating}+ {isArabic ? 'نجوم' : 'stars'})
              </Label>
              <Slider
                value={[filters.supplierRating]}
                onValueChange={(value) => updateFilters({ supplierRating: value[0] })}
                max={5}
                min={0}
                step={0.5}
                className="mt-2"
              />
            </div>
          </div>
        )}

        {/* Clear Filters */}
        {activeFiltersCount > 0 && (
          <div className="flex justify-between items-center pt-4 border-t">
            <span className="text-sm text-gray-600">
              {activeFiltersCount} {isArabic ? 'مرشح نشط' : 'active filters'}
            </span>
            <Button variant="outline" size="sm" onClick={clearFilters}>
              <X className="h-4 w-4 mr-2" />
              {isArabic ? 'مسح جميع المرشحات' : 'Clear all filters'}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
