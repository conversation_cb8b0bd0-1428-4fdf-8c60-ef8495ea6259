// AIDEVCOMMERCE Database Schema
// Advanced E-commerce Platform with AI Integration

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  USER
  ADMIN
  SUPER_ADMIN
  MODERATOR
}

enum AddressType {
  SHIPPING
  BILLING
}

enum CategoryStatus {
  ACTIVE
  INACTIVE
}

enum ProductStatus {
  DRAFT
  ACTIVE
  ARCHIVED
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PROCESSING
  SUCCEEDED
  FAILED
  CANCELLED
  REFUNDED
}

enum NotificationType {
  ORDER_CONFIRMED
  ORDER_SHIPPED
  ORDER_DELIVERED
  PAYMENT_SUCCEEDED
  PAYMENT_FAILED
  PRODUCT_BACK_IN_STOCK
  NEW_BLOG_POST
  NEWSLETTER
  SYSTEM
}

// User Management
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String
  avatar        String?
  phone         String?
  role          UserRole  @default(USER)
  emailVerified DateTime?
  phoneVerified DateTime?
  password      String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts      Account[]
  sessions      Session[]
  addresses     Address[]
  orders        Order[]
  cartItems     CartItem[]
  wishlistItems WishlistItem[]
  reviews       Review[]
  notifications Notification[]
  blogPosts     BlogPost[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model Address {
  id         String      @id @default(cuid())
  userId     String
  type       AddressType
  firstName  String
  lastName   String
  company    String?
  address1   String
  address2   String?
  city       String
  state      String
  postalCode String
  country    String
  phone      String?
  isDefault  Boolean     @default(false)
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt

  user            User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  shippingOrders  Order[] @relation("ShippingAddress")
  billingOrders   Order[] @relation("BillingAddress")

  @@map("addresses")
}

// Product Management
model Category {
  id          String    @id @default(cuid())
  name        String
  slug        String    @unique
  description String?
  image       String?
  parentId    String?
  seoTitle    String?
  seoDescription String?
  status      CategoryStatus @default(ACTIVE)
  position    Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")
  products Product[]

  @@map("categories")
}

model Product {
  id               String    @id @default(cuid())
  name             String
  slug             String    @unique
  description      String
  shortDescription String?
  price            Float
  comparePrice     Float?
  cost             Float?
  sku              String    @unique
  barcode          String?
  trackQuantity    Boolean   @default(true)
  quantity         Int?      @default(0)
  weight           Float?
  length           Float?
  width            Float?
  height           Float?
  categoryId       String
  tags             Json
  seoTitle         String?
  seoDescription   String?
  status           ProductStatus @default(DRAFT)
  featured         Boolean   @default(false)
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  category      Category        @relation(fields: [categoryId], references: [id])
  images        ProductImage[]
  variants      ProductVariant[]
  attributes    ProductAttribute[]
  cartItems     CartItem[]
  orderItems    OrderItem[]
  wishlistItems WishlistItem[]
  reviews       Review[]

  @@map("products")
}

model ProductImage {
  id        String   @id @default(cuid())
  productId String
  url       String
  alt       String
  position  Int      @default(0)
  createdAt DateTime @default(now())

  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model ProductVariant {
  id           String   @id @default(cuid())
  productId    String
  name         String
  price        Float
  comparePrice Float?
  sku          String   @unique
  barcode      String?
  quantity     Int?     @default(0)
  image        String?
  options      Json     // Store variant options as JSON
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  product       Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
  cartItems     CartItem[]
  orderItems    OrderItem[]
  wishlistItems WishlistItem[]

  @@map("product_variants")
}

model ProductAttribute {
  id        String @id @default(cuid())
  productId String
  name      String
  value     String

  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_attributes")
}

// Shopping Cart
model CartItem {
  id        String   @id @default(cuid())
  userId    String?
  sessionId String?
  productId String
  variantId String?
  quantity  Int
  price     Float
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user    User?           @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  variant ProductVariant? @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@map("cart_items")
}

// Orders
model Order {
  id              String        @id @default(cuid())
  orderNumber     String        @unique
  userId          String?
  email           String
  status          OrderStatus   @default(PENDING)
  paymentStatus   PaymentStatus @default(PENDING)
  subtotal        Float
  tax             Float         @default(0)
  shipping        Float         @default(0)
  discount        Float         @default(0)
  total           Float
  currency        String        @default("USD")
  shippingAddressId String
  billingAddressId  String
  paymentMethod   String?
  paymentIntentId String?
  notes           String?
  trackingNumber  String?
  shippedAt       DateTime?
  deliveredAt     DateTime?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  user            User?    @relation(fields: [userId], references: [id])
  items           OrderItem[]
  shippingAddress Address  @relation("ShippingAddress", fields: [shippingAddressId], references: [id])
  billingAddress  Address  @relation("BillingAddress", fields: [billingAddressId], references: [id])

  @@map("orders")
}

model OrderItem {
  id        String  @id @default(cuid())
  orderId   String
  productId String
  variantId String?
  quantity  Int
  price     Float
  total     Float
  createdAt DateTime @default(now())

  order   Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product         @relation(fields: [productId], references: [id])
  variant ProductVariant? @relation(fields: [variantId], references: [id])

  @@map("order_items")
}

// Wishlist
model WishlistItem {
  id        String   @id @default(cuid())
  userId    String
  productId String
  variantId String?
  createdAt DateTime @default(now())

  user    User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  variant ProductVariant? @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@unique([userId, productId, variantId])
  @@map("wishlist_items")
}

// Reviews
model Review {
  id        String   @id @default(cuid())
  userId    String
  productId String
  rating    Int      // 1-5 stars
  title     String?
  content   String
  images    Json
  verified  Boolean  @default(false)
  helpful   Int      @default(0)
  status    String   @default("pending") // pending, approved, rejected
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("reviews")
}

// Services
model ServiceCategory {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  image       String?
  status      CategoryStatus @default(ACTIVE)
  position    Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  services Service[]

  @@map("service_categories")
}

model Service {
  id               String   @id @default(cuid())
  name             String
  slug             String   @unique
  description      String
  shortDescription String?
  price            Float?
  duration         Int?     // in minutes
  categoryId       String
  features         Json
  images           Json
  status           CategoryStatus @default(ACTIVE)
  featured         Boolean  @default(false)
  seoTitle         String?
  seoDescription   String?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  category ServiceCategory @relation(fields: [categoryId], references: [id])

  @@map("services")
}

// Production Lines
model ProductionLineCategory {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  image       String?
  status      CategoryStatus @default(ACTIVE)
  position    Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  productionLines ProductionLine[]

  @@map("production_line_categories")
}

model ProductionLine {
  id               String   @id @default(cuid())
  name             String
  slug             String   @unique
  description      String
  shortDescription String?
  specifications   Json     // Store specifications as JSON
  images           Json
  videos           Json
  brochure         String?
  categoryId       String
  status           CategoryStatus @default(ACTIVE)
  featured         Boolean  @default(false)
  seoTitle         String?
  seoDescription   String?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  category ProductionLineCategory @relation(fields: [categoryId], references: [id])

  @@map("production_lines")
}

// Blog
model BlogCategory {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  image       String?
  status      CategoryStatus @default(ACTIVE)
  position    Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  posts BlogPost[]

  @@map("blog_categories")
}

model BlogPost {
  id             String   @id @default(cuid())
  title          String
  slug           String   @unique
  excerpt        String?
  content        String
  featuredImage  String?
  images         Json
  authorId       String
  categoryId     String
  tags           Json
  status         String   @default("draft") // draft, published, archived
  featured       Boolean  @default(false)
  publishedAt    DateTime?
  seoTitle       String?
  seoDescription String?
  readingTime    Int?
  views          Int      @default(0)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  author   User         @relation(fields: [authorId], references: [id])
  category BlogCategory @relation(fields: [categoryId], references: [id])

  @@map("blog_posts")
}

// Notifications
model Notification {
  id        String           @id @default(cuid())
  userId    String
  type      NotificationType
  title     String
  message   String
  data      Json?            // Additional data as JSON
  read      Boolean          @default(false)
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

// Newsletter
model NewsletterSubscriber {
  id          String   @id @default(cuid())
  email       String   @unique
  name        String?
  status      String   @default("active") // active, unsubscribed
  subscribedAt DateTime @default(now())
  unsubscribedAt DateTime?

  @@map("newsletter_subscribers")
}

// Contact Forms
model ContactSubmission {
  id        String   @id @default(cuid())
  name      String
  email     String
  phone     String?
  subject   String
  message   String
  status    String   @default("new") // new, read, replied
  createdAt DateTime @default(now())

  @@map("contact_submissions")
}

// Analytics
model PageView {
  id        String   @id @default(cuid())
  path      String
  userAgent String?
  ip        String?
  country   String?
  city      String?
  createdAt DateTime @default(now())

  @@map("page_views")
}

model SearchQuery {
  id        String   @id @default(cuid())
  query     String
  results   Int      @default(0)
  userId    String?
  createdAt DateTime @default(now())

  @@map("search_queries")
}
