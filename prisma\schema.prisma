// AIDEVCOMMERCE Database Schema
// Advanced E-commerce Platform with AI Integration

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  USER
  ADMIN
  SUPER_ADMIN
  MODERATOR
}

enum AddressType {
  SHIPPING
  BILLING
}

enum CategoryStatus {
  ACTIVE
  INACTIVE
}

enum ProductStatus {
  DRAFT
  ACTIVE
  ARCHIVED
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PROCESSING
  SUCCEEDED
  FAILED
  CANCELLED
  REFUNDED
}

enum NotificationType {
  ORDER_CONFIRMED
  ORDER_SHIPPED
  ORDER_DELIVERED
  PAYMENT_SUCCEEDED
  PAYMENT_FAILED
  PRODUCT_BACK_IN_STOCK
  NEW_BLOG_POST
  NEWSLETTER
  SYSTEM
}

// Wholesale & Services Enums
enum ServiceType {
  INSPECTION
  STORAGE
  SHIPPING
  CERTIFICATION
  CONSULTING
  QUALITY_CONTROL
  CUSTOMS_CLEARANCE
  DOCUMENTATION
}

enum ServiceStatus {
  PENDING
  ASSIGNED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  ON_HOLD
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum InquiryStatus {
  PENDING
  QUOTED
  NEGOTIATING
  ACCEPTED
  REJECTED
  EXPIRED
}

// User Management
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String
  avatar        String?
  phone         String?
  role          UserRole  @default(USER)
  emailVerified DateTime?
  phoneVerified DateTime?
  password      String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts         Account[]
  sessions         Session[]
  addresses        Address[]
  orders           Order[]
  cartItems        CartItem[]
  wishlistItems    WishlistItem[]
  reviews          Review[]
  notifications    Notification[]
  merchantProfile  MerchantProfile?
  serviceRequests  ServiceRequest[]
  productInquiries ProductInquiry[]
  blogPosts     BlogPost[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model Address {
  id         String      @id @default(cuid())
  userId     String
  type       AddressType
  firstName  String
  lastName   String
  company    String?
  address1   String
  address2   String?
  city       String
  state      String
  postalCode String
  country    String
  phone      String?
  isDefault  Boolean     @default(false)
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt

  user            User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  shippingOrders  Order[] @relation("ShippingAddress")
  billingOrders   Order[] @relation("BillingAddress")

  @@map("addresses")
}

// Product Management
model Category {
  id          String    @id @default(cuid())
  name        String
  slug        String    @unique
  description String?
  image       String?
  parentId    String?
  seoTitle    String?
  seoDescription String?
  status      CategoryStatus @default(ACTIVE)
  position    Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")
  products Product[]

  @@map("categories")
}

model Product {
  id               String    @id @default(cuid())
  name             String
  nameAr           String?   // Arabic name
  slug             String    @unique
  description      String
  descriptionAr    String?   // Arabic description
  shortDescription String?
  shortDescriptionAr String? // Arabic short description

  // Pricing structure
  price            Float
  comparePrice     Float?
  cost             Float?
  wholesalePrice   Float?    // Wholesale price

  // Product identification
  sku              String    @unique
  barcode          String?
  supplierSku      String?   // Supplier's SKU

  // Inventory management
  trackQuantity    Boolean   @default(true)
  quantity         Int?      @default(0)
  minimumOrder     Int       @default(1) // MOQ

  // Physical properties
  weight           Float?
  length           Float?
  width            Float?
  height           Float?

  // Product type and sourcing
  productType      String    @default("retail") // retail, wholesale, affiliate, liquidation, machinery, raw_material
  isAffiliate      Boolean   @default(false)
  affiliateUrl     String?
  origin           String    @default("China")
  brand            String?
  model            String?

  // Categorization
  categoryId       String
  tags             Json

  // SEO
  seoTitle         String?
  seoDescription   String?
  keywords         Json?     // Array of keywords

  // Status and visibility
  status           ProductStatus @default(DRAFT)
  featured         Boolean   @default(false)

  // Timestamps
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  // Relations
  category         Category        @relation(fields: [categoryId], references: [id])
  images           ProductImage[]
  variants         ProductVariant[]
  attributes       ProductAttribute[]
  cartItems        CartItem[]
  orderItems       OrderItem[]
  wishlistItems    WishlistItem[]
  reviews          Review[]
  wholesaleProduct WholesaleProduct?
  inquiries        ProductInquiry[]

  @@map("products")
}

model ProductImage {
  id        String   @id @default(cuid())
  productId String
  url       String
  alt       String
  position  Int      @default(0)
  createdAt DateTime @default(now())

  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model ProductVariant {
  id           String   @id @default(cuid())
  productId    String
  name         String
  price        Float
  comparePrice Float?
  sku          String   @unique
  barcode      String?
  quantity     Int?     @default(0)
  image        String?
  options      Json     // Store variant options as JSON
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  product       Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
  cartItems     CartItem[]
  orderItems    OrderItem[]
  wishlistItems WishlistItem[]

  @@map("product_variants")
}

model ProductAttribute {
  id        String @id @default(cuid())
  productId String
  name      String
  value     String

  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_attributes")
}

// Shopping Cart
model CartItem {
  id        String   @id @default(cuid())
  userId    String?
  sessionId String?
  productId String
  variantId String?
  quantity  Int
  price     Float
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user    User?           @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  variant ProductVariant? @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@map("cart_items")
}

// Orders
model Order {
  id              String        @id @default(cuid())
  orderNumber     String        @unique
  userId          String?
  email           String
  status          OrderStatus   @default(PENDING)
  paymentStatus   PaymentStatus @default(PENDING)
  subtotal        Float
  tax             Float         @default(0)
  shipping        Float         @default(0)
  discount        Float         @default(0)
  total           Float
  currency        String        @default("USD")
  shippingAddressId String
  billingAddressId  String
  paymentMethod   String?
  paymentIntentId String?
  notes           String?
  trackingNumber  String?
  shippedAt       DateTime?
  deliveredAt     DateTime?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  user            User?    @relation(fields: [userId], references: [id])
  items           OrderItem[]
  shippingAddress Address  @relation("ShippingAddress", fields: [shippingAddressId], references: [id])
  billingAddress  Address  @relation("BillingAddress", fields: [billingAddressId], references: [id])

  @@map("orders")
}

model OrderItem {
  id        String  @id @default(cuid())
  orderId   String
  productId String
  variantId String?
  quantity  Int
  price     Float
  total     Float
  createdAt DateTime @default(now())

  order   Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product         @relation(fields: [productId], references: [id])
  variant ProductVariant? @relation(fields: [variantId], references: [id])

  @@map("order_items")
}

// Wishlist
model WishlistItem {
  id        String   @id @default(cuid())
  userId    String
  productId String
  variantId String?
  createdAt DateTime @default(now())

  user    User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  variant ProductVariant? @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@unique([userId, productId, variantId])
  @@map("wishlist_items")
}

// Reviews
model Review {
  id        String   @id @default(cuid())
  userId    String
  productId String
  rating    Int      // 1-5 stars
  title     String?
  content   String
  images    Json
  verified  Boolean  @default(false)
  helpful   Int      @default(0)
  status    String   @default("pending") // pending, approved, rejected
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("reviews")
}

// Services
model ServiceCategory {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  image       String?
  status      CategoryStatus @default(ACTIVE)
  position    Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  services Service[]

  @@map("service_categories")
}

model Service {
  id               String   @id @default(cuid())
  name             String
  slug             String   @unique
  description      String
  shortDescription String?
  price            Float?
  duration         Int?     // in minutes
  categoryId       String
  features         Json
  images           Json
  status           CategoryStatus @default(ACTIVE)
  featured         Boolean  @default(false)
  seoTitle         String?
  seoDescription   String?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  category ServiceCategory @relation(fields: [categoryId], references: [id])

  @@map("services")
}

// Production Lines
model ProductionLineCategory {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  image       String?
  status      CategoryStatus @default(ACTIVE)
  position    Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  productionLines ProductionLine[]

  @@map("production_line_categories")
}

model ProductionLine {
  id               String   @id @default(cuid())
  name             String
  slug             String   @unique
  description      String
  shortDescription String?
  specifications   Json     // Store specifications as JSON
  images           Json
  videos           Json
  brochure         String?
  categoryId       String
  status           CategoryStatus @default(ACTIVE)
  featured         Boolean  @default(false)
  seoTitle         String?
  seoDescription   String?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  category ProductionLineCategory @relation(fields: [categoryId], references: [id])

  @@map("production_lines")
}

// Blog
model BlogCategory {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  image       String?
  status      CategoryStatus @default(ACTIVE)
  position    Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  posts BlogPost[]

  @@map("blog_categories")
}

model BlogPost {
  id             String   @id @default(cuid())
  title          String
  slug           String   @unique
  excerpt        String?
  content        String
  featuredImage  String?
  images         Json
  authorId       String
  categoryId     String
  tags           Json
  status         String   @default("draft") // draft, published, archived
  featured       Boolean  @default(false)
  publishedAt    DateTime?
  seoTitle       String?
  seoDescription String?
  readingTime    Int?
  views          Int      @default(0)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  author   User         @relation(fields: [authorId], references: [id])
  category BlogCategory @relation(fields: [categoryId], references: [id])

  @@map("blog_posts")
}

// Notifications
model Notification {
  id        String           @id @default(cuid())
  userId    String
  type      NotificationType
  title     String
  message   String
  data      Json?            // Additional data as JSON
  read      Boolean          @default(false)
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

// ============================================================================
// WHOLESALE & SERVICES BUSINESS MODELS
// ============================================================================

// Supplier Management
model Supplier {
  id              String   @id @default(cuid())
  name            String
  nameAr          String?  // Arabic name
  companyName     String
  contactPerson   String
  email           String   @unique
  phone           String
  whatsapp        String?
  wechat          String?

  // Address information
  address         String
  city            String
  province        String
  country         String   @default("China")
  postalCode      String?

  // Business information
  businessLicense String?
  taxId           String?
  bankAccount     String?
  paymentTerms    String?

  // Ratings and verification
  rating          Float?   @default(0)
  verified        Boolean  @default(false)
  trustScore      Int      @default(0)

  // Status
  status          String   @default("active") // active, inactive, suspended

  // Timestamps
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  wholesaleProducts WholesaleProduct[]
  serviceRequests   ServiceRequest[]

  @@map("suppliers")
}

// Enhanced Product Model for Wholesale Business
model WholesaleProduct {
  id              String   @id @default(cuid())
  productId       String   @unique

  // Wholesale pricing tiers
  tier1MinQty     Int      @default(1)
  tier1Price      Float
  tier2MinQty     Int?
  tier2Price      Float?
  tier3MinQty     Int?
  tier3Price      Float?

  // Stock liquidation
  isLiquidation   Boolean  @default(false)
  liquidationPrice Float?
  liquidationQty  Int?
  liquidationDeadline DateTime?

  // Affiliate marketing
  isAffiliate     Boolean  @default(false)
  affiliateUrl    String?
  commission      Float?

  // Supplier information
  supplierId      String?
  supplierPrice   Float?
  supplierSku     String?
  leadTime        Int?     // days

  // Minimum order requirements
  moq             Int      @default(1) // Minimum Order Quantity
  packageSize     Int      @default(1)

  // Shipping information
  weight          Float?
  dimensions      Json?    // {length, width, height}
  shippingClass   String?

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  product         Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  supplier        Supplier? @relation(fields: [supplierId], references: [id])

  @@map("wholesale_products")
}

// Service Request System
model ServiceRequest {
  id              String   @id @default(cuid())
  requestNumber   String   @unique

  // Customer information
  customerId      String
  customerName    String
  customerEmail   String
  customerPhone   String
  customerCompany String?

  // Service details
  serviceType     ServiceType
  title           String
  description     String
  priority        Priority @default(MEDIUM)

  // Service-specific fields
  inspectionDetails Json?   // For inspection services
  storageDetails    Json?   // For storage services
  shippingDetails   Json?   // For shipping services
  certificationDetails Json? // For certification services
  consultingDetails Json?   // For consulting services

  // Supplier assignment
  supplierId      String?
  supplierNotes   String?

  // Status and tracking
  status          ServiceStatus @default(PENDING)
  estimatedCost   Float?
  finalCost       Float?
  estimatedDays   Int?

  // Documents and attachments
  attachments     Json?    // Array of file URLs
  documents       Json?    // Generated documents

  // Timeline
  requestedAt     DateTime @default(now())
  assignedAt      DateTime?
  startedAt       DateTime?
  completedAt     DateTime?

  // Internal notes
  internalNotes   String?
  customerNotes   String?

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  customer        User     @relation(fields: [customerId], references: [id])
  supplier        Supplier? @relation(fields: [supplierId], references: [id])
  updates         ServiceUpdate[]

  @@map("service_requests")
}

// Service Request Updates/Timeline
model ServiceUpdate {
  id              String   @id @default(cuid())
  serviceRequestId String

  title           String
  description     String
  status          ServiceStatus
  updatedBy       String   // User ID
  attachments     Json?

  createdAt       DateTime @default(now())

  serviceRequest  ServiceRequest @relation(fields: [serviceRequestId], references: [id], onDelete: Cascade)

  @@map("service_updates")
}

// Product Inquiries for Wholesale
model ProductInquiry {
  id              String   @id @default(cuid())
  inquiryNumber   String   @unique

  // Customer information
  customerId      String
  customerName    String
  customerEmail   String
  customerPhone   String
  customerCompany String?

  // Product information
  productId       String
  requestedQty    Int
  targetPrice     Float?
  specifications  String?

  // Status
  status          InquiryStatus @default(PENDING)

  // Response
  quotedPrice     Float?
  quotedQty       Int?
  leadTime        Int?
  validUntil      DateTime?

  // Notes
  customerNotes   String?
  supplierNotes   String?
  internalNotes   String?

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  customer        User     @relation(fields: [customerId], references: [id])
  product         Product  @relation(fields: [productId], references: [id])

  @@map("product_inquiries")
}

// Enhanced User Profile for Arab Merchants
model MerchantProfile {
  id              String   @id @default(cuid())
  userId          String   @unique

  // Business information
  companyName     String
  companyNameAr   String?
  businessType    String   // importer, distributor, retailer, etc.
  establishedYear Int?

  // Contact information
  contactPerson   String
  position        String?
  whatsapp        String?
  wechat          String?
  telegram        String?

  // Business address
  businessAddress String
  city            String
  country         String
  postalCode      String?

  // Business verification
  businessLicense String?
  taxCertificate  String?
  importLicense   String?
  verified        Boolean  @default(false)

  // Preferences
  preferredLanguage String @default("en") // en, ar
  currency        String   @default("USD")
  paymentTerms    String?

  // Business metrics
  totalOrders     Int      @default(0)
  totalSpent      Float    @default(0)
  creditLimit     Float?

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("merchant_profiles")
}

// Newsletter
model NewsletterSubscriber {
  id          String   @id @default(cuid())
  email       String   @unique
  name        String?
  status      String   @default("active") // active, unsubscribed
  subscribedAt DateTime @default(now())
  unsubscribedAt DateTime?

  @@map("newsletter_subscribers")
}

// Contact Forms
model ContactSubmission {
  id        String   @id @default(cuid())
  name      String
  email     String
  phone     String?
  subject   String
  message   String
  status    String   @default("new") // new, read, replied
  createdAt DateTime @default(now())

  @@map("contact_submissions")
}

// Analytics
model PageView {
  id        String   @id @default(cuid())
  path      String
  userAgent String?
  ip        String?
  country   String?
  city      String?
  createdAt DateTime @default(now())

  @@map("page_views")
}

model SearchQuery {
  id        String   @id @default(cuid())
  query     String
  results   Int      @default(0)
  userId    String?
  createdAt DateTime @default(now())

  @@map("search_queries")
}
