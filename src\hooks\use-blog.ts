import { useQuery } from '@tanstack/react-query'

export interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt: string
  content: string
  featuredImage?: string
  images: string[]
  authorId: string
  categoryId: string
  tags: string[]
  status: string
  featured: boolean
  publishedAt?: string
  seoTitle?: string
  seoDescription?: string
  readingTime?: number
  views: number
  createdAt: string
  updatedAt: string
  author: {
    id: string
    name: string
    avatar?: string
  }
  category: {
    id: string
    name: string
    slug: string
  }
  relatedPosts?: BlogPost[]
}

export interface BlogResponse {
  posts: BlogPost[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface BlogParams {
  page?: number
  limit?: number
  category?: string
  search?: string
  featured?: boolean
  status?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export function useBlogPosts(params: BlogParams = {}) {
  const queryParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, value.toString())
    }
  })

  return useQuery<BlogResponse>({
    queryKey: ['blog-posts', params],
    queryFn: async () => {
      const response = await fetch(`/api/blog?${queryParams.toString()}`)
      if (!response.ok) {
        throw new Error('Failed to fetch blog posts')
      }
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useBlogPost(slug: string) {
  return useQuery<BlogPost>({
    queryKey: ['blog-post', slug],
    queryFn: async () => {
      const response = await fetch(`/api/blog/${slug}`)
      if (!response.ok) {
        throw new Error('Failed to fetch blog post')
      }
      return response.json()
    },
    enabled: !!slug,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useFeaturedBlogPosts(limit: number = 4) {
  return useBlogPosts({
    featured: true,
    limit,
    status: 'published',
    sortBy: 'publishedAt',
    sortOrder: 'desc'
  })
}

export function useLatestBlogPosts(limit: number = 6) {
  return useBlogPosts({
    limit,
    status: 'published',
    sortBy: 'publishedAt',
    sortOrder: 'desc'
  })
}
