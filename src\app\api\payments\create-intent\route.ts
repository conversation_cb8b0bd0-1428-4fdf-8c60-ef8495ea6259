import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { createPaymentIntent, createStripeCustomer } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { 
      amount, 
      currency = 'usd', 
      items, 
      shippingAddress,
      billingAddress 
    } = body

    // Validate required fields
    if (!amount || !items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json(
        { error: 'Missing required fields: amount, items' },
        { status: 400 }
      )
    }

    // Validate amount
    if (typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'Invalid amount' },
        { status: 400 }
      )
    }

    // Get or create Stripe customer
    let stripeCustomerId = session.user.stripeCustomerId

    if (!stripeCustomerId) {
      // Create new Stripe customer
      const stripeCustomer = await createStripeCustomer({
        email: session.user.email!,
        name: session.user.name || undefined,
        address: shippingAddress ? {
          line1: shippingAddress.address,
          line2: shippingAddress.address2 || undefined,
          city: shippingAddress.city,
          state: shippingAddress.state,
          postal_code: shippingAddress.zipCode,
          country: shippingAddress.country || 'US',
        } : undefined,
      })

      stripeCustomerId = stripeCustomer.id

      // Update user with Stripe customer ID
      await prisma.user.update({
        where: { id: session.user.id },
        data: { stripeCustomerId },
      })
    }

    // Create payment intent
    const paymentIntentData = {
      amount,
      currency,
      customer: stripeCustomerId,
      description: `Order for ${items.length} item(s)`,
      metadata: {
        userId: session.user.id,
        itemCount: items.length.toString(),
        items: JSON.stringify(items.map((item: any) => ({
          id: item.id,
          name: item.name,
          quantity: item.quantity,
          price: item.price,
        }))),
      },
      shipping: shippingAddress ? {
        name: shippingAddress.name || session.user.name || 'Customer',
        address: {
          line1: shippingAddress.address,
          line2: shippingAddress.address2 || undefined,
          city: shippingAddress.city,
          state: shippingAddress.state,
          postal_code: shippingAddress.zipCode,
          country: shippingAddress.country || 'US',
        },
      } : undefined,
    }

    const { clientSecret, paymentIntentId } = await createPaymentIntent(paymentIntentData)

    // Create order record in database
    const order = await prisma.order.create({
      data: {
        userId: session.user.id,
        status: 'pending',
        total: amount,
        subtotal: amount - (body.shipping || 0) - (body.tax || 0),
        shipping: body.shipping || 0,
        tax: body.tax || 0,
        currency,
        paymentIntentId,
        shippingAddress: shippingAddress ? JSON.stringify(shippingAddress) : null,
        billingAddress: billingAddress ? JSON.stringify(billingAddress) : null,
        items: {
          create: items.map((item: any) => ({
            productId: item.id,
            quantity: item.quantity,
            price: item.price,
            name: item.name,
          })),
        },
      },
      include: {
        items: true,
      },
    })

    return NextResponse.json({
      clientSecret,
      paymentIntentId,
      orderId: order.id,
    })

  } catch (error) {
    console.error('Payment intent creation error:', error)
    return NextResponse.json(
      { error: 'Failed to create payment intent' },
      { status: 500 }
    )
  }
}

// GET endpoint to retrieve payment intent status
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const paymentIntentId = searchParams.get('payment_intent_id')

    if (!paymentIntentId) {
      return NextResponse.json(
        { error: 'Payment intent ID required' },
        { status: 400 }
      )
    }

    // Get payment intent from Stripe
    const { confirmPayment } = await import('@/lib/stripe')
    const paymentIntent = await confirmPayment(paymentIntentId)

    // Update order status based on payment intent status
    if (paymentIntent.status === 'succeeded') {
      await prisma.order.updateMany({
        where: {
          paymentIntentId,
          userId: session.user.id,
        },
        data: {
          status: 'confirmed',
          paidAt: new Date(),
        },
      })
    }

    return NextResponse.json({
      status: paymentIntent.status,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
    })

  } catch (error) {
    console.error('Payment intent retrieval error:', error)
    return NextResponse.json(
      { error: 'Failed to retrieve payment intent' },
      { status: 500 }
    )
  }
}
