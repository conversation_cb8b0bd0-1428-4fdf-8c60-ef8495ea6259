"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { useAuth } from "@/components/providers/auth-provider"
import { useToast } from "@/hooks/use-toast"
import { formatDate } from "@/lib/utils"
import {
  Star,
  ThumbsUp,
  ThumbsDown,
  MessageCircle,
  Camera,
  Video,
  Shield,
  CheckCircle,
  Flag,
  Filter,
  SortDesc,
  Image as ImageIcon,
  User,
  Calendar,
  Award,
} from "lucide-react"

interface ReviewImage {
  id: string
  url: string
  thumbnail: string
  alt: string
}

interface Review {
  id: string
  userId: string
  userName: string
  userAvatar?: string
  rating: number
  title: string
  content: string
  images?: ReviewImage[]
  videos?: string[]
  date: string
  verified: boolean
  helpful: number
  notHelpful: number
  replies?: Review[]
  pros?: string[]
  cons?: string[]
  recommended: boolean
}

interface ReviewStats {
  averageRating: number
  totalReviews: number
  ratingDistribution: { [key: number]: number }
  verifiedPurchases: number
  recommendationRate: number
}

interface ProductReviewsProps {
  productId: string
  reviews: Review[]
  stats: ReviewStats
  onSubmitReview?: (review: Partial<Review>) => void
  onHelpfulVote?: (reviewId: string, helpful: boolean) => void
  onReportReview?: (reviewId: string, reason: string) => void
}

export function ProductReviews({
  productId,
  reviews,
  stats,
  onSubmitReview,
  onHelpfulVote,
  onReportReview
}: ProductReviewsProps) {
  const [showWriteReview, setShowWriteReview] = useState(false)
  const [filterRating, setFilterRating] = useState<number | null>(null)
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'helpful' | 'rating'>('newest')
  const [showVerifiedOnly, setShowVerifiedOnly] = useState(false)
  const [showWithPhotos, setShowWithPhotos] = useState(false)
  
  // New review form state
  const [newReview, setNewReview] = useState({
    rating: 0,
    title: '',
    content: '',
    pros: [''],
    cons: [''],
    recommended: true,
    images: [] as File[]
  })

  const { user } = useAuth()
  const { toast } = useToast()

  const filteredAndSortedReviews = reviews
    .filter(review => {
      if (filterRating && review.rating !== filterRating) return false
      if (showVerifiedOnly && !review.verified) return false
      if (showWithPhotos && (!review.images || review.images.length === 0)) return false
      return true
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.date).getTime() - new Date(a.date).getTime()
        case 'oldest':
          return new Date(a.date).getTime() - new Date(b.date).getTime()
        case 'helpful':
          return b.helpful - a.helpful
        case 'rating':
          return b.rating - a.rating
        default:
          return 0
      }
    })

  const handleSubmitReview = () => {
    if (!user) {
      toast({
        title: "Login Required",
        description: "Please log in to write a review.",
        variant: "destructive"
      })
      return
    }

    if (newReview.rating === 0 || !newReview.title.trim() || !newReview.content.trim()) {
      toast({
        title: "Missing Information",
        description: "Please provide a rating, title, and review content.",
        variant: "destructive"
      })
      return
    }

    onSubmitReview?.({
      ...newReview,
      userId: user.id,
      userName: user.name,
      userAvatar: user.avatar,
      date: new Date().toISOString(),
      verified: true, // Assume verified purchase for demo
      helpful: 0,
      notHelpful: 0,
      pros: newReview.pros.filter(p => p.trim()),
      cons: newReview.cons.filter(c => c.trim())
    })

    // Reset form
    setNewReview({
      rating: 0,
      title: '',
      content: '',
      pros: [''],
      cons: [''],
      recommended: true,
      images: []
    })
    setShowWriteReview(false)

    toast({
      title: "Review Submitted",
      description: "Thank you for your review! It will be published shortly."
    })
  }

  const addProCon = (type: 'pros' | 'cons') => {
    setNewReview(prev => ({
      ...prev,
      [type]: [...prev[type], '']
    }))
  }

  const updateProCon = (type: 'pros' | 'cons', index: number, value: string) => {
    setNewReview(prev => ({
      ...prev,
      [type]: prev[type].map((item, i) => i === index ? value : item)
    }))
  }

  const removeProCon = (type: 'pros' | 'cons', index: number) => {
    setNewReview(prev => ({
      ...prev,
      [type]: prev[type].filter((_, i) => i !== index)
    }))
  }

  const StarRating = ({ rating, size = 'sm', interactive = false, onRatingChange }: {
    rating: number
    size?: 'sm' | 'md' | 'lg'
    interactive?: boolean
    onRatingChange?: (rating: number) => void
  }) => {
    const sizeClasses = {
      sm: 'h-4 w-4',
      md: 'h-5 w-5',
      lg: 'h-6 w-6'
    }

    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            disabled={!interactive}
            onClick={() => interactive && onRatingChange?.(star)}
            className={`${interactive ? 'cursor-pointer hover:scale-110' : 'cursor-default'} transition-transform`}
          >
            <Star
              className={`${sizeClasses[size]} ${
                star <= rating
                  ? 'text-yellow-400 fill-current'
                  : 'text-gray-300'
              }`}
            />
          </button>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Review Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Customer Reviews</span>
            <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
              <Star className="h-3 w-3 mr-1 fill-current" />
              {stats.averageRating.toFixed(1)} ({stats.totalReviews} reviews)
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Rating Breakdown */}
            <div>
              <h4 className="font-semibold mb-3">Rating Breakdown</h4>
              <div className="space-y-2">
                {[5, 4, 3, 2, 1].map((rating) => (
                  <div key={rating} className="flex items-center space-x-3">
                    <div className="flex items-center space-x-1 w-12">
                      <span className="text-sm">{rating}</span>
                      <Star className="h-3 w-3 text-yellow-400 fill-current" />
                    </div>
                    <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-yellow-400 h-2 rounded-full"
                        style={{
                          width: `${((stats.ratingDistribution[rating] || 0) / stats.totalReviews) * 100}%`
                        }}
                      />
                    </div>
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-8">
                      {stats.ratingDistribution[rating] || 0}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Review Stats */}
            <div>
              <h4 className="font-semibold mb-3">Review Statistics</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Shield className="h-4 w-4 text-green-600" />
                    <span className="text-sm">Verified Purchases</span>
                  </div>
                  <span className="text-sm font-medium">
                    {Math.round((stats.verifiedPurchases / stats.totalReviews) * 100)}%
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Award className="h-4 w-4 text-blue-600" />
                    <span className="text-sm">Recommendation Rate</span>
                  </div>
                  <span className="text-sm font-medium">
                    {Math.round(stats.recommendationRate * 100)}%
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <ImageIcon className="h-4 w-4 text-purple-600" />
                    <span className="text-sm">Reviews with Photos</span>
                  </div>
                  <span className="text-sm font-medium">
                    {reviews.filter(r => r.images && r.images.length > 0).length}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Write Review Button */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Reviews ({filteredAndSortedReviews.length})</h3>
        <Button onClick={() => setShowWriteReview(true)}>
          <MessageCircle className="h-4 w-4 mr-2" />
          Write a Review
        </Button>
      </div>

      {/* Write Review Form */}
      {showWriteReview && (
        <Card>
          <CardHeader>
            <CardTitle>Write Your Review</CardTitle>
            <CardDescription>
              Share your experience with this product to help other customers
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Rating */}
            <div>
              <label className="block text-sm font-medium mb-2">Overall Rating *</label>
              <StarRating
                rating={newReview.rating}
                size="lg"
                interactive
                onRatingChange={(rating) => setNewReview(prev => ({ ...prev, rating }))}
              />
            </div>

            {/* Title */}
            <div>
              <label className="block text-sm font-medium mb-2">Review Title *</label>
              <Input
                placeholder="Summarize your experience"
                value={newReview.title}
                onChange={(e) => setNewReview(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>

            {/* Content */}
            <div>
              <label className="block text-sm font-medium mb-2">Your Review *</label>
              <Textarea
                placeholder="Tell us about your experience with this product..."
                rows={4}
                value={newReview.content}
                onChange={(e) => setNewReview(prev => ({ ...prev, content: e.target.value }))}
              />
            </div>

            {/* Pros */}
            <div>
              <label className="block text-sm font-medium mb-2">What did you like?</label>
              {newReview.pros.map((pro, index) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <Input
                    placeholder="Add a positive point"
                    value={pro}
                    onChange={(e) => updateProCon('pros', index, e.target.value)}
                  />
                  {newReview.pros.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeProCon('pros', index)}
                    >
                      ×
                    </Button>
                  )}
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addProCon('pros')}
              >
                Add Pro
              </Button>
            </div>

            {/* Cons */}
            <div>
              <label className="block text-sm font-medium mb-2">What could be improved?</label>
              {newReview.cons.map((con, index) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <Input
                    placeholder="Add an area for improvement"
                    value={con}
                    onChange={(e) => updateProCon('cons', index, e.target.value)}
                  />
                  {newReview.cons.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeProCon('cons', index)}
                    >
                      ×
                    </Button>
                  )}
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addProCon('cons')}
              >
                Add Con
              </Button>
            </div>

            {/* Recommendation */}
            <div>
              <label className="block text-sm font-medium mb-2">Would you recommend this product?</label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="recommended"
                    checked={newReview.recommended}
                    onChange={() => setNewReview(prev => ({ ...prev, recommended: true }))}
                    className="mr-2"
                  />
                  Yes, I recommend it
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="recommended"
                    checked={!newReview.recommended}
                    onChange={() => setNewReview(prev => ({ ...prev, recommended: false }))}
                    className="mr-2"
                  />
                  No, I don't recommend it
                </label>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={() => setShowWriteReview(false)}>
                Cancel
              </Button>
              <Button onClick={handleSubmitReview}>
                Submit Review
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters and Sorting */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap items-center gap-4">
            {/* Rating Filter */}
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4" />
              <select
                value={filterRating || ''}
                onChange={(e) => setFilterRating(e.target.value ? Number(e.target.value) : null)}
                className="px-3 py-1 border rounded-md bg-background text-sm"
              >
                <option value="">All Ratings</option>
                {[5, 4, 3, 2, 1].map(rating => (
                  <option key={rating} value={rating}>{rating} Stars</option>
                ))}
              </select>
            </div>

            {/* Sort */}
            <div className="flex items-center space-x-2">
              <SortDesc className="h-4 w-4" />
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-1 border rounded-md bg-background text-sm"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="helpful">Most Helpful</option>
                <option value="rating">Highest Rating</option>
              </select>
            </div>

            {/* Filters */}
            <label className="flex items-center space-x-2 text-sm">
              <input
                type="checkbox"
                checked={showVerifiedOnly}
                onChange={(e) => setShowVerifiedOnly(e.target.checked)}
              />
              <span>Verified purchases only</span>
            </label>

            <label className="flex items-center space-x-2 text-sm">
              <input
                type="checkbox"
                checked={showWithPhotos}
                onChange={(e) => setShowWithPhotos(e.target.checked)}
              />
              <span>With photos</span>
            </label>
          </div>
        </CardContent>
      </Card>

      {/* Reviews List */}
      <div className="space-y-4">
        {filteredAndSortedReviews.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                No reviews found
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Try adjusting your filters or be the first to write a review!
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredAndSortedReviews.map((review) => (
            <Card key={review.id}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                      {review.userAvatar ? (
                        <img
                          src={review.userAvatar}
                          alt={review.userName}
                          className="w-10 h-10 rounded-full object-cover"
                        />
                      ) : (
                        <User className="h-5 w-5 text-gray-500" />
                      )}
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{review.userName}</span>
                        {review.verified && (
                          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 text-xs">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Verified Purchase
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                        <Calendar className="h-3 w-3" />
                        <span>{formatDate(review.date)}</span>
                      </div>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    <Flag className="h-4 w-4" />
                  </Button>
                </div>

                {/* Rating and Title */}
                <div className="mb-3">
                  <div className="flex items-center space-x-3 mb-2">
                    <StarRating rating={review.rating} />
                    {review.recommended && (
                      <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 text-xs">
                        Recommended
                      </Badge>
                    )}
                  </div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">
                    {review.title}
                  </h4>
                </div>

                {/* Review Content */}
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  {review.content}
                </p>

                {/* Pros and Cons */}
                {(review.pros?.length || review.cons?.length) && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    {review.pros && review.pros.length > 0 && (
                      <div>
                        <h5 className="font-medium text-green-700 dark:text-green-400 mb-2">
                          Pros:
                        </h5>
                        <ul className="space-y-1">
                          {review.pros.map((pro, index) => (
                            <li key={index} className="flex items-start space-x-2 text-sm">
                              <ThumbsUp className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>{pro}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {review.cons && review.cons.length > 0 && (
                      <div>
                        <h5 className="font-medium text-red-700 dark:text-red-400 mb-2">
                          Cons:
                        </h5>
                        <ul className="space-y-1">
                          {review.cons.map((con, index) => (
                            <li key={index} className="flex items-start space-x-2 text-sm">
                              <ThumbsDown className="h-3 w-3 text-red-600 mt-0.5 flex-shrink-0" />
                              <span>{con}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}

                {/* Review Images */}
                {review.images && review.images.length > 0 && (
                  <div className="flex space-x-2 mb-4">
                    {review.images.map((image) => (
                      <img
                        key={image.id}
                        src={image.thumbnail}
                        alt={image.alt}
                        className="w-16 h-16 object-cover rounded-lg cursor-pointer hover:opacity-80"
                      />
                    ))}
                  </div>
                )}

                {/* Helpful Votes */}
                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={() => onHelpfulVote?.(review.id, true)}
                      className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400 hover:text-green-600"
                    >
                      <ThumbsUp className="h-4 w-4" />
                      <span>Helpful ({review.helpful})</span>
                    </button>
                    <button
                      onClick={() => onHelpfulVote?.(review.id, false)}
                      className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400 hover:text-red-600"
                    >
                      <ThumbsDown className="h-4 w-4" />
                      <span>Not Helpful ({review.notHelpful})</span>
                    </button>
                  </div>
                  <span className="text-xs text-gray-500">
                    Was this review helpful?
                  </span>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Load More */}
      {filteredAndSortedReviews.length > 0 && (
        <div className="text-center">
          <Button variant="outline">
            Load More Reviews
          </Button>
        </div>
      )}
    </div>
  )
}
