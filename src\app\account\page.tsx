"use client"

import Link from "next/link"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { AccountLayout } from "@/components/layout/account-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useLanguage } from "@/components/providers/language-provider"
import { formatPrice, formatDate } from "@/lib/utils"
import {
  Package,
  Heart,
  MapPin,
  CreditCard,
  TrendingUp,
  ShoppingBag,
  Clock,
  CheckCircle,
  Star,
  ArrowRight,
  Bell,
  Gift,
} from "lucide-react"

// Mock data
const mockStats = {
  totalOrders: 12,
  totalSpent: 1299.99,
  wishlistItems: 8,
  savedAddresses: 3,
  loyaltyPoints: 2450,
  memberSince: new Date("2023-06-15"),
}

const mockRecentOrders = [
  {
    id: "ORD-2024-001",
    orderNumber: "ORD-2024-001",
    status: "delivered",
    total: 299.99,
    currency: "USD",
    createdAt: new Date("2024-01-15"),
    itemCount: 3,
  },
  {
    id: "ORD-2024-002",
    orderNumber: "ORD-2024-002",
    status: "shipped",
    total: 89.99,
    currency: "USD",
    createdAt: new Date("2024-01-20"),
    itemCount: 2,
  },
]

const mockWishlistItems = [
  {
    id: "1",
    name: "Premium Wireless Headphones",
    price: 199.99,
    currency: "USD",
    image: "/placeholder-product.jpg",
    inStock: true,
  },
  {
    id: "2",
    name: "Smart Fitness Watch",
    price: 299.99,
    currency: "USD",
    image: "/placeholder-product.jpg",
    inStock: false,
  },
]

const mockNotifications = [
  {
    id: "1",
    type: "order",
    title: "Order Delivered",
    message: "Your order #ORD-2024-001 has been delivered",
    createdAt: new Date("2024-01-18"),
    read: false,
  },
  {
    id: "2",
    type: "promotion",
    title: "Special Offer",
    message: "Get 20% off on your next purchase",
    createdAt: new Date("2024-01-17"),
    read: true,
  },
]

export default function AccountDashboard() {
  const { t } = useLanguage()
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'loading') return // Still loading
    if (!session) {
      router.push('/auth/login')
      return
    }
  }, [session, status, router])

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null // Will redirect to login
  }

  return (
    <AccountLayout>
      <div className="p-6">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Welcome back, {session.user?.name || 'User'}!
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Here's what's happening with your account
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Orders
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {mockStats.totalOrders}
                  </p>
                </div>
                <Package className="h-8 w-8 text-primary-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Spent
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatPrice(mockStats.totalSpent)}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Wishlist Items
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {mockStats.wishlistItems}
                  </p>
                </div>
                <Heart className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Loyalty Points
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {mockStats.loyaltyPoints.toLocaleString()}
                  </p>
                </div>
                <Star className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Orders */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <Package className="h-5 w-5 mr-2" />
                  Recent Orders
                </CardTitle>
                <Link href="/account/orders">
                  <Button variant="ghost" size="sm">
                    View All
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </Link>
              </div>
              <CardDescription>
                Your latest order activity
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockRecentOrders.map((order) => (
                  <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="h-10 w-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                        {order.status === "delivered" ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <Clock className="h-5 w-5 text-blue-500" />
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-sm">#{order.orderNumber}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {order.itemCount} items • {formatDate(order.createdAt)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        {formatPrice(order.total, { currency: order.currency })}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400 capitalize">
                        {order.status}
                      </p>
                    </div>
                  </div>
                ))}
                
                {mockRecentOrders.length === 0 && (
                  <div className="text-center py-8">
                    <ShoppingBag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 dark:text-gray-400">No orders yet</p>
                    <Link href="/shop">
                      <Button className="mt-2">Start Shopping</Button>
                    </Link>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Wishlist Preview */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <Heart className="h-5 w-5 mr-2" />
                  Wishlist
                </CardTitle>
                <Link href="/account/wishlist">
                  <Button variant="ghost" size="sm">
                    View All
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </Link>
              </div>
              <CardDescription>
                Items you've saved for later
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockWishlistItems.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="h-12 w-12 bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center">
                        <Package className="h-6 w-6 text-gray-400" />
                      </div>
                      <div>
                        <p className="font-medium text-sm">{item.name}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {formatPrice(item.price, { currency: item.currency })}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {item.inStock ? (
                        <Button size="sm">Add to Cart</Button>
                      ) : (
                        <Button size="sm" variant="outline" disabled>
                          Out of Stock
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
                
                {mockWishlistItems.length === 0 && (
                  <div className="text-center py-8">
                    <Heart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 dark:text-gray-400">No wishlist items yet</p>
                    <Link href="/shop">
                      <Button className="mt-2">Browse Products</Button>
                    </Link>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Manage your account settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <Link href="/account/profile">
                  <Button variant="outline" className="w-full justify-start">
                    <Package className="h-4 w-4 mr-2" />
                    Edit Profile
                  </Button>
                </Link>
                <Link href="/account/addresses">
                  <Button variant="outline" className="w-full justify-start">
                    <MapPin className="h-4 w-4 mr-2" />
                    Addresses
                  </Button>
                </Link>
                <Link href="/account/payment-methods">
                  <Button variant="outline" className="w-full justify-start">
                    <CreditCard className="h-4 w-4 mr-2" />
                    Payment
                  </Button>
                </Link>
                <Link href="/account/security">
                  <Button variant="outline" className="w-full justify-start">
                    <Package className="h-4 w-4 mr-2" />
                    Security
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          {/* Notifications */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <Bell className="h-5 w-5 mr-2" />
                  Recent Notifications
                </CardTitle>
                <Link href="/account/notifications">
                  <Button variant="ghost" size="sm">
                    View All
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </Link>
              </div>
              <CardDescription>
                Latest updates and alerts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockNotifications.map((notification) => (
                  <div key={notification.id} className={`p-4 border rounded-lg ${!notification.read ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800' : ''}`}>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className="font-medium text-sm">{notification.title}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          {notification.message}
                        </p>
                        <p className="text-xs text-gray-400 dark:text-gray-500 mt-2">
                          {formatDate(notification.createdAt)}
                        </p>
                      </div>
                      {!notification.read && (
                        <div className="h-2 w-2 bg-blue-500 rounded-full ml-2 mt-1"></div>
                      )}
                    </div>
                  </div>
                ))}
                
                {mockNotifications.length === 0 && (
                  <div className="text-center py-8">
                    <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 dark:text-gray-400">No notifications</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Loyalty Program */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Gift className="h-5 w-5 mr-2" />
              Loyalty Program
            </CardTitle>
            <CardDescription>
              You're a valued member since {formatDate(mockStats.memberSince)}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                  {mockStats.loyaltyPoints.toLocaleString()} Points
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  Earn 1 point for every $1 spent
                </p>
              </div>
              <div className="text-right">
                <Button>
                  Redeem Points
                </Button>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  Next reward at 3,000 points
                </p>
              </div>
            </div>
            
            {/* Progress Bar */}
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400 mb-2">
                <span>Current: {mockStats.loyaltyPoints}</span>
                <span>Next Reward: 3,000</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(mockStats.loyaltyPoints / 3000) * 100}%` }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AccountLayout>
  )
}
