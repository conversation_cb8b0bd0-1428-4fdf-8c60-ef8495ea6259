"use client"

import { useState, useEffect, useRef } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import {
  Users,
  ShoppingBag,
  Globe,
  Award,
  TrendingUp,
  Star,
  Clock,
  Shield,
  Zap,
  Heart,
  Target,
  CheckCircle,
} from "lucide-react"

interface Stat {
  id: string
  icon: React.ComponentType<any>
  value: number
  suffix: string
  label: string
  description: string
  color: string
  prefix?: string
}

const stats: Stat[] = [
  {
    id: "customers",
    icon: Users,
    value: 50000,
    suffix: "+",
    label: "Happy Customers",
    description: "Satisfied customers worldwide trust our platform",
    color: "from-blue-500 to-cyan-500"
  },
  {
    id: "products",
    icon: ShoppingBag,
    value: 1000000,
    suffix: "+",
    label: "Products Sold",
    description: "Products delivered with excellence and care",
    color: "from-green-500 to-emerald-500"
  },
  {
    id: "countries",
    icon: Globe,
    value: 25,
    suffix: "+",
    label: "Countries Served",
    description: "Global reach with local expertise",
    color: "from-purple-500 to-pink-500"
  },
  {
    id: "rating",
    icon: Star,
    value: 4.9,
    suffix: "/5",
    label: "Customer Rating",
    description: "Consistently high customer satisfaction",
    color: "from-yellow-500 to-orange-500"
  },
  {
    id: "uptime",
    icon: Shield,
    value: 99.9,
    suffix: "%",
    label: "Platform Uptime",
    description: "Reliable service you can count on",
    color: "from-red-500 to-pink-500"
  },
  {
    id: "support",
    icon: Clock,
    value: 24,
    suffix: "/7",
    label: "Support Available",
    description: "Round-the-clock customer assistance",
    color: "from-indigo-500 to-purple-500"
  },
  {
    id: "experience",
    icon: Award,
    value: 10,
    suffix: "+",
    label: "Years Experience",
    description: "Decade of innovation and excellence",
    color: "from-teal-500 to-cyan-500"
  },
  {
    id: "growth",
    icon: TrendingUp,
    value: 150,
    suffix: "%",
    label: "YoY Growth",
    description: "Consistent year-over-year expansion",
    color: "from-orange-500 to-red-500"
  }
]

function AnimatedCounter({ 
  value, 
  suffix = "", 
  prefix = "", 
  duration = 2000,
  isVisible = false 
}: { 
  value: number
  suffix?: string
  prefix?: string
  duration?: number
  isVisible?: boolean
}) {
  const [count, setCount] = useState(0)
  const [hasAnimated, setHasAnimated] = useState(false)

  useEffect(() => {
    if (!isVisible || hasAnimated) return

    setHasAnimated(true)
    const startTime = Date.now()
    const startValue = 0

    const animate = () => {
      const now = Date.now()
      const elapsed = now - startTime
      const progress = Math.min(elapsed / duration, 1)
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4)
      const currentValue = startValue + (value - startValue) * easeOutQuart
      
      setCount(currentValue)
      
      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }
    
    requestAnimationFrame(animate)
  }, [isVisible, value, duration, hasAnimated])

  const formatValue = (num: number) => {
    if (value >= 1000000) {
      return (num / 1000000).toFixed(1) + "M"
    } else if (value >= 1000) {
      return (num / 1000).toFixed(value % 1000 === 0 ? 0 : 1) + "K"
    } else if (value < 10 && value % 1 !== 0) {
      return num.toFixed(1)
    } else {
      return Math.floor(num).toString()
    }
  }

  return (
    <span className="font-bold">
      {prefix}{formatValue(count)}{suffix}
    </span>
  )
}

function useIntersectionObserver(ref: React.RefObject<Element>, options = {}) {
  const [isIntersecting, setIsIntersecting] = useState(false)

  useEffect(() => {
    const element = ref.current
    if (!element) return

    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting)
    }, options)

    observer.observe(element)
    return () => observer.unobserve(element)
  }, [ref, options])

  return isIntersecting
}

export function StatsSection() {
  const sectionRef = useRef<HTMLDivElement>(null)
  const isVisible = useIntersectionObserver(sectionRef, { threshold: 0.3 })
  const { t } = useLanguage()

  return (
    <section 
      ref={sectionRef}
      className="py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 text-white overflow-hidden relative"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-grid-pattern"></div>
      </div>

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/20 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/20 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-cyan-500/20 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge className="mb-4 px-4 py-2 bg-white/20 text-white border-white/30">
            <TrendingUp className="h-4 w-4 mr-2" />
            Our Impact
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
              Numbers That
            </span>
            <br />
            <span className="text-white">Tell Our Story</span>
          </h2>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
            Our commitment to excellence is reflected in these achievements. 
            Every number represents our dedication to delivering exceptional value and service.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <Card 
                key={stat.id}
                className="bg-white/10 border-white/20 backdrop-blur-sm hover:bg-white/15 transition-all duration-300 group hover:scale-105"
                style={{
                  animationDelay: `${index * 100}ms`
                }}
              >
                <CardContent className="p-6 text-center">
                  {/* Animated Icon */}
                  <div className={`w-16 h-16 bg-gradient-to-br ${stat.color} rounded-2xl flex items-center justify-center mx-auto mb-4 transform transition-all duration-500 group-hover:scale-110 group-hover:rotate-6`}>
                    <Icon className="h-8 w-8 text-white" />
                  </div>

                  {/* Animated Counter */}
                  <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                    <AnimatedCounter 
                      value={stat.value}
                      suffix={stat.suffix}
                      prefix={stat.prefix}
                      isVisible={isVisible}
                      duration={2000 + index * 200}
                    />
                  </div>

                  {/* Label */}
                  <h3 className="text-lg font-semibold text-white mb-2 group-hover:text-cyan-300 transition-colors">
                    {stat.label}
                  </h3>

                  {/* Description */}
                  <p className="text-sm text-blue-200 leading-relaxed">
                    {stat.description}
                  </p>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Achievement Highlights */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <div className="bg-white/5 rounded-2xl p-8 backdrop-blur-sm border border-white/10 text-center">
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="h-6 w-6 text-white" />
            </div>
            <h3 className="text-xl font-bold text-white mb-2">ISO Certified</h3>
            <p className="text-blue-200 text-sm">
              Certified for quality management and information security standards
            </p>
          </div>

          <div className="bg-white/5 rounded-2xl p-8 backdrop-blur-sm border border-white/10 text-center">
            <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <Award className="h-6 w-6 text-white" />
            </div>
            <h3 className="text-xl font-bold text-white mb-2">Industry Awards</h3>
            <p className="text-blue-200 text-sm">
              Recognized for innovation and excellence in e-commerce technology
            </p>
          </div>

          <div className="bg-white/5 rounded-2xl p-8 backdrop-blur-sm border border-white/10 text-center">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <Heart className="h-6 w-6 text-white" />
            </div>
            <h3 className="text-xl font-bold text-white mb-2">Customer Love</h3>
            <p className="text-blue-200 text-sm">
              Consistently rated as the most trusted platform by our users
            </p>
          </div>
        </div>

        {/* Milestones Timeline */}
        <div className="bg-white/5 rounded-2xl p-8 backdrop-blur-sm border border-white/10">
          <h3 className="text-2xl font-bold text-white text-center mb-8">
            Key Milestones
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-cyan-400 mb-2">2014</div>
              <div className="text-white font-medium mb-1">Founded</div>
              <div className="text-blue-200 text-sm">Started with a vision</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-400 mb-2">2018</div>
              <div className="text-white font-medium mb-1">10K Customers</div>
              <div className="text-blue-200 text-sm">Reached first milestone</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-400 mb-2">2021</div>
              <div className="text-white font-medium mb-1">Global Expansion</div>
              <div className="text-blue-200 text-sm">Entered 25 countries</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-400 mb-2">2024</div>
              <div className="text-white font-medium mb-1">AI Integration</div>
              <div className="text-blue-200 text-sm">Launched AI platform</div>
            </div>
          </div>
        </div>

        {/* Bottom Message */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-white/10 to-white/5 rounded-2xl p-8 backdrop-blur-sm border border-white/10">
            <h3 className="text-2xl font-bold text-white mb-4">
              Growing Together
            </h3>
            <p className="text-blue-100 max-w-2xl mx-auto leading-relaxed">
              These numbers represent more than statistics—they represent relationships, 
              trust, and shared success. Thank you for being part of our journey as we 
              continue to innovate and grow together.
            </p>
            <div className="flex items-center justify-center space-x-6 mt-6 text-blue-200">
              <div className="flex items-center space-x-2">
                <Target className="h-5 w-5" />
                <span className="text-sm">Mission Driven</span>
              </div>
              <div className="w-px h-4 bg-white/20" />
              <div className="flex items-center space-x-2">
                <Zap className="h-5 w-5" />
                <span className="text-sm">Innovation Focused</span>
              </div>
              <div className="w-px h-4 bg-white/20" />
              <div className="flex items-center space-x-2">
                <Heart className="h-5 w-5" />
                <span className="text-sm">Customer Centric</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
