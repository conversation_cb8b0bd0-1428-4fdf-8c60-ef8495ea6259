import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { PAGINATION } from '@/config/constants'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || PAGINATION.DEFAULT_PAGE_SIZE.toString())
    const category = searchParams.get('category')
    const search = searchParams.get('search')
    const featured = searchParams.get('featured')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      status: 'ACTIVE'
    }

    if (category) {
      where.category = {
        slug: category
      }
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { shortDescription: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (featured === 'true') {
      where.featured = true
    }

    // Build orderBy clause
    const orderBy: any = {}
    orderBy[sortBy] = sortOrder

    const [productionLines, total] = await Promise.all([
      prisma.productionLine.findMany({
        where,
        include: {
          category: true
        },
        orderBy,
        skip,
        take: limit
      }),
      prisma.productionLine.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      productionLines,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })
  } catch (error) {
    console.error('Error fetching production lines:', error)
    return NextResponse.json(
      { error: 'Failed to fetch production lines' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const {
      name,
      slug,
      description,
      shortDescription,
      specifications,
      images,
      videos,
      brochure,
      categoryId,
      status,
      featured,
      seoTitle,
      seoDescription
    } = body

    // Check if production line with same slug exists
    const existingLine = await prisma.productionLine.findUnique({
      where: { slug }
    })

    if (existingLine) {
      return NextResponse.json(
        { error: 'Production line with this slug already exists' },
        { status: 400 }
      )
    }

    const productionLine = await prisma.productionLine.create({
      data: {
        name,
        slug,
        description,
        shortDescription,
        specifications: specifications || {},
        images: images || [],
        videos: videos || [],
        brochure,
        categoryId,
        status: status || 'ACTIVE',
        featured: featured ?? false,
        seoTitle,
        seoDescription
      },
      include: {
        category: true
      }
    })

    return NextResponse.json(productionLine, { status: 201 })
  } catch (error) {
    console.error('Error creating production line:', error)
    return NextResponse.json(
      { error: 'Failed to create production line' },
      { status: 500 }
    )
  }
}
