"use client"

import { useState } from "react"
import Link from "next/link"
import { notFound } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { useCartStore } from "@/store/cart-store"
import { useWishlistStore } from "@/store/wishlist-store"
import { formatPrice } from "@/lib/utils"
import ProductReviews from "@/components/product/product-reviews"
import {
  Heart,
  ShoppingCart,
  Star,
  Minus,
  Plus,
  Share2,
  ArrowLeft,
  Truck,
  Shield,
  RotateCcw,
  MessageCircle,
  ChevronLeft,
  ChevronRight,
} from "lucide-react"

// Mock product data
const mockProduct = {
  id: "1",
  name: "Premium Wireless Headphones",
  slug: "premium-wireless-headphones",
  price: 199.99,
  comparePrice: 249.99,
  rating: 4.8,
  reviewCount: 124,
  images: [
    "/placeholder-product.jpg",
    "/placeholder-product.jpg",
    "/placeholder-product.jpg",
    "/placeholder-product.jpg",
  ],
  category: "Electronics",
  brand: "AudioTech",
  sku: "AT-WH-001",
  inStock: true,
  stockCount: 15,
  description: "Experience premium sound quality with our latest wireless headphones featuring advanced noise cancellation technology.",
  features: [
    "Active Noise Cancellation",
    "30-hour battery life",
    "Quick charge: 5 min = 3 hours",
    "Premium leather ear cushions",
    "Bluetooth 5.0 connectivity",
    "Built-in microphone",
  ],
  specifications: {
    "Driver Size": "40mm",
    "Frequency Response": "20Hz - 20kHz",
    "Impedance": "32 ohms",
    "Weight": "250g",
    "Connectivity": "Bluetooth 5.0, 3.5mm jack",
    "Battery Life": "30 hours",
    "Charging Time": "2 hours",
    "Warranty": "2 years",
  },
  variants: [
    { id: "1", name: "Black", price: 199.99, inStock: true },
    { id: "2", name: "White", price: 199.99, inStock: true },
    { id: "3", name: "Silver", price: 209.99, inStock: false },
  ],
}

const mockReviews = [
  {
    id: "1",
    user: "John D.",
    rating: 5,
    date: "2024-01-15",
    title: "Excellent sound quality!",
    content: "These headphones exceeded my expectations. The noise cancellation is fantastic and the battery life is amazing.",
    verified: true,
  },
  {
    id: "2",
    user: "Sarah M.",
    rating: 4,
    date: "2024-01-10",
    title: "Great value for money",
    content: "Really happy with this purchase. Comfortable to wear for long periods and great sound quality.",
    verified: true,
  },
]

interface ProductPageProps {
  params: {
    slug: string
  }
}

export default function ProductPage({ params }: ProductPageProps) {
  const [selectedImage, setSelectedImage] = useState(0)
  const [selectedVariant, setSelectedVariant] = useState(mockProduct.variants[0])
  const [quantity, setQuantity] = useState(1)
  const [activeTab, setActiveTab] = useState("description")
  
  const { t } = useLanguage()
  const { toast } = useToast()
  const { addItem, openCart } = useCartStore()
  const { toggleItem: toggleWishlistItem, isInWishlist } = useWishlistStore()

  // In a real app, you would fetch the product based on the slug
  const product = mockProduct

  if (!product) {
    notFound()
  }

  const handleAddToCart = () => {
    addItem({
      productId: product.id,
      name: product.name,
      slug: product.slug,
      price: product.price,
      image: product.images[0],
      inStock: product.inStock && selectedVariant.inStock,
      maxQuantity: product.stockCount,
      variant: selectedVariant ? {
        id: selectedVariant.id,
        name: selectedVariant.name,
        price: selectedVariant.price,
      } : undefined,
      quantity,
    })

    toast({
      title: "Added to Cart",
      description: `${quantity}x ${product.name} (${selectedVariant.name}) added to cart`,
    })

    // Open cart sidebar
    setTimeout(() => openCart(), 100)
  }

  const handleAddToWishlist = () => {
    const wasAdded = toggleWishlistItem({
      productId: product.id,
      name: product.name,
      slug: product.slug,
      price: product.price,
      comparePrice: product.comparePrice,
      image: product.images[0],
      inStock: product.inStock,
      category: product.category,
      rating: product.rating,
      variant: selectedVariant ? {
        id: selectedVariant.id,
        name: selectedVariant.name,
        price: selectedVariant.price,
      } : undefined,
    })

    toast({
      title: wasAdded ? "Added to Wishlist" : "Removed from Wishlist",
      description: `${product.name} has been ${wasAdded ? 'added to' : 'removed from'} your wishlist`,
    })
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: product.description,
        url: window.location.href,
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
      toast({
        title: "Link Copied",
        description: "Product link copied to clipboard",
      })
    }
  }

  const discount = product.comparePrice 
    ? Math.round(((product.comparePrice - product.price) / product.comparePrice) * 100)
    : 0

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-8">
          <Link href="/" className="hover:text-foreground">Home</Link>
          <span>/</span>
          <Link href="/shop" className="hover:text-foreground">Shop</Link>
          <span>/</span>
          <Link href={`/shop?category=${product.category}`} className="hover:text-foreground">
            {product.category}
          </Link>
          <span>/</span>
          <span className="text-foreground">{product.name}</span>
        </nav>

        {/* Back Button */}
        <Link href="/shop" className="inline-flex items-center text-sm text-gray-500 hover:text-foreground mb-6">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Shop
        </Link>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Product Images */}
          <div className="space-y-4">
            {/* Main Image */}
            <div className="aspect-square bg-white dark:bg-gray-800 rounded-lg overflow-hidden border">
              <div className="w-full h-full flex items-center justify-center text-6xl">
                📦
              </div>
            </div>

            {/* Thumbnail Images */}
            <div className="flex space-x-2">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`w-20 h-20 bg-white dark:bg-gray-800 rounded-lg border-2 flex items-center justify-center text-2xl ${
                    selectedImage === index 
                      ? "border-primary-500" 
                      : "border-gray-200 dark:border-gray-700"
                  }`}
                >
                  📦
                </button>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            {/* Product Title and Rating */}
            <div>
              <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-2">
                <span>{product.brand}</span>
                <span>•</span>
                <span>SKU: {product.sku}</span>
              </div>
              
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                {product.name}
              </h1>

              <div className="flex items-center space-x-4 mb-4">
                <div className="flex items-center space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-5 w-5 ${
                        i < Math.floor(product.rating)
                          ? "text-yellow-400 fill-current"
                          : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm text-gray-500">
                  {product.rating} ({product.reviewCount} reviews)
                </span>
              </div>
            </div>

            {/* Price */}
            <div className="flex items-center space-x-4">
              <span className="text-3xl font-bold text-gray-900 dark:text-white">
                {formatPrice(selectedVariant.price)}
              </span>
              {product.comparePrice && (
                <>
                  <span className="text-xl text-gray-500 line-through">
                    {formatPrice(product.comparePrice)}
                  </span>
                  <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded text-sm font-medium">
                    Save {discount}%
                  </span>
                </>
              )}
            </div>

            {/* Stock Status */}
            <div className="flex items-center space-x-2">
              {product.inStock ? (
                <>
                  <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                  <span className="text-green-600 dark:text-green-400 text-sm">
                    In Stock ({product.stockCount} available)
                  </span>
                </>
              ) : (
                <>
                  <div className="h-2 w-2 bg-red-500 rounded-full"></div>
                  <span className="text-red-600 dark:text-red-400 text-sm">
                    Out of Stock
                  </span>
                </>
              )}
            </div>

            {/* Variants */}
            <div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                Color
              </h3>
              <div className="flex space-x-2">
                {product.variants.map((variant) => (
                  <button
                    key={variant.id}
                    onClick={() => setSelectedVariant(variant)}
                    disabled={!variant.inStock}
                    className={`px-4 py-2 border rounded-md text-sm font-medium transition-colors ${
                      selectedVariant.id === variant.id
                        ? "border-primary-500 bg-primary-50 dark:bg-primary-900 text-primary-700 dark:text-primary-300"
                        : variant.inStock
                        ? "border-gray-300 dark:border-gray-600 hover:border-gray-400"
                        : "border-gray-200 dark:border-gray-700 text-gray-400 cursor-not-allowed"
                    }`}
                  >
                    {variant.name}
                    {!variant.inStock && " (Out of Stock)"}
                  </button>
                ))}
              </div>
            </div>

            {/* Quantity */}
            <div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                Quantity
              </h3>
              <div className="flex items-center space-x-4">
                <div className="flex items-center border rounded-md">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800"
                  >
                    <Minus className="h-4 w-4" />
                  </button>
                  <span className="px-4 py-2 border-x">{quantity}</span>
                  <button
                    onClick={() => setQuantity(Math.min(product.stockCount, quantity + 1))}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800"
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
                <span className="text-sm text-gray-500">
                  Total: {formatPrice(selectedVariant.price * quantity)}
                </span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-4">
              <Button
                onClick={handleAddToCart}
                disabled={!product.inStock}
                className="flex-1"
                size="lg"
              >
                <ShoppingCart className="h-5 w-5 mr-2" />
                Add to Cart
              </Button>
              
              <Button
                variant="outline"
                onClick={handleAddToWishlist}
                size="lg"
              >
                <Heart className={`h-5 w-5 ${
                  isInWishlist(product.id, selectedVariant?.id)
                    ? 'fill-current text-red-500'
                    : ''
                }`} />
              </Button>
              
              <Button
                variant="outline"
                onClick={handleShare}
                size="lg"
              >
                <Share2 className="h-5 w-5" />
              </Button>
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-6 border-t">
              <div className="flex items-center space-x-2">
                <Truck className="h-5 w-5 text-green-500" />
                <span className="text-sm">Free Shipping</span>
              </div>
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-blue-500" />
                <span className="text-sm">2 Year Warranty</span>
              </div>
              <div className="flex items-center space-x-2">
                <RotateCcw className="h-5 w-5 text-purple-500" />
                <span className="text-sm">30-Day Returns</span>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="mt-16">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="description">Description</TabsTrigger>
              <TabsTrigger value="specifications">Specifications</TabsTrigger>
              <TabsTrigger value="reviews">Reviews ({product.reviewCount})</TabsTrigger>
            </TabsList>

            <TabsContent value="description" className="mt-8">
              <Card>
                <CardContent className="p-6">
                  <p className="text-gray-600 dark:text-gray-400 mb-6">
                    {product.description}
                  </p>
                  
                  <h3 className="text-lg font-semibold mb-4">Key Features</h3>
                  <ul className="space-y-2">
                    {product.features.map((feature, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <div className="h-1.5 w-1.5 bg-primary-500 rounded-full"></div>
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="specifications" className="mt-8">
              <Card>
                <CardContent className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(product.specifications).map(([key, value]) => (
                      <div key={key} className="flex justify-between py-2 border-b border-gray-200 dark:border-gray-700">
                        <span className="font-medium">{key}</span>
                        <span className="text-gray-600 dark:text-gray-400">{value}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="reviews" className="mt-8">
              <ProductReviews productId={product.id} />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
