import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { emailService } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      productId,
      supplierId,
      inquiryType,
      urgency,
      quantity,
      targetPrice,
      specifications,
      customRequirements,
      shippingDestination,
      expectedDelivery,
      businessType,
      companySize,
      additionalNotes,
      customerName,
      customerEmail,
      customerPhone,
      customerCompany,
      estimatedTotal,
    } = body

    // Validate required fields
    if (!productId || !supplierId || !inquiryType || !quantity) {
      return NextResponse.json(
        { error: 'Missing required fields: productId, supplierId, inquiryType, quantity' },
        { status: 400 }
      )
    }

    // Get product and supplier information
    const [product, supplier] = await Promise.all([
      prisma.product.findUnique({
        where: { id: productId },
        include: {
          category: true,
          images: true,
        },
      }),
      prisma.supplier.findUnique({
        where: { id: supplierId },
      }),
    ])

    if (!product || !supplier) {
      return NextResponse.json(
        { error: 'Product or supplier not found' },
        { status: 404 }
      )
    }

    // Validate quantity against MOQ
    const requestedQuantity = parseInt(quantity)
    if (requestedQuantity < product.minimumOrder) {
      return NextResponse.json(
        { error: `Quantity must be at least ${product.minimumOrder} (MOQ)` },
        { status: 400 }
      )
    }

    // Generate unique inquiry number
    const inquiryNumber = `INQ-${Date.now()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`

    // Determine priority based on urgency
    const priorityMap = {
      low: 'LOW',
      normal: 'MEDIUM',
      high: 'HIGH',
      urgent: 'URGENT',
    }

    // Create the product inquiry
    const productInquiry = await prisma.productInquiry.create({
      data: {
        inquiryNumber,
        customerId: session.user.id,
        customerName: customerName || session.user.name || 'Unknown',
        customerEmail: customerEmail || session.user.email || '',
        customerPhone: customerPhone || '',
        customerCompany: customerCompany || '',
        productId,
        requestedQty: requestedQuantity,
        targetPrice: targetPrice ? parseFloat(targetPrice) : null,
        specifications: specifications || '',
        status: 'PENDING',
        
        // Additional inquiry details stored as JSON
        customerNotes: JSON.stringify({
          inquiryType,
          urgency,
          customRequirements,
          shippingDestination,
          expectedDelivery,
          businessType,
          companySize,
          additionalNotes,
          estimatedTotal,
        }),
      },
      include: {
        product: {
          include: {
            category: true,
          },
        },
        customer: true,
      },
    })

    // Send notification email to supplier
    try {
      await emailService.sendProductInquiryNotification({
        inquiryNumber,
        supplierEmail: supplier.email,
        supplierName: supplier.contactPerson,
        customerName: customerName || session.user.name || 'Customer',
        customerEmail: customerEmail || session.user.email || '',
        customerCompany: customerCompany || '',
        productName: product.name,
        quantity: requestedQuantity,
        targetPrice: targetPrice ? parseFloat(targetPrice) : null,
        specifications,
        urgency,
        inquiryType,
        estimatedTotal,
      })
    } catch (emailError) {
      console.error('Failed to send supplier notification:', emailError)
      // Don't fail the request if email fails
    }

    // Send confirmation email to customer
    try {
      await emailService.sendInquiryConfirmation({
        customerName: customerName || session.user.name || 'Customer',
        customerEmail: customerEmail || session.user.email || '',
        inquiryNumber,
        productName: product.name,
        supplierName: supplier.name,
        quantity: requestedQuantity,
        estimatedTotal,
        expectedResponseTime: supplier.responseTime || '24-48 hours',
      })
    } catch (emailError) {
      console.error('Failed to send confirmation email:', emailError)
    }

    // Create notification for admin
    try {
      await prisma.notification.create({
        data: {
          userId: session.user.id,
          type: 'SYSTEM',
          title: 'New Product Inquiry',
          message: `New inquiry #${inquiryNumber} for ${product.name}`,
          data: JSON.stringify({
            inquiryId: productInquiry.id,
            productId,
            supplierId,
            quantity: requestedQuantity,
          }),
        },
      })
    } catch (notificationError) {
      console.error('Failed to create notification:', notificationError)
    }

    return NextResponse.json({
      success: true,
      inquiryNumber,
      inquiry: {
        id: productInquiry.id,
        inquiryNumber: productInquiry.inquiryNumber,
        status: productInquiry.status,
        createdAt: productInquiry.createdAt,
        product: {
          name: product.name,
          category: product.category?.name,
        },
        supplier: {
          name: supplier.name,
          responseTime: supplier.responseTime,
        },
      },
    })

  } catch (error) {
    console.error('Product inquiry creation error:', error)
    return NextResponse.json(
      { error: 'Failed to create product inquiry' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status')
    const productId = searchParams.get('productId')
    
    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      customerId: session.user.id,
    }

    if (status) {
      where.status = status
    }

    if (productId) {
      where.productId = productId
    }

    // Get product inquiries
    const [inquiries, total] = await Promise.all([
      prisma.productInquiry.findMany({
        where,
        include: {
          product: {
            include: {
              category: true,
              images: true,
            },
          },
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.productInquiry.count({ where }),
    ])

    // Enrich inquiries with supplier information
    const enrichedInquiries = await Promise.all(
      inquiries.map(async (inquiry) => {
        // Parse customer notes to get additional details
        let additionalDetails = {}
        try {
          additionalDetails = JSON.parse(inquiry.customerNotes || '{}')
        } catch (e) {
          // Ignore parsing errors
        }

        // Get supplier information
        const supplier = await prisma.supplier.findFirst({
          where: {
            wholesaleProducts: {
              some: {
                productId: inquiry.productId,
              },
            },
          },
          select: {
            id: true,
            name: true,
            contactPerson: true,
            email: true,
            phone: true,
            city: true,
            rating: true,
            verified: true,
            responseTime: true,
          },
        })

        return {
          ...inquiry,
          additionalDetails,
          supplier,
        }
      })
    )

    return NextResponse.json({
      inquiries: enrichedInquiries,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })

  } catch (error) {
    console.error('Product inquiries fetch error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch product inquiries' },
      { status: 500 }
    )
  }
}

// Update inquiry status (for suppliers/admin)
export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { inquiryId, status, quotedPrice, quotedQty, leadTime, validUntil, supplierNotes } = body

    if (!inquiryId || !status) {
      return NextResponse.json(
        { error: 'Missing required fields: inquiryId, status' },
        { status: 400 }
      )
    }

    // Get the inquiry
    const inquiry = await prisma.productInquiry.findUnique({
      where: { id: inquiryId },
      include: {
        product: true,
        customer: true,
      },
    })

    if (!inquiry) {
      return NextResponse.json(
        { error: 'Inquiry not found' },
        { status: 404 }
      )
    }

    // Check permissions (customer can only view, suppliers/admin can update)
    if (session.user.role !== 'ADMIN' && inquiry.customerId !== session.user.id) {
      // TODO: Add supplier permission check
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Update the inquiry
    const updatedInquiry = await prisma.productInquiry.update({
      where: { id: inquiryId },
      data: {
        status: status as any,
        quotedPrice: quotedPrice ? parseFloat(quotedPrice) : null,
        quotedQty: quotedQty ? parseInt(quotedQty) : null,
        leadTime: leadTime ? parseInt(leadTime) : null,
        validUntil: validUntil ? new Date(validUntil) : null,
        supplierNotes: supplierNotes || null,
        updatedAt: new Date(),
      },
      include: {
        product: true,
        customer: true,
      },
    })

    // Send notification to customer about status update
    if (status !== 'PENDING') {
      try {
        await emailService.sendInquiryStatusUpdate({
          customerName: inquiry.customer.name || 'Customer',
          customerEmail: inquiry.customer.email || '',
          inquiryNumber: inquiry.inquiryNumber,
          productName: inquiry.product.name,
          status,
          quotedPrice,
          quotedQty,
          leadTime,
          validUntil,
          supplierNotes,
        })
      } catch (emailError) {
        console.error('Failed to send status update email:', emailError)
      }
    }

    return NextResponse.json({
      success: true,
      inquiry: updatedInquiry,
    })

  } catch (error) {
    console.error('Inquiry update error:', error)
    return NextResponse.json(
      { error: 'Failed to update inquiry' },
      { status: 500 }
    )
  }
}
