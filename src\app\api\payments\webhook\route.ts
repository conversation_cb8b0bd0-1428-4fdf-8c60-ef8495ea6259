import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { verifyWebhookSignature } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'
import { emailService } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const headersList = headers()
    const signature = headersList.get('stripe-signature')

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing stripe-signature header' },
        { status: 400 }
      )
    }

    // Verify webhook signature
    const event = verifyWebhookSignature(body, signature)

    // Handle different event types
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object as any)
        break

      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object as any)
        break

      case 'payment_intent.canceled':
        await handlePaymentCanceled(event.data.object as any)
        break

      case 'customer.created':
        await handleCustomerCreated(event.data.object as any)
        break

      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object as any)
        break

      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object as any)
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })

  } catch (error) {
    console.error('Webhook error:', error)
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 400 }
    )
  }
}

async function handlePaymentSucceeded(paymentIntent: any) {
  try {
    console.log('Payment succeeded:', paymentIntent.id)

    // Update order status
    const order = await prisma.order.findFirst({
      where: { paymentIntentId: paymentIntent.id },
      include: {
        user: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    })

    if (!order) {
      console.error('Order not found for payment intent:', paymentIntent.id)
      return
    }

    // Update order status
    await prisma.order.update({
      where: { id: order.id },
      data: {
        status: 'confirmed',
        paidAt: new Date(),
        paymentMethod: 'stripe',
      },
    })

    // Update product inventory
    for (const item of order.items) {
      if (item.product) {
        await prisma.product.update({
          where: { id: item.product.id },
          data: {
            stockCount: {
              decrement: item.quantity,
            },
          },
        })
      }
    }

    // Send order confirmation email
    if (order.user.email) {
      const shippingAddress = order.shippingAddress 
        ? JSON.parse(order.shippingAddress) 
        : null

      await emailService.sendOrderConfirmation({
        orderId: order.id,
        customerName: order.user.name || 'Customer',
        customerEmail: order.user.email,
        orderDate: order.createdAt,
        items: order.items.map(item => ({
          name: item.name,
          quantity: item.quantity,
          price: item.price,
          image: item.product?.images?.[0]?.url,
        })),
        subtotal: order.subtotal,
        shipping: order.shipping,
        tax: order.tax,
        total: order.total,
        shippingAddress: shippingAddress || {
          name: order.user.name || 'Customer',
          address: 'Address not provided',
          city: 'City not provided',
          state: 'State not provided',
          zipCode: 'ZIP not provided',
          country: 'Country not provided',
        },
        estimatedDelivery: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
      })
    }

    console.log('Order confirmed and email sent:', order.id)

  } catch (error) {
    console.error('Error handling payment succeeded:', error)
  }
}

async function handlePaymentFailed(paymentIntent: any) {
  try {
    console.log('Payment failed:', paymentIntent.id)

    // Update order status
    await prisma.order.updateMany({
      where: { paymentIntentId: paymentIntent.id },
      data: {
        status: 'failed',
        failedAt: new Date(),
      },
    })

    // Optionally send payment failed email to customer
    const order = await prisma.order.findFirst({
      where: { paymentIntentId: paymentIntent.id },
      include: { user: true },
    })

    if (order?.user.email) {
      // Send payment failed notification
      console.log('Payment failed for order:', order.id)
      // You could implement a payment failed email here
    }

  } catch (error) {
    console.error('Error handling payment failed:', error)
  }
}

async function handlePaymentCanceled(paymentIntent: any) {
  try {
    console.log('Payment canceled:', paymentIntent.id)

    // Update order status
    await prisma.order.updateMany({
      where: { paymentIntentId: paymentIntent.id },
      data: {
        status: 'canceled',
        canceledAt: new Date(),
      },
    })

  } catch (error) {
    console.error('Error handling payment canceled:', error)
  }
}

async function handleCustomerCreated(customer: any) {
  try {
    console.log('Customer created:', customer.id)

    // Update user with Stripe customer ID if not already set
    if (customer.email) {
      await prisma.user.updateMany({
        where: { 
          email: customer.email,
          stripeCustomerId: null,
        },
        data: {
          stripeCustomerId: customer.id,
        },
      })
    }

  } catch (error) {
    console.error('Error handling customer created:', error)
  }
}

async function handleInvoicePaymentSucceeded(invoice: any) {
  try {
    console.log('Invoice payment succeeded:', invoice.id)
    
    // Handle subscription payments or other invoice-based payments
    // This would be useful for subscription-based products

  } catch (error) {
    console.error('Error handling invoice payment succeeded:', error)
  }
}

async function handleInvoicePaymentFailed(invoice: any) {
  try {
    console.log('Invoice payment failed:', invoice.id)
    
    // Handle failed subscription payments
    // This would be useful for subscription-based products

  } catch (error) {
    console.error('Error handling invoice payment failed:', error)
  }
}
