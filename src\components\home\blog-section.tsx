"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useLatestBlogPosts } from "@/hooks/use-blog"
import {
  BookOpen,
  Calendar,
  User,
  Clock,
  ArrowRight,
  Eye,
  MessageCircle,
  Share2,
  TrendingUp,
  Zap,
  Lightbulb,
  Target,
  Shield,
  Loader2,
} from "lucide-react"

// Helper functions

function getCategoryIcon(category: string) {
  switch (category.toLowerCase()) {
    case 'technology':
      return Zap
    case 'sustainability':
      return Target
    case 'security':
      return Shield
    case 'business':
      return TrendingUp
    default:
      return Lightbulb
  }
}

function getCategoryColor(category: string) {
  switch (category.toLowerCase()) {
    case 'technology':
      return 'from-blue-500 to-cyan-500'
    case 'sustainability':
      return 'from-green-500 to-emerald-500'
    case 'security':
      return 'from-red-500 to-orange-500'
    case 'business':
      return 'from-purple-500 to-pink-500'
    default:
      return 'from-gray-500 to-gray-600'
  }
}

export function BlogSection() {
  const [hoveredArticle, setHoveredArticle] = useState<string | null>(null)
  const { t } = useLanguage()

  // Fetch latest blog posts from API
  const { data: blogData, isLoading, error } = useLatestBlogPosts(4)
  const latestArticles = blogData?.posts || []

  // Loading state
  if (isLoading) {
    return (
      <section className="py-20 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-blue-900/20 dark:to-indigo-900/20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Badge className="mb-4 px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white">
              <BookOpen className="h-4 w-4 mr-2" />
              Latest Insights
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Expert Insights
              </span>
              <br />
              <span className="text-gray-900 dark:text-white">& Industry News</span>
            </h2>
          </div>
          <div className="flex justify-center items-center py-20">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-2 text-lg">Loading latest articles...</span>
          </div>
        </div>
      </section>
    )
  }

  // Error state
  if (error) {
    return (
      <section className="py-20 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-blue-900/20 dark:to-indigo-900/20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Badge className="mb-4 px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white">
              <BookOpen className="h-4 w-4 mr-2" />
              Latest Insights
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Expert Insights
              </span>
              <br />
              <span className="text-gray-900 dark:text-white">& Industry News</span>
            </h2>
          </div>
          <div className="text-center py-20">
            <p className="text-red-600 dark:text-red-400 text-lg">
              Failed to load articles. Please try again later.
            </p>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-blue-900/20 dark:to-indigo-900/20">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge className="mb-4 px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white">
            <BookOpen className="h-4 w-4 mr-2" />
            Latest Insights
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Expert Insights
            </span>
            <br />
            <span className="text-gray-900 dark:text-white">& Industry News</span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed">
            Stay informed with the latest trends, insights, and expert analysis from our team of 
            industry professionals and thought leaders.
          </p>
        </div>

        {/* Featured Article */}
        <div className="mb-16">
          {latestArticles.filter(article => article.featured)[0] && (
            <Card className="overflow-hidden hover:shadow-2xl transition-all duration-500 group">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                {/* Image */}
                <div className="relative h-80 lg:h-auto">
                  <div className="w-full h-full bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900 dark:to-indigo-900 flex items-center justify-center relative overflow-hidden">
                    <div className="text-8xl opacity-20">📰</div>
                    
                    {/* Featured Badge */}
                    <div className="absolute top-4 left-4">
                      <Badge className="bg-gradient-to-r from-orange-500 to-red-500 text-white">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        Featured
                      </Badge>
                    </div>

                    {/* Category Badge */}
                    <div className="absolute top-4 right-4">
                      <Badge className={`bg-gradient-to-r ${getCategoryColor(latestArticles.filter(article => article.featured)[0]?.category?.name || '')} text-white`}>
                        {latestArticles.filter(article => article.featured)[0]?.category?.name}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="p-8 lg:p-12 flex flex-col justify-center">
                  {(() => {
                    const featuredArticle = latestArticles.filter(article => article.featured)[0]
                    if (!featuredArticle) return null

                    return (
                      <div className="space-y-6">
                        {/* Meta Info */}
                        <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>{new Date(featuredArticle.publishedAt || featuredArticle.createdAt).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Clock className="h-4 w-4" />
                            <span>{featuredArticle.readingTime || 5} min read</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Eye className="h-4 w-4" />
                            <span>{featuredArticle.views.toLocaleString()} views</span>
                          </div>
                        </div>

                        {/* Title */}
                        <h3 className="text-3xl font-bold text-gray-900 dark:text-white leading-tight">
                          {featuredArticle.title}
                        </h3>

                        {/* Excerpt */}
                        <p className="text-gray-600 dark:text-gray-400 text-lg leading-relaxed">
                          {featuredArticle.excerpt}
                        </p>

                        {/* Author */}
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                            <User className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">
                              {featuredArticle.author.name}
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              Author
                            </p>
                          </div>
                        </div>

                        {/* Tags */}
                        <div className="flex flex-wrap gap-2">
                          {featuredArticle.tags && Array.isArray(featuredArticle.tags) && featuredArticle.tags.slice(0, 3).map((tag) => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>

                        {/* CTA */}
                        <div className="flex items-center space-x-4">
                          <Link href={`/blog/${featuredArticle.slug}`}>
                            <Button className="group bg-gradient-to-r from-blue-500 to-indigo-500 hover:shadow-lg">
                              Read Full Article
                              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                            </Button>
                          </Link>
                          <div className="flex items-center space-x-3 text-gray-600 dark:text-gray-400">
                            <Button variant="ghost" size="sm" className="p-2">
                              <Share2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    )
                  })()}
                  </div>
                </div>
              </div>
            </Card>
          )}
        </div>

        {/* Latest Articles Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {latestArticles.map((article) => {
            const CategoryIcon = getCategoryIcon(article.category?.name || '')
            const isHovered = hoveredArticle === article.id
            
            return (
              <Card 
                key={article.id}
                className={`overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-2 group cursor-pointer ${
                  isHovered ? 'ring-2 ring-blue-500' : ''
                }`}
                onMouseEnter={() => setHoveredArticle(article.id)}
                onMouseLeave={() => setHoveredArticle(null)}
              >
                {/* Image */}
                <div className="relative h-48">
                  <div className={`w-full h-full bg-gradient-to-br ${getCategoryColor(article.category?.name || '')} opacity-20 flex items-center justify-center relative overflow-hidden`}>
                    <div className="text-6xl opacity-30">📄</div>

                    {/* Category Icon */}
                    <div className="absolute top-4 left-4">
                      <div className={`w-8 h-8 bg-gradient-to-br ${getCategoryColor(article.category?.name || '')} rounded-lg flex items-center justify-center`}>
                        <CategoryIcon className="h-4 w-4 text-white" />
                      </div>
                    </div>

                    {/* Read Time */}
                    <div className="absolute top-4 right-4">
                      <Badge className="bg-black/50 text-white text-xs">
                        {article.readingTime || 5} min
                      </Badge>
                    </div>
                  </div>
                </div>

                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Category & Date */}
                    <div className="flex items-center justify-between text-sm">
                      <Badge className={`bg-gradient-to-r ${getCategoryColor(article.category?.name || '')} text-white`}>
                        {article.category?.name}
                      </Badge>
                      <span className="text-gray-500">{new Date(article.publishedAt || article.createdAt).toLocaleDateString()}</span>
                    </div>

                    {/* Title */}
                    <Link href={`/blog/${article.slug}`}>
                      <h4 className="font-bold text-gray-900 dark:text-white leading-tight group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors cursor-pointer">
                        {article.title}
                      </h4>
                    </Link>

                    {/* Excerpt */}
                    <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed line-clamp-3">
                      {article.excerpt}
                    </p>

                    {/* Author */}
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                        <User className="h-3 w-3 text-gray-600 dark:text-gray-300" />
                      </div>
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {article.author.name}
                      </span>
                    </div>

                    {/* Stats */}
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center space-x-3">
                        <span className="flex items-center space-x-1">
                          <Eye className="h-3 w-3" />
                          <span>{article.views > 1000 ? `${(article.views / 1000).toFixed(1)}k` : article.views}</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span>{article.readingTime || 5} min</span>
                        </span>
                      </div>
                      <Link href={`/blog/${article.slug}`}>
                        <Button variant="ghost" size="sm" className="text-xs p-1 h-auto">
                          Read More
                          <ArrowRight className="ml-1 h-3 w-3" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl p-8 border border-blue-200 dark:border-blue-800">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Stay Updated with Industry Insights
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-2xl mx-auto">
              Subscribe to our newsletter and never miss the latest trends, expert analysis, 
              and actionable insights from the world of technology and business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/blog">
                <Button size="lg" className="px-8 py-4 text-lg font-semibold bg-gradient-to-r from-blue-500 to-indigo-500 hover:shadow-lg group">
                  Explore All Articles
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Button>
              </Link>
              <Button variant="outline" size="lg" className="px-8 py-4 text-lg font-semibold border-blue-300 text-blue-600 hover:bg-blue-50 dark:border-blue-600 dark:text-blue-400 dark:hover:bg-blue-900/20">
                <BookOpen className="mr-2 h-5 w-5" />
                Subscribe to Newsletter
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
