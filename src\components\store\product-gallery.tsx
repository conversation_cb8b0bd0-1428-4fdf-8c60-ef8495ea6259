"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  ChevronLeft,
  ChevronRight,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Maximize2,
  Play,
  Pause,
  RotateCcw,
  Move3D,
  Eye,
  Camera,
  Video,
  X,
} from "lucide-react"

interface ProductImage {
  id: string
  url: string
  alt: string
  type: 'image' | 'video' | '360'
  thumbnail: string
}

interface ProductGalleryProps {
  images: ProductImage[]
  productName: string
  className?: string
}

export function ProductGallery({ images, productName, className = "" }: ProductGalleryProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isZoomed, setIsZoomed] = useState(false)
  const [zoomLevel, setZoomLevel] = useState(1)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [is360Playing, setIs360Playing] = useState(false)
  const [rotation360, setRotation360] = useState(0)
  const [dragStart, setDragStart] = useState<{ x: number; y: number } | null>(null)
  
  const galleryRef = useRef<HTMLDivElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const animationRef = useRef<number>()

  const currentImage = images[currentIndex]

  // Auto-rotate 360° images when playing
  useEffect(() => {
    if (is360Playing && currentImage?.type === '360') {
      const animate = () => {
        setRotation360(prev => (prev + 1) % 360)
        animationRef.current = requestAnimationFrame(animate)
      }
      animationRef.current = requestAnimationFrame(animate)
    } else if (animationRef.current) {
      cancelAnimationFrame(animationRef.current)
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [is360Playing, currentImage?.type])

  const nextImage = () => {
    setCurrentIndex((prev) => (prev + 1) % images.length)
    setZoomLevel(1)
    setIsZoomed(false)
  }

  const prevImage = () => {
    setCurrentIndex((prev) => (prev - 1 + images.length) % images.length)
    setZoomLevel(1)
    setIsZoomed(false)
  }

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.5, 3))
    setIsZoomed(true)
  }

  const handleZoomOut = () => {
    const newZoom = Math.max(zoomLevel - 0.5, 1)
    setZoomLevel(newZoom)
    if (newZoom === 1) setIsZoomed(false)
  }

  const toggleFullscreen = () => {
    if (!isFullscreen && galleryRef.current) {
      galleryRef.current.requestFullscreen?.()
      setIsFullscreen(true)
    } else if (document.fullscreenElement) {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
  }

  const handle360Drag = (e: React.MouseEvent) => {
    if (currentImage?.type !== '360') return

    if (dragStart) {
      const deltaX = e.clientX - dragStart.x
      const rotationDelta = deltaX * 0.5
      setRotation360(prev => (prev + rotationDelta) % 360)
      setDragStart({ x: e.clientX, y: e.clientY })
    }
  }

  const start360Drag = (e: React.MouseEvent) => {
    if (currentImage?.type === '360') {
      setDragStart({ x: e.clientX, y: e.clientY })
      setIs360Playing(false)
    }
  }

  const end360Drag = () => {
    setDragStart(null)
  }

  const getImageTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Video className="h-3 w-3" />
      case '360':
        return <Move3D className="h-3 w-3" />
      default:
        return <Camera className="h-3 w-3" />
    }
  }

  const getImageTypeLabel = (type: string) => {
    switch (type) {
      case 'video':
        return 'Video'
      case '360':
        return '360° View'
      default:
        return 'Photo'
    }
  }

  return (
    <div className={`space-y-4 ${className}`} ref={galleryRef}>
      {/* Main Image Display */}
      <Card className="relative overflow-hidden bg-white dark:bg-gray-900">
        <CardContent className="p-0">
          <div className="relative aspect-square bg-gray-50 dark:bg-gray-800">
            {/* Image Type Badge */}
            <Badge className="absolute top-4 left-4 z-10 bg-black/70 text-white">
              {getImageTypeIcon(currentImage?.type)}
              <span className="ml-1">{getImageTypeLabel(currentImage?.type)}</span>
            </Badge>

            {/* Navigation Arrows */}
            {images.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white"
                  onClick={prevImage}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white"
                  onClick={nextImage}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </>
            )}

            {/* Controls */}
            <div className="absolute top-4 right-4 z-10 flex space-x-2">
              {/* Zoom Controls */}
              {currentImage?.type === 'image' && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="bg-black/50 hover:bg-black/70 text-white"
                    onClick={handleZoomIn}
                    disabled={zoomLevel >= 3}
                  >
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="bg-black/50 hover:bg-black/70 text-white"
                    onClick={handleZoomOut}
                    disabled={zoomLevel <= 1}
                  >
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                </>
              )}

              {/* 360° Controls */}
              {currentImage?.type === '360' && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="bg-black/50 hover:bg-black/70 text-white"
                    onClick={() => setIs360Playing(!is360Playing)}
                  >
                    {is360Playing ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="bg-black/50 hover:bg-black/70 text-white"
                    onClick={() => setRotation360(0)}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </>
              )}

              {/* Fullscreen */}
              <Button
                variant="ghost"
                size="sm"
                className="bg-black/50 hover:bg-black/70 text-white"
                onClick={toggleFullscreen}
              >
                {isFullscreen ? <X className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
              </Button>
            </div>

            {/* Main Image/Video */}
            <div className="w-full h-full flex items-center justify-center overflow-hidden">
              {currentImage?.type === 'video' ? (
                <video
                  className="max-w-full max-h-full object-contain"
                  controls
                  poster={currentImage.thumbnail}
                >
                  <source src={currentImage.url} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              ) : (
                <img
                  ref={imageRef}
                  src={currentImage?.url || '/placeholder-product.jpg'}
                  alt={currentImage?.alt || productName}
                  className={`max-w-full max-h-full object-contain transition-transform duration-200 ${
                    currentImage?.type === '360' ? 'cursor-grab active:cursor-grabbing' : ''
                  } ${isZoomed ? 'cursor-zoom-out' : 'cursor-zoom-in'}`}
                  style={{
                    transform: `scale(${zoomLevel}) ${
                      currentImage?.type === '360' ? `rotate(${rotation360}deg)` : ''
                    }`,
                  }}
                  onClick={() => {
                    if (currentImage?.type === 'image') {
                      if (isZoomed) {
                        setZoomLevel(1)
                        setIsZoomed(false)
                      } else {
                        setZoomLevel(2)
                        setIsZoomed(true)
                      }
                    }
                  }}
                  onMouseDown={start360Drag}
                  onMouseMove={handle360Drag}
                  onMouseUp={end360Drag}
                  onMouseLeave={end360Drag}
                />
              )}
            </div>

            {/* 360° Instructions */}
            {currentImage?.type === '360' && !is360Playing && (
              <div className="absolute bottom-4 left-1/2 -translate-x-1/2 z-10">
                <Badge className="bg-black/70 text-white">
                  <Move3D className="h-3 w-3 mr-1" />
                  Drag to rotate • Click play for auto-rotation
                </Badge>
              </div>
            )}

            {/* Image Counter */}
            {images.length > 1 && (
              <div className="absolute bottom-4 right-4 z-10">
                <Badge className="bg-black/70 text-white">
                  {currentIndex + 1} / {images.length}
                </Badge>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Thumbnail Navigation */}
      {images.length > 1 && (
        <div className="flex space-x-2 overflow-x-auto pb-2">
          {images.map((image, index) => (
            <button
              key={image.id}
              onClick={() => setCurrentIndex(index)}
              className={`relative flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ${
                index === currentIndex
                  ? 'border-primary-500 ring-2 ring-primary-200'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
              }`}
            >
              <img
                src={image.thumbnail}
                alt={`${productName} thumbnail ${index + 1}`}
                className="w-full h-full object-cover"
              />
              
              {/* Type indicator */}
              <div className="absolute top-1 right-1">
                <div className="w-4 h-4 bg-black/70 rounded-full flex items-center justify-center">
                  {getImageTypeIcon(image.type)}
                </div>
              </div>
            </button>
          ))}
        </div>
      )}

      {/* Image Information */}
      <div className="text-center text-sm text-gray-600 dark:text-gray-400">
        {currentImage?.type === '360' && (
          <p className="flex items-center justify-center space-x-1">
            <Eye className="h-4 w-4" />
            <span>Interactive 360° view - drag to explore all angles</span>
          </p>
        )}
        {currentImage?.type === 'video' && (
          <p>Product demonstration video</p>
        )}
        {isZoomed && (
          <p>Click image to zoom out • Scroll to zoom in/out</p>
        )}
      </div>
    </div>
  )
}
