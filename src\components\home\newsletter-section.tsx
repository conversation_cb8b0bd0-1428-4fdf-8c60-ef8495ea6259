"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import {
  Mail,
  Send,
  CheckCircle,
  Gift,
  TrendingUp,
  Zap,
  Bell,
  Star,
  Users,
  Shield,
  Clock,
  ArrowRight,
} from "lucide-react"

const benefits = [
  {
    icon: Zap,
    title: "Exclusive Deals",
    description: "Get early access to flash sales and subscriber-only discounts"
  },
  {
    icon: TrendingUp,
    title: "Industry Insights",
    description: "Stay ahead with expert analysis and market trends"
  },
  {
    icon: Gift,
    title: "Free Resources",
    description: "Download exclusive guides, templates, and tools"
  },
  {
    icon: Bell,
    title: "Product Updates",
    description: "Be first to know about new products and features"
  }
]

const stats = [
  { icon: Users, value: "50,000+", label: "Subscribers" },
  { icon: Star, value: "4.9/5", label: "Rating" },
  { icon: Mail, value: "Weekly", label: "Frequency" },
  { icon: Shield, value: "100%", label: "Privacy Protected" }
]

export function NewsletterSection() {
  const [email, setEmail] = useState("")
  const [isSubscribing, setIsSubscribing] = useState(false)
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [preferences, setPreferences] = useState({
    deals: true,
    insights: true,
    products: false,
    events: false
  })
  const { toast } = useToast()
  const { t } = useLanguage()

  const handleSubscribe = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email) {
      toast({
        title: "Email Required",
        description: "Please enter your email address to subscribe.",
        variant: "destructive"
      })
      return
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      toast({
        title: "Invalid Email",
        description: "Please enter a valid email address.",
        variant: "destructive"
      })
      return
    }

    setIsSubscribing(true)

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Simulate email verification process
    toast({
      title: "Verification Email Sent!",
      description: "Please check your email and click the verification link to complete your subscription."
    })

    setIsSubscribed(true)
    setEmail("")
    setIsSubscribing(false)
  }

  const handlePreferenceChange = (key: string) => {
    setPreferences(prev => ({
      ...prev,
      [key]: !prev[key as keyof typeof prev]
    }))
  }

  if (isSubscribed) {
    return (
      <section className="py-20 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 dark:from-green-900/20 dark:via-emerald-900/20 dark:to-teal-900/20">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="h-10 w-10 text-white" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Thank You for Subscribing!
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 mb-8">
              We've sent a verification email to your inbox. Please click the link to confirm 
              your subscription and start receiving our exclusive content.
            </p>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-4">
                What happens next?
              </h3>
              <div className="space-y-3 text-left">
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold text-blue-600 dark:text-blue-400">1</span>
                  </div>
                  <span className="text-gray-600 dark:text-gray-400">Check your email inbox</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold text-blue-600 dark:text-blue-400">2</span>
                  </div>
                  <span className="text-gray-600 dark:text-gray-400">Click the verification link</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold text-blue-600 dark:text-blue-400">3</span>
                  </div>
                  <span className="text-gray-600 dark:text-gray-400">Start receiving exclusive content</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 text-white overflow-hidden relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-grid-pattern"></div>
      </div>

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white/10 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-white/10 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge className="mb-4 px-4 py-2 bg-white/20 text-white border-white/30">
            <Mail className="h-4 w-4 mr-2" />
            Stay Connected
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
              Join Our Newsletter
            </span>
            <br />
            <span className="text-white">Get Exclusive Updates</span>
          </h2>
          <p className="text-xl text-purple-100 max-w-3xl mx-auto leading-relaxed">
            Subscribe to receive the latest deals, industry insights, product updates, 
            and exclusive content delivered straight to your inbox.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Newsletter Form */}
          <div>
            <Card className="bg-white/10 border-white/20 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white text-2xl">
                  Subscribe Now
                </CardTitle>
                <CardDescription className="text-purple-100">
                  Join thousands of subscribers who trust us for the latest updates
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Email Form */}
                <form onSubmit={handleSubscribe} className="space-y-4">
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your email address"
                      className="w-full pl-12 pr-4 py-4 bg-white/20 border border-white/30 rounded-lg text-white placeholder-purple-200 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent backdrop-blur-sm"
                      required
                    />
                  </div>

                  {/* Subscription Preferences */}
                  <div className="space-y-3">
                    <h4 className="text-white font-medium">What would you like to receive?</h4>
                    <div className="grid grid-cols-2 gap-3">
                      {Object.entries(preferences).map(([key, value]) => (
                        <label key={key} className="flex items-center space-x-2 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={value}
                            onChange={() => handlePreferenceChange(key)}
                            className="rounded border-white/30 bg-white/20 text-purple-500 focus:ring-purple-500"
                          />
                          <span className="text-sm text-purple-100 capitalize">
                            {key === 'deals' ? 'Exclusive Deals' : 
                             key === 'insights' ? 'Industry Insights' :
                             key === 'products' ? 'Product Updates' : 'Events & Webinars'}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <Button
                    type="submit"
                    disabled={isSubscribing}
                    className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-black font-semibold py-4 text-lg group"
                  >
                    {isSubscribing ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black mr-2"></div>
                        Subscribing...
                      </>
                    ) : (
                      <>
                        Subscribe Now
                        <Send className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                      </>
                    )}
                  </Button>
                </form>

                {/* Privacy Notice */}
                <div className="text-xs text-purple-200 bg-white/5 rounded-lg p-3">
                  <div className="flex items-start space-x-2">
                    <Shield className="h-4 w-4 flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-medium mb-1">Your Privacy is Protected</p>
                      <p>We respect your privacy and will never share your email address. 
                      You can unsubscribe at any time with one click.</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Benefits & Stats */}
          <div className="space-y-8">
            {/* Benefits */}
            <div>
              <h3 className="text-2xl font-bold text-white mb-6">
                Why Subscribe?
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {benefits.map((benefit, index) => {
                  const Icon = benefit.icon
                  return (
                    <div key={index} className="bg-white/10 rounded-lg p-4 backdrop-blur-sm border border-white/20">
                      <div className="flex items-start space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-lg flex items-center justify-center flex-shrink-0">
                          <Icon className="h-5 w-5 text-black" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-white mb-1">
                            {benefit.title}
                          </h4>
                          <p className="text-sm text-purple-100">
                            {benefit.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Stats */}
            <div>
              <h3 className="text-2xl font-bold text-white mb-6">
                Join Our Community
              </h3>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                {stats.map((stat, index) => {
                  const Icon = stat.icon
                  return (
                    <div key={index} className="text-center">
                      <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-2">
                        <Icon className="h-6 w-6 text-yellow-400" />
                      </div>
                      <div className="text-2xl font-bold text-white">
                        {stat.value}
                      </div>
                      <div className="text-sm text-purple-200">
                        {stat.label}
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Testimonial */}
            <div className="bg-white/10 rounded-lg p-6 backdrop-blur-sm border border-white/20">
              <div className="flex items-center space-x-1 mb-3">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-purple-100 mb-4 italic">
                "The newsletter is packed with valuable insights and exclusive deals. 
                It's become an essential part of my weekly reading!"
              </p>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-full flex items-center justify-center">
                  <span className="text-black font-bold text-sm">JD</span>
                </div>
                <div>
                  <p className="text-white font-medium text-sm">John Doe</p>
                  <p className="text-purple-200 text-xs">Tech Entrepreneur</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-white/5 rounded-2xl p-8 backdrop-blur-sm border border-white/10">
            <h3 className="text-2xl font-bold text-white mb-4">
              Ready to Stay Ahead of the Curve?
            </h3>
            <p className="text-purple-100 mb-6 max-w-2xl mx-auto">
              Don't miss out on exclusive opportunities, industry insights, and special offers. 
              Join our growing community of informed subscribers today.
            </p>
            <div className="flex items-center justify-center space-x-4 text-sm text-purple-200">
              <div className="flex items-center space-x-1">
                <Clock className="h-4 w-4" />
                <span>Weekly Updates</span>
              </div>
              <div className="w-px h-4 bg-white/20" />
              <div className="flex items-center space-x-1">
                <Shield className="h-4 w-4" />
                <span>No Spam</span>
              </div>
              <div className="w-px h-4 bg-white/20" />
              <div className="flex items-center space-x-1">
                <CheckCircle className="h-4 w-4" />
                <span>Easy Unsubscribe</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
