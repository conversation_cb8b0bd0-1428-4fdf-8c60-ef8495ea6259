"use client"

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useLanguage } from '@/components/providers/language-provider'
import {
  MapPin,
  Star,
  Users,
  Package,
  Shield,
  Award,
  Globe,
  MessageCircle,
  ArrowRight,
  CheckCircle,
  Factory,
  Truck,
} from 'lucide-react'

interface Supplier {
  id: string
  name: string
  nameAr: string
  city: string
  province: string
  specialties: string[]
  specialtiesAr: string[]
  rating: number
  yearsInBusiness: number
  productsCount: number
  verified: boolean
  certifications: string[]
  image: string
  description: string
  descriptionAr: string
  contactInfo: {
    whatsapp: string
    wechat: string
    email: string
  }
}

const featuredSuppliers: Supplier[] = [
  {
    id: '1',
    name: 'Guangzhou Electronics Manufacturing Co.',
    nameAr: 'شركة قوانغتشو لتصنيع الإلكترونيات',
    city: 'Guangzhou',
    province: 'Guangdong',
    specialties: ['Consumer Electronics', 'Smartphones', 'Audio Equipment'],
    specialtiesAr: ['الإلكترونيات الاستهلاكية', 'الهواتف الذكية', 'معدات الصوت'],
    rating: 4.8,
    yearsInBusiness: 15,
    productsCount: 2500,
    verified: true,
    certifications: ['ISO 9001', 'CE', 'FCC', 'RoHS'],
    image: '/supplier-guangzhou-electronics.jpg',
    description: 'Leading manufacturer of consumer electronics with 15 years of experience serving global markets.',
    descriptionAr: 'شركة رائدة في تصنيع الإلكترونيات الاستهلاكية مع 15 عامًا من الخبرة في خدمة الأسواق العالمية.',
    contactInfo: {
      whatsapp: '+86-138-1234-5678',
      wechat: 'gzelec_official',
      email: '<EMAIL>'
    }
  },
  {
    id: '2',
    name: 'Yiwu International Trading Hub',
    nameAr: 'مركز ييوو للتجارة الدولية',
    city: 'Yiwu',
    province: 'Zhejiang',
    specialties: ['Home & Garden', 'Textiles', 'Small Commodities'],
    specialtiesAr: ['المنزل والحديقة', 'المنسوجات', 'السلع الصغيرة'],
    rating: 4.6,
    yearsInBusiness: 12,
    productsCount: 5000,
    verified: true,
    certifications: ['ISO 14001', 'BSCI', 'Sedex'],
    image: '/supplier-yiwu-trading.jpg',
    description: 'Comprehensive trading company specializing in small commodities and wholesale distribution.',
    descriptionAr: 'شركة تجارية شاملة متخصصة في السلع الصغيرة والتوزيع بالجملة.',
    contactInfo: {
      whatsapp: '+86-139-8765-4321',
      wechat: 'yiwu_trade_hub',
      email: '<EMAIL>'
    }
  },
  {
    id: '3',
    name: 'Shenzhen Tech Solutions Ltd.',
    nameAr: 'شركة شنتشن للحلول التقنية المحدودة',
    city: 'Shenzhen',
    province: 'Guangdong',
    specialties: ['Industrial Equipment', 'Automation', 'Machinery'],
    specialtiesAr: ['المعدات الصناعية', 'الأتمتة', 'الآلات'],
    rating: 4.9,
    yearsInBusiness: 20,
    productsCount: 1200,
    verified: true,
    certifications: ['ISO 9001', 'ISO 14001', 'OHSAS 18001'],
    image: '/supplier-shenzhen-tech.jpg',
    description: 'Advanced technology solutions provider for industrial automation and manufacturing equipment.',
    descriptionAr: 'مزود حلول تقنية متقدمة للأتمتة الصناعية ومعدات التصنيع.',
    contactInfo: {
      whatsapp: '+86-136-9876-5432',
      wechat: 'sztech_solutions',
      email: '<EMAIL>'
    }
  },
  {
    id: '4',
    name: 'Ningbo Machinery Export Co.',
    nameAr: 'شركة نينغبو لتصدير الآلات',
    city: 'Ningbo',
    province: 'Zhejiang',
    specialties: ['Production Lines', 'Manufacturing Equipment', 'Raw Materials'],
    specialtiesAr: ['خطوط الإنتاج', 'معدات التصنيع', 'المواد الخام'],
    rating: 4.7,
    yearsInBusiness: 18,
    productsCount: 800,
    verified: true,
    certifications: ['CE', 'ISO 9001', 'SGS'],
    image: '/supplier-ningbo-machinery.jpg',
    description: 'Specialized in heavy machinery and production line equipment for various industries.',
    descriptionAr: 'متخصصة في الآلات الثقيلة ومعدات خطوط الإنتاج لمختلف الصناعات.',
    contactInfo: {
      whatsapp: '+86-137-5555-6666',
      wechat: 'ningbo_machinery',
      email: '<EMAIL>'
    }
  }
]

export function SuppliersSection() {
  const { language } = useLanguage()
  const [currentSupplier, setCurrentSupplier] = useState(0)
  const isArabic = language === 'ar'

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSupplier((prev) => (prev + 1) % featuredSuppliers.length)
    }, 8000)
    return () => clearInterval(timer)
  }, [])

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating)
            ? 'text-yellow-400 fill-current'
            : 'text-gray-300'
        }`}
      />
    ))
  }

  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className={`text-center mb-16 ${isArabic ? 'rtl' : 'ltr'}`}>
          <Badge className="mb-4 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            <Users className="h-4 w-4 mr-2" />
            {isArabic ? 'موردونا الموثقون' : 'Our Verified Suppliers'}
          </Badge>
          
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            {isArabic ? 'تواصل مع أفضل الموردين الصينيين' : 'Connect with Top Chinese Suppliers'}
          </h2>
          
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            {isArabic 
              ? 'شبكة من الموردين الموثقين والمعتمدين في الصين، جاهزون لخدمة احتياجاتك التجارية'
              : 'A network of verified and certified suppliers in China, ready to serve your business needs'
            }
          </p>
        </div>

        {/* Featured Supplier Spotlight */}
        <div className="mb-16">
          <Card className="overflow-hidden bg-white dark:bg-gray-800 shadow-xl">
            <CardContent className="p-0">
              <div className="grid grid-cols-1 lg:grid-cols-2">
                {/* Supplier Image */}
                <div className="relative h-64 lg:h-auto bg-gradient-to-br from-blue-500 to-blue-700">
                  <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Factory className="h-24 w-24 text-white opacity-50" />
                  </div>
                  <div className="absolute top-4 left-4">
                    <Badge className="bg-green-500 text-white">
                      <Shield className="h-3 w-3 mr-1" />
                      {isArabic ? 'موثق' : 'Verified'}
                    </Badge>
                  </div>
                </div>

                {/* Supplier Info */}
                <div className="p-8 space-y-6">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {isArabic 
                        ? featuredSuppliers[currentSupplier].nameAr 
                        : featuredSuppliers[currentSupplier].name
                      }
                    </h3>
                    <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
                      <MapPin className="h-4 w-4" />
                      <span>
                        {featuredSuppliers[currentSupplier].city}, {featuredSuppliers[currentSupplier].province}, China
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      {renderStars(featuredSuppliers[currentSupplier].rating)}
                      <span className="text-sm font-medium ml-2">
                        {featuredSuppliers[currentSupplier].rating}
                      </span>
                    </div>
                    <Badge variant="outline">
                      {featuredSuppliers[currentSupplier].yearsInBusiness} {isArabic ? 'سنة خبرة' : 'years'}
                    </Badge>
                  </div>

                  <p className="text-gray-600 dark:text-gray-400">
                    {isArabic 
                      ? featuredSuppliers[currentSupplier].descriptionAr
                      : featuredSuppliers[currentSupplier].description
                    }
                  </p>

                  {/* Specialties */}
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                      {isArabic ? 'التخصصات:' : 'Specialties:'}
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {(isArabic 
                        ? featuredSuppliers[currentSupplier].specialtiesAr
                        : featuredSuppliers[currentSupplier].specialties
                      ).map((specialty, index) => (
                        <Badge key={index} variant="secondary">
                          {specialty}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <Package className="h-6 w-6 text-blue-600 mx-auto mb-1" />
                      <div className="font-bold text-lg">
                        {featuredSuppliers[currentSupplier].productsCount.toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">
                        {isArabic ? 'منتج' : 'Products'}
                      </div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <Award className="h-6 w-6 text-green-600 mx-auto mb-1" />
                      <div className="font-bold text-lg">
                        {featuredSuppliers[currentSupplier].certifications.length}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">
                        {isArabic ? 'شهادة' : 'Certifications'}
                      </div>
                    </div>
                  </div>

                  {/* Contact Actions */}
                  <div className="flex space-x-3">
                    <Button className="flex-1">
                      <MessageCircle className="h-4 w-4 mr-2" />
                      {isArabic ? 'تواصل الآن' : 'Contact Now'}
                    </Button>
                    <Button variant="outline">
                      <Globe className="h-4 w-4 mr-2" />
                      {isArabic ? 'عرض المنتجات' : 'View Products'}
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Supplier Navigation */}
          <div className="flex justify-center space-x-2 mt-6">
            {featuredSuppliers.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSupplier(index)}
                className={`w-3 h-3 rounded-full transition-all ${
                  index === currentSupplier
                    ? 'bg-blue-600'
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Supplier Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {featuredSuppliers.map((supplier, index) => (
            <Card key={supplier.id} className="group hover:shadow-lg transition-all duration-300">
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Supplier Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 dark:text-white line-clamp-2">
                        {isArabic ? supplier.nameAr : supplier.name}
                      </h3>
                      <div className="flex items-center space-x-1 mt-1">
                        <MapPin className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-500">{supplier.city}</span>
                      </div>
                    </div>
                    {supplier.verified && (
                      <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        {isArabic ? 'موثق' : 'Verified'}
                      </Badge>
                    )}
                  </div>

                  {/* Rating */}
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center space-x-1">
                      {renderStars(supplier.rating)}
                    </div>
                    <span className="text-sm font-medium">{supplier.rating}</span>
                  </div>

                  {/* Quick Stats */}
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="text-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                      <div className="font-semibold">{supplier.yearsInBusiness}</div>
                      <div className="text-gray-500">{isArabic ? 'سنة' : 'Years'}</div>
                    </div>
                    <div className="text-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                      <div className="font-semibold">{supplier.productsCount}</div>
                      <div className="text-gray-500">{isArabic ? 'منتج' : 'Products'}</div>
                    </div>
                  </div>

                  {/* Contact Button */}
                  <Button size="sm" className="w-full group-hover:bg-blue-600 transition-colors">
                    <MessageCircle className="h-3 w-3 mr-2" />
                    {isArabic ? 'تواصل' : 'Contact'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              {isArabic ? 'هل تريد أن تصبح مورداً معتمداً؟' : 'Want to become a verified supplier?'}
            </h3>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              {isArabic 
                ? 'انضم إلى شبكتنا من الموردين الموثقين واحصل على وصول مباشر للتجار العرب'
                : 'Join our network of verified suppliers and get direct access to Arab merchants'
              }
            </p>
            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                {isArabic ? 'سجل كمورد' : 'Register as Supplier'}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                {isArabic ? 'تصفح جميع الموردين' : 'Browse All Suppliers'}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
