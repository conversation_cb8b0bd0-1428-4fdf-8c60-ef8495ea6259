import { useQuery } from '@tanstack/react-query'

export interface Service {
  id: string
  name: string
  slug: string
  description: string
  shortDescription?: string
  price?: number
  duration?: number
  categoryId: string
  features: string[]
  images: string[]
  status: string
  featured: boolean
  seoTitle?: string
  seoDescription?: string
  createdAt: string
  updatedAt: string
  category: {
    id: string
    name: string
    slug: string
  }
  relatedServices?: Service[]
}

export interface ServicesResponse {
  services: Service[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface ServicesParams {
  page?: number
  limit?: number
  category?: string
  search?: string
  featured?: boolean
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export function useServices(params: ServicesParams = {}) {
  const queryParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, value.toString())
    }
  })

  return useQuery<ServicesResponse>({
    queryKey: ['services', params],
    queryFn: async () => {
      const response = await fetch(`/api/services?${queryParams.toString()}`)
      if (!response.ok) {
        throw new Error('Failed to fetch services')
      }
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useService(slug: string) {
  return useQuery<Service>({
    queryKey: ['service', slug],
    queryFn: async () => {
      const response = await fetch(`/api/services/${slug}`)
      if (!response.ok) {
        throw new Error('Failed to fetch service')
      }
      return response.json()
    },
    enabled: !!slug,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useFeaturedServices(limit: number = 6) {
  return useServices({
    featured: true,
    limit,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })
}
