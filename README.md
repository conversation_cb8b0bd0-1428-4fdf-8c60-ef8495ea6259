# 🇨🇳🤝🇸🇦 SINO-ARAB TRADE - China-Arab Wholesale Marketplace

A comprehensive wholesale marketplace platform connecting Chinese suppliers with Arab merchants, featuring professional services, quality control, and complete trade facilitation.

![Sino-Arab Trade](https://img.shields.io/badge/Sino--Arab--Trade-v2.0.0-blue.svg)
![Next.js](https://img.shields.io/badge/Next.js-14-black.svg)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)
![Prisma](https://img.shields.io/badge/Prisma-5.0-green.svg)
![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-3.0-blue.svg)
![Arabic Support](https://img.shields.io/badge/Arabic-RTL%20Support-green.svg)

## ✨ Features

### 🏭 **Wholesale Business Model**
- **Wholesale Products**: Products with MOQ (Minimum Order Quantity) and tiered pricing
- **Supplier Network**: Verified Chinese suppliers with ratings and certifications
- **Stock Liquidation**: Special bulk deals and overstock clearance
- **Affiliate Marketing**: External product links with commission tracking
- **Production Lines**: Industrial machinery and equipment showcase
- **Raw Materials**: Manufacturing materials and components

### 🛠️ **Professional Services**
- **Product Inspection**: Quality control and pre-shipment inspection services
- **Warehouse Storage**: Secure storage solutions with inventory management
- **International Shipping**: Sea freight, air freight, and door-to-door delivery
- **Product Certification**: CE, ISO, FDA, SASO, and other required certifications
- **Business Consulting**: Sourcing strategy, supplier evaluation, and market research
- **Quality Control**: Comprehensive testing and quality assurance services

### 🌍 **China-Arab Trade Focus**
- **Arabic Language Support**: Full RTL (Right-to-Left) language support
- **Cultural Adaptation**: Designed specifically for Arab merchants
- **Chinese Supplier Integration**: Direct connection with verified Chinese manufacturers
- **Middle East Shipping**: Specialized shipping routes to Arab countries
- **Local Support**: Arabic-speaking customer service team
- **Currency Support**: USD, CNY, and regional currency options

### 👥 **Merchant Management**
- **Merchant Profiles**: Comprehensive business profiles for Arab importers
- **Verification System**: Business license and import license verification
- **Credit Management**: Credit limits and payment terms for wholesale customers
- **Order History**: Complete transaction history and repeat order management
- **Communication Tools**: WhatsApp, WeChat, and email integration

### 📊 **Advanced Analytics**
- **Business Intelligence**: Real-time metrics for wholesale operations
- **Supplier Performance**: Track supplier ratings, delivery times, and quality
- **Market Analysis**: Product demand trends and pricing analytics
- **Customer Insights**: Merchant behavior and purchasing patterns
- **Financial Reporting**: Revenue, profit margins, and commission tracking

### 🔧 **Service Request System**
- **Service Booking**: Online booking for inspection, shipping, and certification
- **Request Tracking**: Real-time status updates and progress tracking
- **Document Management**: Digital certificates, reports, and documentation
- **Supplier Assignment**: Automatic assignment to qualified service providers
- **Quality Assurance**: Service completion verification and customer feedback

## 🛠️ **Technology Stack**

### **Frontend**
- **Next.js 14**: React framework with App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first CSS framework
- **Shadcn/ui**: Modern component library
- **React Query**: Data fetching and caching

### **Backend**
- **Next.js API Routes**: Serverless API endpoints
- **Prisma ORM**: Type-safe database access
- **SQLite**: Development database (easily switchable to PostgreSQL/MySQL)
- **NextAuth.js**: Authentication and session management

### **State Management**
- **Zustand**: Lightweight state management for cart and wishlist
- **React Context**: Global app state and theming
- **React Query**: Server state management and caching

## 🚀 **Getting Started**

### **Prerequisites**
- Node.js 18+
- npm or yarn
- Git

### **Installation**

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/aidevcommerce.git
   cd aidevcommerce
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

4. **Set up the database**
   ```bash
   npm run db:push
   npm run db:seed
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🔧 **Available Scripts**

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production
npm run start           # Start production server

# Database
npm run db:push         # Push schema changes to database
npm run db:seed         # Seed database with sample data
npm run db:studio       # Open Prisma Studio
```

## 🌟 **Current Status**

✅ **Fully Functional E-commerce Platform**
- Live application running on http://localhost:3000
- Real database with comprehensive sample data
- Working authentication (login/register/logout)
- Shopping features (cart, wishlist, product browsing)
- Admin panel with real-time statistics
- All components using real API data
- Performance optimized with monitoring

## 📁 **Project Structure**

```
aidevcommerce/
├── src/
│   ├── app/                    # Next.js App Router pages
│   ├── components/            # Reusable components
│   ├── hooks/                 # Custom React hooks
│   ├── lib/                   # Utility libraries
│   ├── store/                 # Zustand stores
│   └── styles/                # Global styles
├── prisma/                    # Database schema and migrations
└── public/                    # Static assets
```

## 🚀 **Deployment**

### **Vercel (Recommended)**
1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

## 📄 **License**

This project is licensed under the MIT License.

---

**Built with ❤️ by the AIDEVCOMMERCE Team**
