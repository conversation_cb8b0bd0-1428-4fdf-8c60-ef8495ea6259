"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useCart } from "@/components/providers/cart-provider"
import { useWishlist } from "@/components/providers/wishlist-provider"
import { useToast } from "@/hooks/use-toast"
import { formatPrice } from "@/lib/utils"
import {
  X,
  Star,
  Check,
  Minus,
  ShoppingCart,
  Heart,
  ArrowRight,
  Scale,
  Zap,
  Shield,
  Award,
} from "lucide-react"

interface Product {
  id: string
  name: string
  price: number
  originalPrice?: number
  rating: number
  reviews: number
  image: string
  category: string
  brand: string
  features: string[]
  specifications: Record<string, string>
  inStock: boolean
  badge?: string
}

interface ProductComparisonProps {
  products: Product[]
  onRemoveProduct: (productId: string) => void
  onAddProduct: () => void
  maxProducts?: number
}

export function ProductComparison({ 
  products, 
  onRemoveProduct, 
  onAddProduct, 
  maxProducts = 4 
}: ProductComparisonProps) {
  const [selectedFeature, setSelectedFeature] = useState<string | null>(null)
  const { addToCart } = useCart()
  const { addToWishlist, isInWishlist } = useWishlist()
  const { toast } = useToast()
  const { t } = useLanguage()

  // Get all unique features across products
  const allFeatures = Array.from(
    new Set(products.flatMap(product => product.features))
  )

  // Get all unique specifications across products
  const allSpecs = Array.from(
    new Set(products.flatMap(product => Object.keys(product.specifications)))
  )

  const handleAddToCart = (product: Product) => {
    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      quantity: 1
    })

    toast({
      title: "Added to Cart",
      description: `${product.name} has been added to your cart.`
    })
  }

  const handleAddToWishlist = (product: Product) => {
    addToWishlist({
      id: product.id,
      name: product.name,
      price: product.price,
      originalPrice: product.originalPrice,
      image: product.image,
      rating: product.rating
    })

    toast({
      title: "Added to Wishlist",
      description: `${product.name} has been added to your wishlist.`
    })
  }

  const getBestValue = (products: Product[], key: 'price' | 'rating') => {
    if (key === 'price') {
      return Math.min(...products.map(p => p.price))
    } else {
      return Math.max(...products.map(p => p.rating))
    }
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <Scale className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          No Products to Compare
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Add products to your comparison to see them side by side
        </p>
        <Button onClick={onAddProduct}>
          Add Products to Compare
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Product Comparison
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Compare {products.length} product{products.length !== 1 ? 's' : ''} side by side
          </p>
        </div>
        {products.length < maxProducts && (
          <Button onClick={onAddProduct} variant="outline">
            Add Product
          </Button>
        )}
      </div>

      {/* Comparison Table */}
      <div className="overflow-x-auto">
        <div className="min-w-full">
          {/* Product Headers */}
          <div className="grid gap-4 mb-6" style={{ gridTemplateColumns: `200px repeat(${products.length}, 1fr)` }}>
            <div></div>
            {products.map((product) => (
              <Card key={product.id} className="relative">
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute top-2 right-2 z-10"
                  onClick={() => onRemoveProduct(product.id)}
                >
                  <X className="h-4 w-4" />
                </Button>
                
                <CardContent className="p-4 text-center">
                  {/* Product Image */}
                  <div className="w-full h-32 bg-gray-100 dark:bg-gray-800 rounded-lg mb-4 flex items-center justify-center">
                    <div className="text-4xl">📱</div>
                  </div>

                  {/* Product Info */}
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2 text-sm">
                    {product.name}
                  </h3>
                  
                  {/* Price */}
                  <div className="mb-3">
                    <div className="flex items-center justify-center space-x-2">
                      <span className={`text-lg font-bold ${
                        getBestValue(products, 'price') === product.price 
                          ? 'text-green-600' 
                          : 'text-gray-900 dark:text-white'
                      }`}>
                        {formatPrice(product.price)}
                      </span>
                      {getBestValue(products, 'price') === product.price && (
                        <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 text-xs">
                          Best Price
                        </Badge>
                      )}
                    </div>
                    {product.originalPrice && (
                      <span className="text-sm text-gray-500 line-through">
                        {formatPrice(product.originalPrice)}
                      </span>
                    )}
                  </div>

                  {/* Rating */}
                  <div className="flex items-center justify-center space-x-1 mb-4">
                    <div className="flex items-center space-x-1">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-3 w-3 ${
                            i < Math.floor(product.rating)
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-xs text-gray-600 dark:text-gray-400">
                      ({product.reviews})
                    </span>
                    {getBestValue(products, 'rating') === product.rating && (
                      <Award className="h-3 w-3 text-yellow-500" />
                    )}
                  </div>

                  {/* Actions */}
                  <div className="space-y-2">
                    <Button
                      size="sm"
                      className="w-full"
                      onClick={() => handleAddToCart(product)}
                      disabled={!product.inStock}
                    >
                      <ShoppingCart className="h-3 w-3 mr-1" />
                      Add to Cart
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="w-full"
                      onClick={() => handleAddToWishlist(product)}
                    >
                      <Heart className={`h-3 w-3 mr-1 ${
                        isInWishlist(product.id) ? 'fill-current text-red-500' : ''
                      }`} />
                      Wishlist
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Basic Info Comparison */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">Basic Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Brand */}
                <div className="grid gap-4" style={{ gridTemplateColumns: `200px repeat(${products.length}, 1fr)` }}>
                  <div className="font-medium text-gray-700 dark:text-gray-300 py-2">
                    Brand
                  </div>
                  {products.map((product) => (
                    <div key={product.id} className="py-2 text-center">
                      {product.brand}
                    </div>
                  ))}
                </div>

                {/* Category */}
                <div className="grid gap-4" style={{ gridTemplateColumns: `200px repeat(${products.length}, 1fr)` }}>
                  <div className="font-medium text-gray-700 dark:text-gray-300 py-2">
                    Category
                  </div>
                  {products.map((product) => (
                    <div key={product.id} className="py-2 text-center">
                      <Badge variant="outline">{product.category}</Badge>
                    </div>
                  ))}
                </div>

                {/* Availability */}
                <div className="grid gap-4" style={{ gridTemplateColumns: `200px repeat(${products.length}, 1fr)` }}>
                  <div className="font-medium text-gray-700 dark:text-gray-300 py-2">
                    Availability
                  </div>
                  {products.map((product) => (
                    <div key={product.id} className="py-2 text-center">
                      <Badge className={product.inStock ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                        {product.inStock ? 'In Stock' : 'Out of Stock'}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Features Comparison */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">Features</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {allFeatures.map((feature) => (
                  <div 
                    key={feature} 
                    className="grid gap-4" 
                    style={{ gridTemplateColumns: `200px repeat(${products.length}, 1fr)` }}
                  >
                    <div className="font-medium text-gray-700 dark:text-gray-300 py-2 text-sm">
                      {feature}
                    </div>
                    {products.map((product) => (
                      <div key={product.id} className="py-2 text-center">
                        {product.features.includes(feature) ? (
                          <Check className="h-4 w-4 text-green-600 mx-auto" />
                        ) : (
                          <Minus className="h-4 w-4 text-gray-400 mx-auto" />
                        )}
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Specifications Comparison */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Specifications</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {allSpecs.map((spec) => (
                  <div 
                    key={spec} 
                    className="grid gap-4" 
                    style={{ gridTemplateColumns: `200px repeat(${products.length}, 1fr)` }}
                  >
                    <div className="font-medium text-gray-700 dark:text-gray-300 py-2 text-sm">
                      {spec}
                    </div>
                    {products.map((product) => (
                      <div key={product.id} className="py-2 text-center text-sm">
                        {product.specifications[spec] || '-'}
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Bottom Actions */}
      <div className="flex items-center justify-between pt-6 border-t">
        <div className="text-sm text-gray-600 dark:text-gray-400">
          Comparing {products.length} of {maxProducts} products
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" onClick={() => products.forEach(p => onRemoveProduct(p.id))}>
            Clear All
          </Button>
          <Button>
            <ArrowRight className="h-4 w-4 mr-2" />
            Continue Shopping
          </Button>
        </div>
      </div>
    </div>
  )
}
