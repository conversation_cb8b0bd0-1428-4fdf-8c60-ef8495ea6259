"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { AdminLayout } from "@/components/layout/admin-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useAdminStats } from "@/hooks/use-admin"
import { formatPrice, formatDate } from "@/lib/utils"
import { AdvancedAnalyticsDashboard } from "@/components/analytics/advanced-analytics-dashboard"
import {
  TrendingUp,
  TrendingDown,
  Users,
  ShoppingCart,
  Package,
  DollarSign,
  Eye,
  MessageCircle,
  Star,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  Calendar,
  BarChart3,
  PieChart,
  Activity,
} from "lucide-react"

// Mock dashboard data
const dashboardStats = {
  totalRevenue: {
    value: 45231.89,
    change: 20.1,
    trend: "up" as const,
    period: "from last month"
  },
  totalOrders: {
    value: 1234,
    change: 15.3,
    trend: "up" as const,
    period: "from last month"
  },
  totalCustomers: {
    value: 2847,
    change: 8.7,
    trend: "up" as const,
    period: "from last month"
  },
  totalProducts: {
    value: 567,
    change: -2.1,
    trend: "down" as const,
    period: "from last month"
  }
}

const recentOrders = [
  {
    id: "ORD-2024-001",
    customer: "John Doe",
    email: "<EMAIL>",
    total: 299.99,
    status: "completed",
    date: new Date("2024-01-20"),
    items: 3
  },
  {
    id: "ORD-2024-002",
    customer: "Jane Smith",
    email: "<EMAIL>",
    total: 159.99,
    status: "processing",
    date: new Date("2024-01-19"),
    items: 2
  },
  {
    id: "ORD-2024-003",
    customer: "Mike Johnson",
    email: "<EMAIL>",
    total: 89.99,
    status: "shipped",
    date: new Date("2024-01-18"),
    items: 1
  },
  {
    id: "ORD-2024-004",
    customer: "Sarah Wilson",
    email: "<EMAIL>",
    total: 449.99,
    status: "pending",
    date: new Date("2024-01-17"),
    items: 5
  }
]

const topProducts = [
  {
    id: "1",
    name: "Premium Wireless Headphones",
    sales: 234,
    revenue: 46800,
    image: "/product-1.jpg"
  },
  {
    id: "2",
    name: "Smart Fitness Watch",
    sales: 189,
    revenue: 56700,
    image: "/product-2.jpg"
  },
  {
    id: "3",
    name: "Ergonomic Office Chair",
    sales: 156,
    revenue: 70200,
    image: "/product-3.jpg"
  },
  {
    id: "4",
    name: "Wireless Charging Pad",
    sales: 298,
    revenue: 14900,
    image: "/product-4.jpg"
  }
]

const recentActivity = [
  {
    id: "1",
    type: "order",
    message: "New order #ORD-2024-001 received",
    time: "2 minutes ago",
    icon: ShoppingCart,
    color: "text-green-600"
  },
  {
    id: "2",
    type: "user",
    message: "New customer registration: <EMAIL>",
    time: "15 minutes ago",
    icon: Users,
    color: "text-blue-600"
  },
  {
    id: "3",
    type: "product",
    message: "Product inventory low: Wireless Headphones",
    time: "1 hour ago",
    icon: Package,
    color: "text-orange-600"
  },
  {
    id: "4",
    type: "review",
    message: "New 5-star review for Smart Watch",
    time: "2 hours ago",
    icon: Star,
    color: "text-yellow-600"
  }
]

const getStatusColor = (status: string) => {
  switch (status) {
    case "completed":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    case "processing":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
    case "shipped":
      return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
    case "pending":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
  }
}

export default function AdminDashboard() {
  const { t } = useLanguage()
  const { data: session, status } = useSession()
  const router = useRouter()
  const { data: statsData, isLoading: statsLoading, error: statsError } = useAdminStats()

  useEffect(() => {
    if (status === 'loading') return // Still loading
    if (!session) {
      router.push('/auth/login')
      return
    }
    // Check if user is admin
    if (session.user?.role !== 'admin') {
      router.push('/')
      return
    }
  }, [session, status, router])

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading admin dashboard...</p>
        </div>
      </div>
    )
  }

  if (!session || session.user?.role !== 'admin') {
    return null // Will redirect
  }

  // Loading state
  if (statsLoading) {
    return (
      <AdminLayout>
        <div className="space-y-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Dashboard
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Loading dashboard data...
              </p>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
                  <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-8 w-24 bg-gray-200 rounded animate-pulse mb-2"></div>
                  <div className="h-4 w-32 bg-gray-200 rounded animate-pulse"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </AdminLayout>
    )
  }

  // Error state
  if (statsError) {
    return (
      <AdminLayout>
        <div className="space-y-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Dashboard
              </h1>
              <p className="text-red-600 dark:text-red-400 mt-1">
                Failed to load dashboard data. Please try again.
              </p>
            </div>
          </div>
        </div>
      </AdminLayout>
    )
  }

  const stats = statsData?.stats

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Welcome back! Here's what's happening with your store.
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              Last 30 days
            </Button>
            <Button>
              <BarChart3 className="h-4 w-4 mr-2" />
              View Reports
            </Button>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats?.totalUsers.value || 0}
              </div>
              <div className="flex items-center text-xs text-muted-foreground">
                {stats?.totalUsers.trend === "up" ? (
                  <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
                ) : stats?.totalUsers.trend === "down" ? (
                  <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
                ) : (
                  <Activity className="h-4 w-4 text-gray-500 mr-1" />
                )}
                <span className={
                  stats?.totalUsers.trend === "up" ? "text-green-600" :
                  stats?.totalUsers.trend === "down" ? "text-red-600" : "text-gray-600"
                }>
                  {stats?.totalUsers.change.toFixed(1)}%
                </span>
                <span className="ml-1">{stats?.totalUsers.period}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Products</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats?.totalProducts.value || 0}
              </div>
              <div className="flex items-center text-xs text-muted-foreground">
                {stats?.totalProducts.trend === "up" ? (
                  <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
                ) : stats?.totalProducts.trend === "down" ? (
                  <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
                ) : (
                  <Activity className="h-4 w-4 text-gray-500 mr-1" />
                )}
                <span className={
                  stats?.totalProducts.trend === "up" ? "text-green-600" :
                  stats?.totalProducts.trend === "down" ? "text-red-600" : "text-gray-600"
                }>
                  {stats?.totalProducts.change.toFixed(1)}%
                </span>
                <span className="ml-1">{stats?.totalProducts.period}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Services</CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats?.totalServices.value || 0}
              </div>
              <div className="flex items-center text-xs text-muted-foreground">
                {stats?.totalServices.trend === "up" ? (
                  <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
                ) : stats?.totalServices.trend === "down" ? (
                  <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
                ) : (
                  <Activity className="h-4 w-4 text-gray-500 mr-1" />
                )}
                <span className={
                  stats?.totalServices.trend === "up" ? "text-green-600" :
                  stats?.totalServices.trend === "down" ? "text-red-600" : "text-gray-600"
                }>
                  {stats?.totalServices.change.toFixed(1)}%
                </span>
                <span className="ml-1">{stats?.totalServices.period}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Blog Posts</CardTitle>
              <MessageCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats?.totalBlogPosts.value || 0}
              </div>
              <div className="flex items-center text-xs text-muted-foreground">
                {stats?.totalBlogPosts.trend === "up" ? (
                  <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
                ) : stats?.totalBlogPosts.trend === "down" ? (
                  <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
                ) : (
                  <Activity className="h-4 w-4 text-gray-500 mr-1" />
                )}
                <span className={
                  stats?.totalBlogPosts.trend === "up" ? "text-green-600" :
                  stats?.totalBlogPosts.trend === "down" ? "text-red-600" : "text-gray-600"
                }>
                  {stats?.totalBlogPosts.change.toFixed(1)}%
                </span>
                <span className="ml-1">{stats?.totalBlogPosts.period}</span>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Orders */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Recent Orders</CardTitle>
                <Button variant="ghost" size="sm">
                  View All
                </Button>
              </div>
              <CardDescription>
                Latest orders from your customers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentOrders.map((order) => (
                  <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                        <ShoppingCart className="h-5 w-5 text-gray-500" />
                      </div>
                      <div>
                        <p className="font-medium text-sm">{order.id}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {order.customer} • {order.items} items
                        </p>
                        <p className="text-xs text-gray-400 dark:text-gray-500">
                          {formatDate(order.date)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        {formatPrice(order.total)}
                      </p>
                      <Badge className={`text-xs ${getStatusColor(order.status)}`}>
                        {order.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Top Products */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Top Products</CardTitle>
                <Button variant="ghost" size="sm">
                  View All
                </Button>
              </div>
              <CardDescription>
                Best performing products this month
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topProducts.map((product, index) => (
                  <div key={product.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                        <span className="text-sm font-semibold text-primary-600 dark:text-primary-400">
                          {index + 1}
                        </span>
                      </div>
                      <div className="w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                        <Package className="h-5 w-5 text-gray-500" />
                      </div>
                      <div>
                        <p className="font-medium text-sm line-clamp-1">
                          {product.name}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {product.sales} sales
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        {formatPrice(product.revenue)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Activity */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="h-5 w-5 mr-2" />
                Recent Activity
              </CardTitle>
              <CardDescription>
                Latest activities in your store
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-4">
                    <div className={`p-2 rounded-full bg-gray-100 dark:bg-gray-800 ${activity.color}`}>
                      <activity.icon className="h-4 w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {activity.message}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 flex items-center mt-1">
                        <Clock className="h-3 w-3 mr-1" />
                        {activity.time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Stats</CardTitle>
              <CardDescription>
                Key metrics at a glance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Eye className="h-4 w-4 text-blue-500" />
                  <span className="text-sm">Page Views</span>
                </div>
                <span className="font-semibold">12,345</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <MessageCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Support Tickets</span>
                </div>
                <span className="font-semibold">23</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Star className="h-4 w-4 text-yellow-500" />
                  <span className="text-sm">Avg. Rating</span>
                </div>
                <span className="font-semibold">4.8</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-4 w-4 text-purple-500" />
                  <span className="text-sm">Conversion Rate</span>
                </div>
                <span className="font-semibold">3.2%</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Advanced Analytics Dashboard */}
        <div className="mt-12">
          <AdvancedAnalyticsDashboard />
        </div>
      </div>
    </AdminLayout>
  )
}
