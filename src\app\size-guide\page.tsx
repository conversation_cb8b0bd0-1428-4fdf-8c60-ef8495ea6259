"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import {
  Ruler,
  Shirt,
  Watch,
  Smartphone,
  Laptop,
  Headphones,
  Calculator,
  ArrowRight,
  Info,
  CheckCircle,
  AlertTriangle,
} from "lucide-react"

export default function SizeGuidePage() {
  const [selectedCategory, setSelectedCategory] = useState("clothing")
  const [measurements, setMeasurements] = useState({
    chest: "",
    waist: "",
    height: "",
    weight: ""
  })
  const { t } = useLanguage()

  const categories = [
    { id: "clothing", name: "<PERSON>lot<PERSON> & Apparel", icon: Shirt },
    { id: "wearables", name: "Wearables & Watches", icon: Watch },
    { id: "electronics", name: "Electronics", icon: Smartphone },
    { id: "accessories", name: "Accessories", icon: Headphones }
  ]

  const clothingSizes = {
    men: [
      { size: "XS", chest: "32-34", waist: "26-28", length: "26" },
      { size: "S", chest: "34-36", waist: "28-30", length: "27" },
      { size: "M", chest: "36-38", waist: "30-32", length: "28" },
      { size: "L", chest: "38-40", waist: "32-34", length: "29" },
      { size: "XL", chest: "40-42", waist: "34-36", length: "30" },
      { size: "XXL", chest: "42-44", waist: "36-38", length: "31" }
    ],
    women: [
      { size: "XS", chest: "30-32", waist: "24-26", hips: "34-36" },
      { size: "S", chest: "32-34", waist: "26-28", hips: "36-38" },
      { size: "M", chest: "34-36", waist: "28-30", hips: "38-40" },
      { size: "L", chest: "36-38", waist: "30-32", hips: "40-42" },
      { size: "XL", chest: "38-40", waist: "32-34", hips: "42-44" },
      { size: "XXL", chest: "40-42", waist: "34-36", hips: "44-46" }
    ]
  }

  const watchSizes = [
    { size: "Small", wrist: "5.5-6.5", caseSize: "38-40mm", description: "Perfect for smaller wrists" },
    { size: "Medium", wrist: "6.5-7.5", caseSize: "40-42mm", description: "Most popular size" },
    { size: "Large", wrist: "7.5-8.5", caseSize: "42-44mm", description: "For larger wrists" },
    { size: "Extra Large", wrist: "8.5+", caseSize: "44mm+", description: "Maximum comfort" }
  ]

  const electronicsSizes = [
    {
      category: "Smartphones",
      items: [
        { name: "Compact", dimensions: "5.4\" x 2.6\" x 0.3\"", weight: "4-5 oz", description: "Easy one-hand use" },
        { name: "Standard", dimensions: "6.1\" x 3.0\" x 0.3\"", weight: "5-6 oz", description: "Perfect balance" },
        { name: "Plus/Pro", dimensions: "6.7\" x 3.3\" x 0.3\"", weight: "6-7 oz", description: "Large screen experience" }
      ]
    },
    {
      category: "Laptops",
      items: [
        { name: "Ultrabook", dimensions: "12-13\"", weight: "2-3 lbs", description: "Ultra-portable" },
        { name: "Standard", dimensions: "14-15\"", weight: "3-4 lbs", description: "Best for most users" },
        { name: "Gaming/Workstation", dimensions: "15-17\"", weight: "4-6 lbs", description: "High performance" }
      ]
    }
  ]

  const measurementTips = [
    {
      title: "Chest Measurement",
      description: "Measure around the fullest part of your chest, keeping the tape horizontal",
      icon: Ruler
    },
    {
      title: "Waist Measurement",
      description: "Measure around your natural waistline, above your hip bones",
      icon: Ruler
    },
    {
      title: "Hip Measurement",
      description: "Measure around the fullest part of your hips",
      icon: Ruler
    },
    {
      title: "Wrist Measurement",
      description: "Measure around your wrist bone where you would wear a watch",
      icon: Watch
    }
  ]

  const getSizeRecommendation = () => {
    const chest = parseFloat(measurements.chest)
    const waist = parseFloat(measurements.waist)
    
    if (!chest || !waist) return null

    // Simple size recommendation logic
    if (chest <= 34 && waist <= 28) return "S"
    if (chest <= 36 && waist <= 30) return "M"
    if (chest <= 38 && waist <= 32) return "L"
    if (chest <= 40 && waist <= 34) return "XL"
    return "XXL"
  }

  const renderClothingSizes = () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Men's Sizes</h3>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse border border-gray-300 dark:border-gray-600">
            <thead>
              <tr className="bg-gray-50 dark:bg-gray-800">
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">Size</th>
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">Chest (inches)</th>
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">Waist (inches)</th>
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">Length (inches)</th>
              </tr>
            </thead>
            <tbody>
              {clothingSizes.men.map((size) => (
                <tr key={size.size}>
                  <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 font-medium">{size.size}</td>
                  <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">{size.chest}</td>
                  <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">{size.waist}</td>
                  <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">{size.length}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Women's Sizes</h3>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse border border-gray-300 dark:border-gray-600">
            <thead>
              <tr className="bg-gray-50 dark:bg-gray-800">
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">Size</th>
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">Chest (inches)</th>
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">Waist (inches)</th>
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">Hips (inches)</th>
              </tr>
            </thead>
            <tbody>
              {clothingSizes.women.map((size) => (
                <tr key={size.size}>
                  <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 font-medium">{size.size}</td>
                  <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">{size.chest}</td>
                  <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">{size.waist}</td>
                  <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">{size.hips}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )

  const renderWearableSizes = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Watch & Wearable Sizes</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {watchSizes.map((size) => (
          <Card key={size.size}>
            <CardContent className="p-6">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">{size.size}</h4>
              <div className="space-y-2 text-sm">
                <p><span className="font-medium">Wrist Size:</span> {size.wrist} inches</p>
                <p><span className="font-medium">Case Size:</span> {size.caseSize}</p>
                <p className="text-gray-600 dark:text-gray-400">{size.description}</p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )

  const renderElectronicsSizes = () => (
    <div className="space-y-8">
      {electronicsSizes.map((category) => (
        <div key={category.category}>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">{category.category}</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {category.items.map((item) => (
              <Card key={item.name}>
                <CardContent className="p-6">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-3">{item.name}</h4>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-medium">Dimensions:</span> {item.dimensions}</p>
                    <p><span className="font-medium">Weight:</span> {item.weight}</p>
                    <p className="text-gray-600 dark:text-gray-400">{item.description}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      ))}
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-600 to-primary-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Size Guide
            </h1>
            <p className="text-xl text-primary-100 mb-8">
              Find the perfect fit with our comprehensive sizing charts and measurement guides
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Badge className="bg-white/20 text-white px-4 py-2">
                <Ruler className="h-4 w-4 mr-2" />
                Accurate Measurements
              </Badge>
              <Badge className="bg-white/20 text-white px-4 py-2">
                <Calculator className="h-4 w-4 mr-2" />
                Size Calculator
              </Badge>
              <Badge className="bg-white/20 text-white px-4 py-2">
                <CheckCircle className="h-4 w-4 mr-2" />
                Perfect Fit Guarantee
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Size Calculator */}
      <section className="py-16 -mt-8 relative z-10">
        <div className="container mx-auto px-4">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-center flex items-center justify-center">
                <Calculator className="h-5 w-5 mr-2" />
                Size Calculator
              </CardTitle>
              <CardDescription className="text-center">
                Enter your measurements to get personalized size recommendations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Chest (inches)
                  </label>
                  <input
                    type="number"
                    value={measurements.chest}
                    onChange={(e) => setMeasurements({...measurements, chest: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="e.g., 36"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Waist (inches)
                  </label>
                  <input
                    type="number"
                    value={measurements.waist}
                    onChange={(e) => setMeasurements({...measurements, waist: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="e.g., 30"
                  />
                </div>
              </div>
              
              {getSizeRecommendation() && (
                <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                    <span className="font-medium text-green-900 dark:text-green-100">
                      Recommended Size: {getSizeRecommendation()}
                    </span>
                  </div>
                  <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                    Based on your measurements, we recommend size {getSizeRecommendation()} for the best fit.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Category Navigation */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Choose Your Category
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Select a product category to view specific sizing information and charts
            </p>
          </div>

          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category) => {
              const Icon = category.icon
              return (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  onClick={() => setSelectedCategory(category.id)}
                  className="flex items-center space-x-2"
                >
                  <Icon className="h-4 w-4" />
                  <span>{category.name}</span>
                </Button>
              )
            })}
          </div>

          {/* Size Charts */}
          <div className="max-w-6xl mx-auto">
            {selectedCategory === "clothing" && renderClothingSizes()}
            {selectedCategory === "wearables" && renderWearableSizes()}
            {selectedCategory === "electronics" && renderElectronicsSizes()}
            {selectedCategory === "accessories" && (
              <div className="text-center py-12">
                <Headphones className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  Accessories Sizing
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Most accessories are one-size-fits-all or have adjustable features. 
                  Check individual product descriptions for specific sizing information.
                </p>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Measurement Tips */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              How to Measure
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Follow these tips to get accurate measurements for the best fit
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {measurementTips.map((tip) => {
              const Icon = tip.icon
              return (
                <Card key={tip.title}>
                  <CardContent className="p-6 text-center">
                    <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <Icon className="h-6 w-6 text-primary-600 dark:text-primary-400" />
                    </div>
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                      {tip.title}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {tip.description}
                    </p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Important Notes */}
      <section className="py-16 bg-amber-50 dark:bg-amber-900/20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-start space-x-4">
              <AlertTriangle className="h-6 w-6 text-amber-600 dark:text-amber-400 flex-shrink-0 mt-1" />
              <div>
                <h3 className="text-lg font-semibold text-amber-900 dark:text-amber-100 mb-2">
                  Important Sizing Notes
                </h3>
                <ul className="space-y-2 text-sm text-amber-800 dark:text-amber-200">
                  <li>• Sizes may vary slightly between different brands and manufacturers</li>
                  <li>• When in doubt, we recommend choosing the larger size for comfort</li>
                  <li>• Check individual product pages for brand-specific sizing information</li>
                  <li>• Contact customer service if you need help choosing the right size</li>
                  <li>• We offer free exchanges if the size doesn't fit perfectly</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Support */}
      <section className="py-16 bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Need Help with Sizing?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Our customer service team is here to help you find the perfect fit
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button className="bg-white text-primary-600 hover:bg-gray-100">
              Contact Support
            </Button>
            <Button variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600">
              Live Chat
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
