"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import {
  FileText,
  Scale,
  ShoppingCart,
  CreditCard,
  Truck,
  RotateCcw,
  Shield,
  AlertTriangle,
  CheckCircle,
  Calendar,
  Mail,
  Phone,
  Globe,
  User,
} from "lucide-react"

export default function TermsPage() {
  const { t } = useLanguage()

  const lastUpdated = "January 15, 2024"
  const effectiveDate = "January 15, 2024"

  const sections = [
    {
      id: "acceptance",
      title: "Acceptance of Terms",
      icon: CheckCircle,
      content: "By accessing and using the AIDEVCOMMERCE website and services, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service. These terms apply to all visitors, users, and others who access or use the service."
    },
    {
      id: "definitions",
      title: "Definitions",
      icon: FileText,
      content: "In these Terms of Service: 'Company' (or 'we' or 'us' or 'our') refers to AIDEVCOMMERCE. 'You' refers to the individual accessing or using the service. 'Service' refers to the AIDEVCOMMERCE website, mobile applications, and related services. 'Content' refers to all text, graphics, images, music, software, audio, video, information, or other materials."
    },
    {
      id: "account-registration",
      title: "Account Registration",
      icon: User,
      content: "To access certain features of our service, you must register for an account. You agree to provide accurate, current, and complete information during registration and to update such information to keep it accurate, current, and complete. You are responsible for safeguarding your password and for all activities under your account."
    },
    {
      id: "purchases-payments",
      title: "Purchases and Payments",
      icon: CreditCard,
      content: "All purchases are subject to product availability. We reserve the right to limit quantities and to refuse service to anyone. Prices are subject to change without notice. Payment must be received by us before shipment of goods. We accept major credit cards, PayPal, and other payment methods as indicated on our website. All payments are processed securely."
    },
    {
      id: "shipping-delivery",
      title: "Shipping and Delivery",
      icon: Truck,
      content: "We will arrange for shipment of products to you. Title and risk of loss pass to you upon delivery to the carrier. Shipping costs are calculated based on weight, dimensions, and destination. Delivery times are estimates and not guaranteed. We are not responsible for delays caused by shipping carriers or customs processing."
    },
    {
      id: "returns-refunds",
      title: "Returns and Refunds",
      icon: RotateCcw,
      content: "Most items may be returned within 30 days of delivery in original condition. Custom or personalized items are final sale. Return shipping costs may apply unless the item is defective or we made an error. Refunds are processed to the original payment method within 5-7 business days after we receive and inspect the returned item."
    },
    {
      id: "intellectual-property",
      title: "Intellectual Property Rights",
      icon: Shield,
      content: "The service and its original content, features, and functionality are and will remain the exclusive property of AIDEVCOMMERCE and its licensors. The service is protected by copyright, trademark, and other laws. Our trademarks and trade dress may not be used in connection with any product or service without our prior written consent."
    },
    {
      id: "user-content",
      title: "User-Generated Content",
      icon: FileText,
      content: "You may submit reviews, comments, and other content. You retain ownership of your content but grant us a worldwide, royalty-free license to use, modify, and display such content. You represent that your content does not violate any third-party rights and complies with our community guidelines."
    },
    {
      id: "prohibited-uses",
      title: "Prohibited Uses",
      icon: AlertTriangle,
      content: "You may not use our service for any unlawful purpose or to solicit others to perform unlawful acts, violate any international, federal, provincial, or state regulations, rules, laws, or local ordinances, infringe upon or violate our intellectual property rights or the intellectual property rights of others, or harass, abuse, insult, harm, defame, slander, disparage, intimidate, or discriminate."
    },
    {
      id: "ai-services",
      title: "AI-Powered Services",
      icon: Globe,
      content: "Our AI-powered features, including product recommendations and customer support, are provided 'as is' for your convenience. While we strive for accuracy, AI-generated content may not always be perfect. You should use your own judgment when relying on AI recommendations. We continuously improve our AI systems but cannot guarantee their accuracy or completeness."
    },
    {
      id: "privacy-data",
      title: "Privacy and Data Protection",
      icon: Shield,
      content: "Your privacy is important to us. Our Privacy Policy explains how we collect, use, and protect your information when you use our service. By using our service, you agree to the collection and use of information in accordance with our Privacy Policy. We comply with applicable data protection laws including GDPR and CCPA."
    },
    {
      id: "disclaimers",
      title: "Disclaimers",
      icon: AlertTriangle,
      content: "The information on this website is provided on an 'as is' basis. To the fullest extent permitted by law, this Company excludes all representations, warranties, conditions, and terms whether express, implied, statutory, or otherwise. We do not warrant that the service will be uninterrupted, timely, secure, or error-free."
    },
    {
      id: "limitation-liability",
      title: "Limitation of Liability",
      icon: Scale,
      content: "In no event shall AIDEVCOMMERCE, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your use of the service."
    },
    {
      id: "indemnification",
      title: "Indemnification",
      icon: Shield,
      content: "You agree to defend, indemnify, and hold harmless AIDEVCOMMERCE and its licensee and licensors, and their employees, contractors, agents, officers and directors, from and against any and all claims, damages, obligations, losses, liabilities, costs or debt, and expenses (including but not limited to attorney's fees)."
    },
    {
      id: "termination",
      title: "Termination",
      icon: AlertTriangle,
      content: "We may terminate or suspend your account and bar access to the service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever and without limitation, including but not limited to a breach of the Terms. Upon termination, your right to use the service will cease immediately."
    },
    {
      id: "governing-law",
      title: "Governing Law",
      icon: Scale,
      content: "These Terms shall be interpreted and governed by the laws of the State of New York, United States, without regard to its conflict of law provisions. Our failure to enforce any right or provision of these Terms will not be considered a waiver of those rights."
    },
    {
      id: "dispute-resolution",
      title: "Dispute Resolution",
      icon: Scale,
      content: "Any disputes arising out of or relating to these Terms or the service shall be resolved through binding arbitration in accordance with the rules of the American Arbitration Association. The arbitration shall be conducted in New York, NY, and judgment on the arbitration award may be entered into any court having jurisdiction thereof."
    },
    {
      id: "changes-terms",
      title: "Changes to Terms",
      icon: Calendar,
      content: "We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days notice prior to any new terms taking effect. What constitutes a material change will be determined at our sole discretion. By continuing to access or use our service after any revisions become effective, you agree to be bound by the revised terms."
    }
  ]

  const contactInfo = [
    {
      method: "Legal Department",
      value: "<EMAIL>",
      icon: Mail,
      description: "For legal questions and concerns"
    },
    {
      method: "Customer Service",
      value: "+****************",
      icon: Phone,
      description: "Mon-Fri, 9AM-6PM EST"
    },
    {
      method: "Mailing Address",
      value: "123 Innovation Drive, Tech City, TC 12345",
      icon: FileText,
      description: "AIDEVCOMMERCE Legal Department"
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-600 to-primary-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Terms of Service
            </h1>
            <p className="text-xl text-primary-100 mb-8">
              Please read these terms carefully before using our services. By using AIDEVCOMMERCE, you agree to these terms.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Badge className="bg-white/20 text-white px-4 py-2">
                <Scale className="h-4 w-4 mr-2" />
                Legally Binding
              </Badge>
              <Badge className="bg-white/20 text-white px-4 py-2">
                <Calendar className="h-4 w-4 mr-2" />
                Effective {effectiveDate}
              </Badge>
              <Badge className="bg-white/20 text-white px-4 py-2">
                <Globe className="h-4 w-4 mr-2" />
                Global Terms
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Effective Date Notice */}
      <section className="py-8 bg-blue-50 dark:bg-blue-900/20 border-b border-blue-200 dark:border-blue-800">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center space-x-3">
              <Calendar className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              <div>
                <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  Effective Date: {effectiveDate} | Last Updated: {lastUpdated}
                </p>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  These terms govern your use of AIDEVCOMMERCE services and constitute a legally binding agreement.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Terms Content */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Introduction */}
            <Card className="mb-8">
              <CardContent className="p-8">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Welcome to AIDEVCOMMERCE
                </h2>
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed mb-4">
                  These Terms of Service ("Terms") govern your use of the AIDEVCOMMERCE website, mobile applications, and related services (collectively, the "Service") operated by AIDEVCOMMERCE ("us", "we", or "our").
                </p>
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  Please read these Terms carefully before using our Service. Your access to and use of the Service is conditioned on your acceptance of and compliance with these Terms. These Terms apply to all visitors, users, and others who access or use the Service.
                </p>
              </CardContent>
            </Card>

            {/* Terms Sections */}
            <div className="space-y-6">
              {sections.map((section) => {
                const Icon = section.icon
                return (
                  <Card key={section.id}>
                    <CardHeader>
                      <CardTitle className="flex items-center text-lg">
                        <Icon className="h-5 w-5 mr-3 text-primary-600 dark:text-primary-400" />
                        {section.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                        {section.content}
                      </p>
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            {/* Contact Information */}
            <Card className="mt-8">
              <CardHeader>
                <CardTitle className="flex items-center text-xl">
                  <Mail className="h-6 w-6 mr-3 text-primary-600 dark:text-primary-400" />
                  Contact Information
                </CardTitle>
                <CardDescription>
                  If you have any questions about these Terms of Service, please contact us:
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {contactInfo.map((contact) => {
                    const Icon = contact.icon
                    return (
                      <div key={contact.method} className="flex items-start space-x-3">
                        <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center flex-shrink-0">
                          <Icon className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {contact.method}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                            {contact.value}
                          </p>
                          <p className="text-xs text-gray-500">
                            {contact.description}
                          </p>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Acknowledgment */}
            <Card className="mt-8 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800">
              <CardContent className="p-6">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="font-semibold text-green-900 dark:text-green-100 mb-2">
                      Acknowledgment
                    </h3>
                    <p className="text-green-800 dark:text-green-200 text-sm leading-relaxed">
                      By using our Service, you acknowledge that you have read these Terms of Service and agree to be bound by them. If you do not agree to these terms, please do not use our Service. We reserve the right to update these terms at any time, and your continued use of the Service constitutes acceptance of any changes.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Important Legal Notice */}
            <Card className="mt-8 bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800">
              <CardContent className="p-6">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="h-6 w-6 text-amber-600 dark:text-amber-400 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="font-semibold text-amber-900 dark:text-amber-100 mb-2">
                      Important Legal Notice
                    </h3>
                    <div className="text-amber-800 dark:text-amber-200 text-sm leading-relaxed space-y-2">
                      <p>
                        These Terms of Service constitute a legally binding agreement between you and AIDEVCOMMERCE. Please ensure you understand all terms before using our services.
                      </p>
                      <p>
                        If any provision of these Terms is found to be unenforceable, the remaining provisions will continue to be valid and enforceable.
                      </p>
                      <p>
                        For questions about these terms or to report violations, please contact our legal <NAME_EMAIL>.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  )
}
