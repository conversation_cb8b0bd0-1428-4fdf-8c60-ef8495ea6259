"use client"

import Link from "next/link"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import {
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Youtube,
  Mail,
  Phone,
  MapPin,
  Send,
  ArrowRight,
  ShoppingBag,
  Wrench,
  FileText,
  Users,
  Shield,
  CreditCard,
  Truck,
  HeadphonesIcon,
  Award,
  Zap,
} from "lucide-react"

export function Footer() {
  const { t, language } = useLanguage()
  const { toast } = useToast()

  const handleNewsletterSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const email = formData.get('email') as string
    
    if (email) {
      toast({
        title: "Subscribed!",
        description: "Thank you for subscribing to our newsletter."
      })
      // Reset form
      e.currentTarget.reset()
    }
  }

  const quickLinks = [
    { name: "Home", href: "/" },
    { name: "Shop", href: "/shop" },
    { name: "Services", href: "/services" },
    { name: "Production Lines", href: "/production-lines" },
    { name: "Blog", href: "/blog" },
    { name: "About Us", href: "/about" },
    { name: "Contact", href: "/contact" },
  ]

  const customerService = [
    { name: "Help Center", href: "/help" },
    { name: "Track Your Order", href: "/track-order" },
    { name: "Returns & Exchanges", href: "/returns" },
    { name: "Shipping Info", href: "/shipping" },
    { name: "Size Guide", href: "/size-guide" },
    { name: "FAQ", href: "/faq" },
    { name: "Contact Support", href: "/support" },
  ]

  const company = [
    { name: "About AIDEVCOMMERCE", href: "/about" },
    { name: "Careers", href: "/careers" },
    { name: "Press & Media", href: "/press" },
    { name: "Investor Relations", href: "/investors" },
    { name: "Sustainability", href: "/sustainability" },
    { name: "Corporate Responsibility", href: "/corporate-responsibility" },
    { name: "Affiliate Program", href: "/affiliates" },
  ]

  const legal = [
    { name: "Privacy Policy", href: "/privacy" },
    { name: "Terms of Service", href: "/terms" },
    { name: "Cookie Policy", href: "/cookies" },
    { name: "GDPR Compliance", href: "/gdpr" },
    { name: "Accessibility", href: "/accessibility" },
    { name: "Sitemap", href: "/sitemap" },
  ]

  const socialLinks = [
    { name: "Facebook", href: "https://facebook.com/aidevcommerce", icon: Facebook },
    { name: "Twitter", href: "https://twitter.com/aidevcommerce", icon: Twitter },
    { name: "Instagram", href: "https://instagram.com/aidevcommerce", icon: Instagram },
    { name: "LinkedIn", href: "https://linkedin.com/company/aidevcommerce", icon: Linkedin },
    { name: "YouTube", href: "https://youtube.com/@aidevcommerce", icon: Youtube },
  ]

  const features = [
    { name: "Free Shipping", description: "On orders over $75", icon: Truck },
    { name: "Secure Payment", description: "256-bit SSL encryption", icon: Shield },
    { name: "24/7 Support", description: "Expert customer service", icon: HeadphonesIcon },
    { name: "Quality Guarantee", description: "30-day return policy", icon: Award },
  ]

  return (
    <footer className="bg-gray-900 dark:bg-gray-950 text-white">
      {/* Features Bar */}
      <div className="border-b border-gray-800 dark:border-gray-900">
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature) => {
              const Icon = feature.icon
              return (
                <div key={feature.name} className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center">
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-white">{feature.name}</h3>
                    <p className="text-sm text-gray-400">{feature.description}</p>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <Link href="/" className="flex items-center space-x-2 mb-6">
              <div className="text-2xl font-bold text-gradient-primary">
                AIDEVCOMMERCE
              </div>
            </Link>
            <p className="text-gray-400 mb-6 leading-relaxed">
              Your premier destination for cutting-edge technology, professional services, 
              and industrial solutions. Powered by AI to deliver personalized shopping 
              experiences and exceptional customer service.
            </p>
            
            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <MapPin className="h-4 w-4 text-primary-400 flex-shrink-0" />
                <span className="text-sm text-gray-400">
                  123 Innovation Drive, Tech City, TC 12345
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-primary-400 flex-shrink-0" />
                <span className="text-sm text-gray-400">+****************</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-primary-400 flex-shrink-0" />
                <span className="text-sm text-gray-400"><EMAIL></span>
              </div>
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-4 mt-6">
              {socialLinks.map((social) => {
                const Icon = social.icon
                return (
                  <Link
                    key={social.name}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-10 h-10 bg-gray-800 hover:bg-primary-600 rounded-lg flex items-center justify-center transition-colors"
                    aria-label={social.name}
                  >
                    <Icon className="h-4 w-4" />
                  </Link>
                )
              })}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold text-white mb-4">Quick Links</h3>
            <ul className="space-y-3">
              {quickLinks.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Customer Service */}
          <div>
            <h3 className="font-semibold text-white mb-4">Customer Service</h3>
            <ul className="space-y-3">
              {customerService.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="font-semibold text-white mb-4">Company</h3>
            <ul className="space-y-3">
              {company.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Newsletter */}
          <div>
            <h3 className="font-semibold text-white mb-4">Stay Updated</h3>
            <p className="text-gray-400 text-sm mb-4">
              Subscribe to our newsletter for the latest updates, exclusive offers, and tech insights.
            </p>
            <form onSubmit={handleNewsletterSubmit} className="space-y-3">
              <div className="relative">
                <input
                  type="email"
                  name="email"
                  placeholder="Enter your email"
                  required
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
              <button
                type="submit"
                className="w-full bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center justify-center space-x-2"
              >
                <Send className="h-4 w-4" />
                <span>Subscribe</span>
              </button>
            </form>
            
            {/* App Download */}
            <div className="mt-6">
              <p className="text-sm text-gray-400 mb-3">Download Our App</p>
              <div className="space-y-2">
                <Link
                  href="#"
                  className="block w-full bg-gray-800 hover:bg-gray-700 rounded-lg p-2 transition-colors"
                >
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-white rounded flex items-center justify-center">
                      <span className="text-xs font-bold text-black">A</span>
                    </div>
                    <div>
                      <p className="text-xs text-gray-400">Download on the</p>
                      <p className="text-sm font-semibold text-white">App Store</p>
                    </div>
                  </div>
                </Link>
                <Link
                  href="#"
                  className="block w-full bg-gray-800 hover:bg-gray-700 rounded-lg p-2 transition-colors"
                >
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-green-500 rounded flex items-center justify-center">
                      <span className="text-xs font-bold text-white">G</span>
                    </div>
                    <div>
                      <p className="text-xs text-gray-400">Get it on</p>
                      <p className="text-sm font-semibold text-white">Google Play</p>
                    </div>
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800 dark:border-gray-900">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
            <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
              <p className="text-sm text-gray-400">
                © 2024 AIDEVCOMMERCE. All rights reserved.
              </p>
              <div className="flex items-center space-x-4">
                {legal.map((link, index) => (
                  <span key={link.href} className="flex items-center space-x-4">
                    <Link
                      href={link.href}
                      className="text-xs text-gray-400 hover:text-white transition-colors"
                    >
                      {link.name}
                    </Link>
                    {index < legal.length - 1 && (
                      <span className="text-gray-600">•</span>
                    )}
                  </span>
                ))}
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Zap className="h-4 w-4 text-primary-400" />
                <span className="text-sm text-gray-400">Powered by AI</span>
              </div>
              <div className="flex items-center space-x-2">
                <CreditCard className="h-4 w-4 text-gray-400" />
                <span className="text-xs text-gray-400">Secure Payments</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
