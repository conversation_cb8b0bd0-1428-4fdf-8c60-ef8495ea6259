"use client"

import { useEffect, useState } from 'react'

interface PerformanceMetrics {
  // Core Web Vitals
  lcp?: number // Largest Contentful Paint
  fid?: number // First Input Delay
  cls?: number // Cumulative Layout Shift
  
  // Other Performance Metrics
  fcp?: number // First Contentful Paint
  ttfb?: number // Time to First Byte
  domContentLoaded?: number
  loadComplete?: number
  
  // Memory Usage
  usedJSHeapSize?: number
  totalJSHeapSize?: number
  jsHeapSizeLimit?: number
  
  // Navigation Timing
  navigationStart?: number
  domainLookupStart?: number
  domainLookupEnd?: number
  connectStart?: number
  connectEnd?: number
  requestStart?: number
  responseStart?: number
  responseEnd?: number
  domLoading?: number
  domInteractive?: number
  domComplete?: number
}

interface PerformanceMonitorProps {
  enableLogging?: boolean
  enableAnalytics?: boolean
  thresholds?: {
    lcp?: number
    fid?: number
    cls?: number
  }
}

export function PerformanceMonitor({
  enableLogging = process.env.NODE_ENV === 'development',
  enableAnalytics = process.env.NODE_ENV === 'production',
  thresholds = {
    lcp: 2500, // 2.5 seconds
    fid: 100,  // 100 milliseconds
    cls: 0.1   // 0.1 layout shift score
  }
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({})

  useEffect(() => {
    // Check if Performance API is available
    if (typeof window === 'undefined' || !window.performance) {
      return
    }

    const measurePerformance = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      const paint = performance.getEntriesByType('paint')
      
      const newMetrics: PerformanceMetrics = {
        // Navigation Timing
        navigationStart: navigation.navigationStart || performance.timeOrigin,
        domainLookupStart: navigation.domainLookupStart,
        domainLookupEnd: navigation.domainLookupEnd,
        connectStart: navigation.connectStart,
        connectEnd: navigation.connectEnd,
        requestStart: navigation.requestStart,
        responseStart: navigation.responseStart,
        responseEnd: navigation.responseEnd,
        domLoading: navigation.domLoading,
        domInteractive: navigation.domInteractive,
        domComplete: navigation.domComplete,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        
        // Paint Timing
        fcp: paint.find(entry => entry.name === 'first-contentful-paint')?.startTime,
        
        // Time to First Byte
        ttfb: navigation.responseStart - navigation.requestStart,
      }

      // Memory Usage (if available)
      if ('memory' in performance) {
        const memory = (performance as any).memory
        newMetrics.usedJSHeapSize = memory.usedJSHeapSize
        newMetrics.totalJSHeapSize = memory.totalJSHeapSize
        newMetrics.jsHeapSizeLimit = memory.jsHeapSizeLimit
      }

      setMetrics(newMetrics)

      if (enableLogging) {
        console.group('🚀 Performance Metrics')
        console.log('First Contentful Paint:', newMetrics.fcp?.toFixed(2), 'ms')
        console.log('Time to First Byte:', newMetrics.ttfb?.toFixed(2), 'ms')
        console.log('DOM Content Loaded:', newMetrics.domContentLoaded?.toFixed(2), 'ms')
        console.log('Load Complete:', newMetrics.loadComplete?.toFixed(2), 'ms')
        if (newMetrics.usedJSHeapSize) {
          console.log('JS Heap Used:', (newMetrics.usedJSHeapSize / 1024 / 1024).toFixed(2), 'MB')
        }
        console.groupEnd()
      }
    }

    // Measure performance after page load
    if (document.readyState === 'complete') {
      measurePerformance()
    } else {
      window.addEventListener('load', measurePerformance)
    }

    // Core Web Vitals measurement
    const measureWebVitals = () => {
      // Largest Contentful Paint
      if ('PerformanceObserver' in window) {
        try {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            const lastEntry = entries[entries.length - 1] as any
            const lcp = lastEntry.startTime
            
            setMetrics(prev => ({ ...prev, lcp }))
            
            if (enableLogging) {
              console.log('🎯 Largest Contentful Paint:', lcp.toFixed(2), 'ms')
              if (thresholds.lcp && lcp > thresholds.lcp) {
                console.warn('⚠️ LCP threshold exceeded:', lcp, '>', thresholds.lcp)
              }
            }
            
            if (enableAnalytics) {
              // Send to analytics service
              sendToAnalytics('lcp', lcp)
            }
          })
          
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

          // First Input Delay
          const fidObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            entries.forEach((entry: any) => {
              const fid = entry.processingStart - entry.startTime
              
              setMetrics(prev => ({ ...prev, fid }))
              
              if (enableLogging) {
                console.log('⚡ First Input Delay:', fid.toFixed(2), 'ms')
                if (thresholds.fid && fid > thresholds.fid) {
                  console.warn('⚠️ FID threshold exceeded:', fid, '>', thresholds.fid)
                }
              }
              
              if (enableAnalytics) {
                sendToAnalytics('fid', fid)
              }
            })
          })
          
          fidObserver.observe({ entryTypes: ['first-input'] })

          // Cumulative Layout Shift
          let clsValue = 0
          const clsObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            entries.forEach((entry: any) => {
              if (!entry.hadRecentInput) {
                clsValue += entry.value
              }
            })
            
            setMetrics(prev => ({ ...prev, cls: clsValue }))
            
            if (enableLogging && clsValue > 0) {
              console.log('📐 Cumulative Layout Shift:', clsValue.toFixed(4))
              if (thresholds.cls && clsValue > thresholds.cls) {
                console.warn('⚠️ CLS threshold exceeded:', clsValue, '>', thresholds.cls)
              }
            }
            
            if (enableAnalytics) {
              sendToAnalytics('cls', clsValue)
            }
          })
          
          clsObserver.observe({ entryTypes: ['layout-shift'] })

        } catch (error) {
          console.warn('Performance Observer not supported:', error)
        }
      }
    }

    measureWebVitals()

    // Cleanup
    return () => {
      window.removeEventListener('load', measurePerformance)
    }
  }, [enableLogging, enableAnalytics, thresholds])

  // Send metrics to analytics service
  const sendToAnalytics = (metric: string, value: number) => {
    // In a real app, you would send this to your analytics service
    // Examples: Google Analytics, Mixpanel, Custom Analytics API
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'web_vitals', {
        event_category: 'Performance',
        event_label: metric,
        value: Math.round(value),
        non_interaction: true,
      })
    }
  }

  // Performance grade calculation
  const getPerformanceGrade = (): 'A' | 'B' | 'C' | 'D' | 'F' => {
    const { lcp, fid, cls } = metrics
    
    if (!lcp || !fid || cls === undefined) return 'F'
    
    let score = 0
    
    // LCP scoring (40% weight)
    if (lcp <= 2500) score += 40
    else if (lcp <= 4000) score += 20
    
    // FID scoring (30% weight)
    if (fid <= 100) score += 30
    else if (fid <= 300) score += 15
    
    // CLS scoring (30% weight)
    if (cls <= 0.1) score += 30
    else if (cls <= 0.25) score += 15
    
    if (score >= 90) return 'A'
    if (score >= 80) return 'B'
    if (score >= 70) return 'C'
    if (score >= 60) return 'D'
    return 'F'
  }

  // Development mode performance display
  if (enableLogging && process.env.NODE_ENV === 'development') {
    return (
      <div className="fixed bottom-4 right-4 bg-black bg-opacity-80 text-white p-4 rounded-lg text-xs font-mono z-50 max-w-xs">
        <div className="font-bold mb-2">Performance Monitor</div>
        <div className="space-y-1">
          <div>Grade: <span className="font-bold text-green-400">{getPerformanceGrade()}</span></div>
          {metrics.lcp && <div>LCP: {metrics.lcp.toFixed(0)}ms</div>}
          {metrics.fid && <div>FID: {metrics.fid.toFixed(0)}ms</div>}
          {metrics.cls !== undefined && <div>CLS: {metrics.cls.toFixed(3)}</div>}
          {metrics.fcp && <div>FCP: {metrics.fcp.toFixed(0)}ms</div>}
          {metrics.ttfb && <div>TTFB: {metrics.ttfb.toFixed(0)}ms</div>}
          {metrics.usedJSHeapSize && (
            <div>Memory: {(metrics.usedJSHeapSize / 1024 / 1024).toFixed(1)}MB</div>
          )}
        </div>
      </div>
    )
  }

  return null
}

// Hook for accessing performance metrics
export function usePerformanceMetrics() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({})

  useEffect(() => {
    if (typeof window === 'undefined' || !window.performance) {
      return
    }

    const updateMetrics = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      const paint = performance.getEntriesByType('paint')
      
      setMetrics({
        fcp: paint.find(entry => entry.name === 'first-contentful-paint')?.startTime,
        ttfb: navigation.responseStart - navigation.requestStart,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
      })
    }

    if (document.readyState === 'complete') {
      updateMetrics()
    } else {
      window.addEventListener('load', updateMetrics)
    }

    return () => {
      window.removeEventListener('load', updateMetrics)
    }
  }, [])

  return metrics
}
