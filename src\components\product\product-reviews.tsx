"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { formatDate } from "@/lib/utils"
import {
  Star,
  ThumbsUp,
  ThumbsDown,
  MessageCircle,
  Flag,
  Filter,
  ChevronDown,
  Verified,
  Award,
  TrendingUp,
  Users,
  BarChart3,
} from "lucide-react"

interface Review {
  id: string
  userId: string
  userName: string
  userAvatar?: string
  rating: number
  title: string
  content: string
  date: Date
  verified: boolean
  helpful: number
  notHelpful: number
  images?: string[]
  variant?: string
  pros?: string[]
  cons?: string[]
}

interface ReviewStats {
  averageRating: number
  totalReviews: number
  ratingDistribution: {
    5: number
    4: number
    3: number
    2: number
    1: number
  }
  recommendationRate: number
}

// Mock reviews data
const mockReviews: Review[] = [
  {
    id: "1",
    userId: "user1",
    userName: "<PERSON>",
    userAvatar: "/avatar-1.jpg",
    rating: 5,
    title: "Excellent quality and fast delivery!",
    content: "I'm absolutely thrilled with this purchase! The quality exceeded my expectations and the delivery was incredibly fast. The product works exactly as described and the customer service was outstanding. I would definitely recommend this to anyone looking for a reliable and high-quality product.",
    date: new Date("2024-01-15"),
    verified: true,
    helpful: 24,
    notHelpful: 2,
    variant: "Black",
    pros: ["Great quality", "Fast delivery", "Excellent customer service"],
    cons: []
  },
  {
    id: "2",
    userId: "user2",
    userName: "Mike Chen",
    userAvatar: "/avatar-2.jpg",
    rating: 4,
    title: "Good product with minor issues",
    content: "Overall, I'm satisfied with this product. It does what it's supposed to do and the build quality is solid. However, I did encounter a few minor issues during setup that took some time to resolve. The customer support was helpful in addressing these concerns.",
    date: new Date("2024-01-10"),
    verified: true,
    helpful: 18,
    notHelpful: 3,
    variant: "Silver",
    pros: ["Solid build quality", "Good performance", "Helpful support"],
    cons: ["Setup issues", "Could be more intuitive"]
  },
  {
    id: "3",
    userId: "user3",
    userName: "Emily Rodriguez",
    userAvatar: "/avatar-3.jpg",
    rating: 5,
    title: "Perfect for my needs!",
    content: "This product is exactly what I was looking for. It fits perfectly into my workflow and has significantly improved my productivity. The design is sleek and modern, and it integrates well with my other devices. Highly recommended!",
    date: new Date("2024-01-08"),
    verified: false,
    helpful: 15,
    notHelpful: 1,
    variant: "White",
    pros: ["Perfect fit", "Sleek design", "Great integration"],
    cons: []
  },
  {
    id: "4",
    userId: "user4",
    userName: "David Thompson",
    userAvatar: "/avatar-4.jpg",
    rating: 3,
    title: "Average product, room for improvement",
    content: "The product is okay but not exceptional. It does the job but there are definitely areas where it could be improved. The price point is reasonable for what you get, but I expected a bit more based on the reviews I read.",
    date: new Date("2024-01-05"),
    verified: true,
    helpful: 8,
    notHelpful: 5,
    variant: "Black",
    pros: ["Reasonable price", "Does the job"],
    cons: ["Could be better", "Not exceptional"]
  },
  {
    id: "5",
    userId: "user5",
    userName: "Lisa Wang",
    userAvatar: "/avatar-5.jpg",
    rating: 5,
    title: "Outstanding value for money!",
    content: "I've been using this product for several weeks now and I'm impressed with its performance. The value for money is outstanding - you get premium features at a very reasonable price. The build quality is excellent and it feels very durable.",
    date: new Date("2024-01-03"),
    verified: true,
    helpful: 32,
    notHelpful: 1,
    variant: "Silver",
    pros: ["Great value", "Premium features", "Durable build"],
    cons: []
  }
]

const mockStats: ReviewStats = {
  averageRating: 4.4,
  totalReviews: 127,
  ratingDistribution: {
    5: 68,
    4: 32,
    3: 15,
    2: 8,
    1: 4
  },
  recommendationRate: 89
}

interface ProductReviewsProps {
  productId: string
}

export default function ProductReviews({ productId }: ProductReviewsProps) {
  const [reviews, setReviews] = useState(mockReviews)
  const [stats, setStats] = useState(mockStats)
  const [sortBy, setSortBy] = useState("newest")
  const [filterRating, setFilterRating] = useState("all")
  const [showWriteReview, setShowWriteReview] = useState(false)
  const [newReview, setNewReview] = useState({
    rating: 5,
    title: "",
    content: "",
    pros: "",
    cons: ""
  })

  const { t } = useLanguage()
  const { toast } = useToast()

  const handleHelpful = (reviewId: string, isHelpful: boolean) => {
    setReviews(reviews.map(review => {
      if (review.id === reviewId) {
        return {
          ...review,
          helpful: isHelpful ? review.helpful + 1 : review.helpful,
          notHelpful: !isHelpful ? review.notHelpful + 1 : review.notHelpful
        }
      }
      return review
    }))

    toast({
      title: "Thank you for your feedback!",
      description: "Your feedback helps other customers make informed decisions."
    })
  }

  const handleSubmitReview = () => {
    if (!newReview.title || !newReview.content) {
      toast({
        title: "Please fill in all required fields",
        description: "Title and review content are required.",
        variant: "destructive"
      })
      return
    }

    const review: Review = {
      id: Date.now().toString(),
      userId: "current-user",
      userName: "You",
      rating: newReview.rating,
      title: newReview.title,
      content: newReview.content,
      date: new Date(),
      verified: false,
      helpful: 0,
      notHelpful: 0,
      pros: newReview.pros ? newReview.pros.split(",").map(p => p.trim()) : [],
      cons: newReview.cons ? newReview.cons.split(",").map(c => c.trim()) : []
    }

    setReviews([review, ...reviews])
    setNewReview({ rating: 5, title: "", content: "", pros: "", cons: "" })
    setShowWriteReview(false)

    toast({
      title: "Review submitted!",
      description: "Thank you for sharing your experience."
    })
  }

  const StarRating = ({ rating, size = "sm" }: { rating: number; size?: "sm" | "md" | "lg" }) => {
    const sizeClasses = {
      sm: "h-4 w-4",
      md: "h-5 w-5",
      lg: "h-6 w-6"
    }

    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClasses[size]} ${
              star <= rating
                ? "text-yellow-400 fill-current"
                : "text-gray-300 dark:text-gray-600"
            }`}
          />
        ))}
      </div>
    )
  }

  const RatingBar = ({ rating, count, total }: { rating: number; count: number; total: number }) => {
    const percentage = (count / total) * 100

    return (
      <div className="flex items-center space-x-2 text-sm">
        <span className="w-8">{rating}</span>
        <Star className="h-4 w-4 text-yellow-400 fill-current" />
        <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className="bg-yellow-400 h-2 rounded-full"
            style={{ width: `${percentage}%` }}
          />
        </div>
        <span className="w-8 text-gray-600 dark:text-gray-400">{count}</span>
      </div>
    )
  }

  const filteredReviews = reviews.filter(review => {
    if (filterRating === "all") return true
    return review.rating === parseInt(filterRating)
  })

  const sortedReviews = [...filteredReviews].sort((a, b) => {
    switch (sortBy) {
      case "newest":
        return new Date(b.date).getTime() - new Date(a.date).getTime()
      case "oldest":
        return new Date(a.date).getTime() - new Date(b.date).getTime()
      case "highest":
        return b.rating - a.rating
      case "lowest":
        return a.rating - b.rating
      case "helpful":
        return b.helpful - a.helpful
      default:
        return 0
    }
  })

  return (
    <div className="space-y-8">
      {/* Review Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Customer Reviews
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Overall Rating */}
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-4xl font-bold text-gray-900 dark:text-white">
                  {stats.averageRating}
                </div>
                <StarRating rating={Math.round(stats.averageRating)} size="lg" />
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                  Based on {stats.totalReviews} reviews
                </p>
              </div>

              <div className="flex items-center justify-center space-x-4 text-sm">
                <div className="flex items-center space-x-1">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <span>{stats.recommendationRate}% recommend</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Users className="h-4 w-4 text-blue-600" />
                  <span>{stats.totalReviews} reviews</span>
                </div>
              </div>
            </div>

            {/* Rating Distribution */}
            <div className="space-y-2">
              {[5, 4, 3, 2, 1].map((rating) => (
                <RatingBar
                  key={rating}
                  rating={rating}
                  count={stats.ratingDistribution[rating as keyof typeof stats.ratingDistribution]}
                  total={stats.totalReviews}
                />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Write Review Button */}
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-semibold">Reviews ({filteredReviews.length})</h3>
        <Button onClick={() => setShowWriteReview(!showWriteReview)}>
          <MessageCircle className="h-4 w-4 mr-2" />
          Write a Review
        </Button>
      </div>

      {/* Write Review Form */}
      {showWriteReview && (
        <Card>
          <CardHeader>
            <CardTitle>Write Your Review</CardTitle>
            <CardDescription>Share your experience with this product</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Rating */}
            <div>
              <label className="block text-sm font-medium mb-2">Rating</label>
              <div className="flex items-center space-x-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    onClick={() => setNewReview({ ...newReview, rating: star })}
                    className="focus:outline-none"
                  >
                    <Star
                      className={`h-6 w-6 ${
                        star <= newReview.rating
                          ? "text-yellow-400 fill-current"
                          : "text-gray-300 dark:text-gray-600"
                      }`}
                    />
                  </button>
                ))}
              </div>
            </div>

            {/* Title */}
            <div>
              <label className="block text-sm font-medium mb-2">Review Title</label>
              <input
                type="text"
                value={newReview.title}
                onChange={(e) => setNewReview({ ...newReview, title: e.target.value })}
                placeholder="Summarize your experience"
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500"
              />
            </div>

            {/* Content */}
            <div>
              <label className="block text-sm font-medium mb-2">Your Review</label>
              <textarea
                value={newReview.content}
                onChange={(e) => setNewReview({ ...newReview, content: e.target.value })}
                placeholder="Tell others about your experience with this product"
                rows={4}
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500"
              />
            </div>

            {/* Pros and Cons */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Pros (comma separated)</label>
                <input
                  type="text"
                  value={newReview.pros}
                  onChange={(e) => setNewReview({ ...newReview, pros: e.target.value })}
                  placeholder="Great quality, Fast delivery"
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Cons (comma separated)</label>
                <input
                  type="text"
                  value={newReview.cons}
                  onChange={(e) => setNewReview({ ...newReview, cons: e.target.value })}
                  placeholder="Expensive, Complex setup"
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>

            <div className="flex space-x-2">
              <Button onClick={handleSubmitReview}>Submit Review</Button>
              <Button variant="outline" onClick={() => setShowWriteReview(false)}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters and Sorting */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4" />
            <select
              value={filterRating}
              onChange={(e) => setFilterRating(e.target.value)}
              className="px-3 py-1 border rounded-lg text-sm focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">All Ratings</option>
              <option value="5">5 Stars</option>
              <option value="4">4 Stars</option>
              <option value="3">3 Stars</option>
              <option value="2">2 Stars</option>
              <option value="1">1 Star</option>
            </select>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600 dark:text-gray-400">Sort by:</span>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-3 py-1 border rounded-lg text-sm focus:ring-2 focus:ring-primary-500"
          >
            <option value="newest">Newest</option>
            <option value="oldest">Oldest</option>
            <option value="highest">Highest Rating</option>
            <option value="lowest">Lowest Rating</option>
            <option value="helpful">Most Helpful</option>
          </select>
        </div>
      </div>

      {/* Reviews List */}
      <div className="space-y-6">
        {sortedReviews.map((review) => (
          <Card key={review.id}>
            <CardContent className="p-6">
              <div className="flex items-start space-x-4">
                <Avatar>
                  <AvatarImage src={review.userAvatar} />
                  <AvatarFallback>
                    {review.userName.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1 space-y-3">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div>
                      <div className="flex items-center space-x-2">
                        <h4 className="font-semibold">{review.userName}</h4>
                        {review.verified && (
                          <Badge variant="secondary" className="text-xs">
                            <Verified className="h-3 w-3 mr-1" />
                            Verified Purchase
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center space-x-2 mt-1">
                        <StarRating rating={review.rating} />
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {formatDate(review.date)}
                        </span>
                        {review.variant && (
                          <Badge variant="outline" className="text-xs">
                            {review.variant}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Flag className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Review Content */}
                  <div>
                    <h5 className="font-medium mb-2">{review.title}</h5>
                    <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                      {review.content}
                    </p>
                  </div>

                  {/* Pros and Cons */}
                  {(review.pros && review.pros.length > 0) || (review.cons && review.cons.length > 0) ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {review.pros && review.pros.length > 0 && (
                        <div>
                          <h6 className="font-medium text-green-700 dark:text-green-400 mb-2">
                            Pros:
                          </h6>
                          <ul className="space-y-1">
                            {review.pros.map((pro, index) => (
                              <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                                <ThumbsUp className="h-3 w-3 text-green-600 mr-2 flex-shrink-0" />
                                {pro}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                      {review.cons && review.cons.length > 0 && (
                        <div>
                          <h6 className="font-medium text-red-700 dark:text-red-400 mb-2">
                            Cons:
                          </h6>
                          <ul className="space-y-1">
                            {review.cons.map((con, index) => (
                              <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                                <ThumbsDown className="h-3 w-3 text-red-600 mr-2 flex-shrink-0" />
                                {con}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ) : null}

                  {/* Helpful Actions */}
                  <div className="flex items-center space-x-4 pt-4 border-t">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Was this review helpful?
                    </span>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleHelpful(review.id, true)}
                        className="text-sm"
                      >
                        <ThumbsUp className="h-4 w-4 mr-1" />
                        Yes ({review.helpful})
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleHelpful(review.id, false)}
                        className="text-sm"
                      >
                        <ThumbsDown className="h-4 w-4 mr-1" />
                        No ({review.notHelpful})
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Load More */}
      {sortedReviews.length < stats.totalReviews && (
        <div className="text-center">
          <Button variant="outline">
            Load More Reviews
            <ChevronDown className="h-4 w-4 ml-2" />
          </Button>
        </div>
      )}
    </div>
  )
}
