"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Elements, PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js'
import { getStripe } from '@/lib/stripe'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/hooks/use-toast'
import { useCartStore } from '@/store/cart-store'
import { formatPrice } from '@/lib/utils'
import {
  CreditCard,
  Lock,
  Shield,
  AlertCircle,
  CheckCircle,
  Loader2,
} from 'lucide-react'

interface StripePaymentFormProps {
  clientSecret: string
  orderId: string
  amount: number
  onSuccess?: () => void
  onError?: (error: string) => void
}

function PaymentForm({ clientSecret, orderId, amount, onSuccess, onError }: StripePaymentFormProps) {
  const stripe = useStripe()
  const elements = useElements()
  const router = useRouter()
  const { toast } = useToast()
  const { clearCart } = useCartStore()
  
  const [isLoading, setIsLoading] = useState(false)
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'processing' | 'succeeded' | 'failed'>('idle')
  const [errorMessage, setErrorMessage] = useState<string | null>(null)

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()

    if (!stripe || !elements) {
      return
    }

    setIsLoading(true)
    setPaymentStatus('processing')
    setErrorMessage(null)

    try {
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/order-success?order_id=${orderId}`,
        },
        redirect: 'if_required',
      })

      if (error) {
        setErrorMessage(error.message || 'An error occurred during payment')
        setPaymentStatus('failed')
        onError?.(error.message || 'Payment failed')
        
        toast({
          title: "Payment Failed",
          description: error.message || 'An error occurred during payment',
          variant: "destructive",
        })
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        setPaymentStatus('succeeded')
        
        toast({
          title: "Payment Successful!",
          description: "Your order has been confirmed",
        })

        // Clear cart
        clearCart()
        
        // Call success callback
        onSuccess?.()
        
        // Redirect to success page
        router.push(`/order-success?order_id=${orderId}&payment_intent=${paymentIntent.id}`)
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'An unexpected error occurred'
      setErrorMessage(errorMsg)
      setPaymentStatus('failed')
      onError?.(errorMsg)
      
      toast({
        title: "Payment Error",
        description: errorMsg,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Payment Element */}
      <div className="space-y-4">
        <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
          <Shield className="h-4 w-4 text-green-500" />
          <span>Your payment information is secure and encrypted</span>
        </div>
        
        <PaymentElement 
          options={{
            layout: 'tabs',
            paymentMethodOrder: ['card', 'apple_pay', 'google_pay'],
          }}
        />
      </div>

      {/* Error Message */}
      {errorMessage && (
        <div className="flex items-center space-x-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <AlertCircle className="h-4 w-4 text-red-500" />
          <span className="text-sm text-red-700 dark:text-red-400">{errorMessage}</span>
        </div>
      )}

      {/* Success Message */}
      {paymentStatus === 'succeeded' && (
        <div className="flex items-center space-x-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <span className="text-sm text-green-700 dark:text-green-400">Payment successful! Redirecting...</span>
        </div>
      )}

      {/* Submit Button */}
      <Button
        type="submit"
        disabled={!stripe || isLoading || paymentStatus === 'succeeded'}
        className="w-full"
        size="lg"
      >
        {isLoading ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Processing Payment...
          </>
        ) : paymentStatus === 'succeeded' ? (
          <>
            <CheckCircle className="h-4 w-4 mr-2" />
            Payment Successful
          </>
        ) : (
          <>
            <Lock className="h-4 w-4 mr-2" />
            Pay {formatPrice(amount)}
          </>
        )}
      </Button>

      {/* Security Notice */}
      <div className="text-center">
        <div className="flex items-center justify-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
          <Shield className="h-3 w-3" />
          <span>Secured by Stripe</span>
        </div>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Your payment information is processed securely. We do not store credit card details.
        </p>
      </div>
    </form>
  )
}

export function StripePaymentForm(props: StripePaymentFormProps) {
  const [stripePromise] = useState(() => getStripe())

  if (!props.clientSecret) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Loading payment form...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <CreditCard className="h-5 w-5 mr-2" />
          Payment Information
        </CardTitle>
        <CardDescription>
          Complete your purchase securely with Stripe
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Elements
          stripe={stripePromise}
          options={{
            clientSecret: props.clientSecret,
            appearance: {
              theme: 'stripe',
              variables: {
                colorPrimary: '#3b82f6',
                colorBackground: '#ffffff',
                colorText: '#1f2937',
                colorDanger: '#ef4444',
                fontFamily: 'system-ui, sans-serif',
                spacingUnit: '4px',
                borderRadius: '6px',
              },
            },
          }}
        >
          <PaymentForm {...props} />
        </Elements>
      </CardContent>
    </Card>
  )
}

// Hook for creating payment intent
export function useCreatePaymentIntent() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createPaymentIntent = async (data: {
    amount: number
    currency?: string
    items: any[]
    shippingAddress?: any
    billingAddress?: any
  }) => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/payments/create-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create payment intent')
      }

      const result = await response.json()
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      throw err
    } finally {
      setIsLoading(false)
    }
  }

  return {
    createPaymentIntent,
    isLoading,
    error,
  }
}
