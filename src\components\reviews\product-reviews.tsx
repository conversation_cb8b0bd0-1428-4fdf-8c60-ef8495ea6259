"use client"

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { formatDate } from '@/lib/utils'
import {
  Star,
  ThumbsUp,
  ThumbsDown,
  MessageCircle,
  User,
  Verified,
  Flag,
  Filter,
  SortAsc,
  SortDesc,
} from 'lucide-react'

interface Review {
  id: string
  userId: string
  userName: string
  userAvatar?: string
  rating: number
  title: string
  content: string
  createdAt: Date
  updatedAt: Date
  verified: boolean
  helpful: number
  notHelpful: number
  userVote?: 'helpful' | 'not_helpful'
  images?: string[]
}

interface ProductReviewsProps {
  productId: string
  productName: string
  averageRating?: number
  totalReviews?: number
}

export function ProductReviews({ 
  productId, 
  productName, 
  averageRating = 0, 
  totalReviews = 0 
}: ProductReviewsProps) {
  const { data: session } = useSession()
  const { toast } = useToast()
  
  const [reviews, setReviews] = useState<Review[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showReviewForm, setShowReviewForm] = useState(false)
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'highest' | 'lowest' | 'helpful'>('newest')
  const [filterRating, setFilterRating] = useState<number | null>(null)
  
  // Review form state
  const [newReview, setNewReview] = useState({
    rating: 5,
    title: '',
    content: '',
  })

  useEffect(() => {
    fetchReviews()
  }, [productId, sortBy, filterRating])

  const fetchReviews = async () => {
    setIsLoading(true)
    try {
      // Mock data - in real app, this would fetch from API
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const mockReviews: Review[] = [
        {
          id: '1',
          userId: 'user1',
          userName: 'John Smith',
          userAvatar: '/avatar1.jpg',
          rating: 5,
          title: 'Excellent product!',
          content: 'This product exceeded my expectations. The quality is outstanding and it arrived quickly. Highly recommend!',
          createdAt: new Date('2024-01-15'),
          updatedAt: new Date('2024-01-15'),
          verified: true,
          helpful: 12,
          notHelpful: 1,
          images: ['/review1.jpg', '/review2.jpg'],
        },
        {
          id: '2',
          userId: 'user2',
          userName: 'Sarah Johnson',
          rating: 4,
          title: 'Good value for money',
          content: 'Great product overall. Minor issues with packaging but the item itself is solid. Would buy again.',
          createdAt: new Date('2024-01-10'),
          updatedAt: new Date('2024-01-10'),
          verified: true,
          helpful: 8,
          notHelpful: 2,
        },
        {
          id: '3',
          userId: 'user3',
          userName: 'Mike Wilson',
          rating: 3,
          title: 'Average product',
          content: 'It\'s okay, nothing special. Does what it\'s supposed to do but could be better for the price.',
          createdAt: new Date('2024-01-05'),
          updatedAt: new Date('2024-01-05'),
          verified: false,
          helpful: 3,
          notHelpful: 5,
        },
      ]
      
      setReviews(mockReviews)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load reviews",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmitReview = async () => {
    if (!session) {
      toast({
        title: "Login Required",
        description: "Please log in to submit a review",
        variant: "destructive",
      })
      return
    }

    if (!newReview.title.trim() || !newReview.content.trim()) {
      toast({
        title: "Missing Information",
        description: "Please provide both a title and review content",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)
    try {
      // Mock API call - in real app, this would submit to API
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const review: Review = {
        id: Date.now().toString(),
        userId: session.user.id,
        userName: session.user.name || 'Anonymous',
        userAvatar: session.user.image,
        rating: newReview.rating,
        title: newReview.title,
        content: newReview.content,
        createdAt: new Date(),
        updatedAt: new Date(),
        verified: false, // Would be set based on purchase history
        helpful: 0,
        notHelpful: 0,
      }
      
      setReviews(prev => [review, ...prev])
      setNewReview({ rating: 5, title: '', content: '' })
      setShowReviewForm(false)
      
      toast({
        title: "Review Submitted",
        description: "Thank you for your review!",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit review",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleVoteHelpful = async (reviewId: string, isHelpful: boolean) => {
    if (!session) {
      toast({
        title: "Login Required",
        description: "Please log in to vote on reviews",
        variant: "destructive",
      })
      return
    }

    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 300))
      
      setReviews(prev => prev.map(review => {
        if (review.id === reviewId) {
          const currentVote = review.userVote
          let helpful = review.helpful
          let notHelpful = review.notHelpful
          let userVote: 'helpful' | 'not_helpful' | undefined
          
          // Remove previous vote
          if (currentVote === 'helpful') helpful--
          if (currentVote === 'not_helpful') notHelpful--
          
          // Add new vote if different from current
          if (currentVote !== (isHelpful ? 'helpful' : 'not_helpful')) {
            if (isHelpful) {
              helpful++
              userVote = 'helpful'
            } else {
              notHelpful++
              userVote = 'not_helpful'
            }
          }
          
          return { ...review, helpful, notHelpful, userVote }
        }
        return review
      }))
      
      toast({
        title: "Vote Recorded",
        description: "Thank you for your feedback!",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to record vote",
        variant: "destructive",
      })
    }
  }

  const renderStars = (rating: number, size: 'sm' | 'md' | 'lg' = 'md') => {
    const sizeClasses = {
      sm: 'h-3 w-3',
      md: 'h-4 w-4',
      lg: 'h-5 w-5',
    }
    
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClasses[size]} ${
              star <= rating
                ? 'text-yellow-400 fill-current'
                : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    )
  }

  const filteredAndSortedReviews = reviews
    .filter(review => filterRating ? review.rating === filterRating : true)
    .sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        case 'highest':
          return b.rating - a.rating
        case 'lowest':
          return a.rating - b.rating
        case 'helpful':
          return b.helpful - a.helpful
        default:
          return 0
      }
    })

  return (
    <div className="space-y-6">
      {/* Reviews Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold">Customer Reviews</h3>
          <div className="flex items-center space-x-4 mt-2">
            <div className="flex items-center space-x-2">
              {renderStars(averageRating, 'lg')}
              <span className="text-lg font-semibold">{averageRating.toFixed(1)}</span>
            </div>
            <span className="text-gray-600 dark:text-gray-400">
              Based on {totalReviews} reviews
            </span>
          </div>
        </div>
        
        {session && (
          <Button onClick={() => setShowReviewForm(!showReviewForm)}>
            <MessageCircle className="h-4 w-4 mr-2" />
            Write Review
          </Button>
        )}
      </div>

      {/* Review Form */}
      {showReviewForm && (
        <Card>
          <CardHeader>
            <CardTitle>Write a Review</CardTitle>
            <CardDescription>Share your experience with {productName}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Rating */}
            <div>
              <label className="block text-sm font-medium mb-2">Rating</label>
              <div className="flex items-center space-x-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    type="button"
                    onClick={() => setNewReview(prev => ({ ...prev, rating: star }))}
                    className="p-1"
                  >
                    <Star
                      className={`h-6 w-6 ${
                        star <= newReview.rating
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300 hover:text-yellow-200'
                      }`}
                    />
                  </button>
                ))}
              </div>
            </div>

            {/* Title */}
            <div>
              <label className="block text-sm font-medium mb-2">Review Title</label>
              <input
                type="text"
                value={newReview.title}
                onChange={(e) => setNewReview(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Summarize your review"
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500"
                maxLength={100}
              />
            </div>

            {/* Content */}
            <div>
              <label className="block text-sm font-medium mb-2">Review</label>
              <Textarea
                value={newReview.content}
                onChange={(e) => setNewReview(prev => ({ ...prev, content: e.target.value }))}
                placeholder="Tell others about your experience with this product"
                rows={4}
                maxLength={1000}
              />
              <div className="text-xs text-gray-500 mt-1">
                {newReview.content.length}/1000 characters
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex items-center space-x-2">
              <Button onClick={handleSubmitReview} disabled={isSubmitting}>
                {isSubmitting ? 'Submitting...' : 'Submit Review'}
              </Button>
              <Button variant="outline" onClick={() => setShowReviewForm(false)}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters and Sorting */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-2 border rounded-lg text-sm"
          >
            <option value="newest">Newest First</option>
            <option value="oldest">Oldest First</option>
            <option value="highest">Highest Rating</option>
            <option value="lowest">Lowest Rating</option>
            <option value="helpful">Most Helpful</option>
          </select>
          
          <select
            value={filterRating || ''}
            onChange={(e) => setFilterRating(e.target.value ? parseInt(e.target.value) : null)}
            className="px-3 py-2 border rounded-lg text-sm"
          >
            <option value="">All Ratings</option>
            <option value="5">5 Stars</option>
            <option value="4">4 Stars</option>
            <option value="3">3 Stars</option>
            <option value="2">2 Stars</option>
            <option value="1">1 Star</option>
          </select>
        </div>
        
        <span className="text-sm text-gray-600">
          Showing {filteredAndSortedReviews.length} of {reviews.length} reviews
        </span>
      </div>

      {/* Reviews List */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="animate-pulse space-y-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                      <div className="space-y-1">
                        <div className="h-4 bg-gray-200 rounded w-24"></div>
                        <div className="h-3 bg-gray-200 rounded w-16"></div>
                      </div>
                    </div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-16 bg-gray-200 rounded"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredAndSortedReviews.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No reviews yet
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Be the first to review this product
              </p>
              {session && (
                <Button onClick={() => setShowReviewForm(true)}>
                  Write First Review
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          filteredAndSortedReviews.map((review) => (
            <Card key={review.id}>
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Review Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                        {review.userAvatar ? (
                          <img
                            src={review.userAvatar}
                            alt={review.userName}
                            className="w-10 h-10 rounded-full"
                          />
                        ) : (
                          <User className="h-5 w-5 text-gray-500" />
                        )}
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{review.userName}</span>
                          {review.verified && (
                            <Badge variant="secondary" className="text-xs">
                              <Verified className="h-3 w-3 mr-1" />
                              Verified Purchase
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center space-x-2 mt-1">
                          {renderStars(review.rating, 'sm')}
                          <span className="text-sm text-gray-600">
                            {formatDate(review.createdAt)}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <Button variant="ghost" size="sm">
                      <Flag className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Review Content */}
                  <div>
                    <h4 className="font-medium mb-2">{review.title}</h4>
                    <p className="text-gray-700 dark:text-gray-300">{review.content}</p>
                  </div>

                  {/* Review Images */}
                  {review.images && review.images.length > 0 && (
                    <div className="flex space-x-2">
                      {review.images.map((image, index) => (
                        <div
                          key={index}
                          className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center"
                        >
                          📷
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Review Actions */}
                  <div className="flex items-center justify-between pt-2 border-t">
                    <div className="flex items-center space-x-4">
                      <button
                        onClick={() => handleVoteHelpful(review.id, true)}
                        className={`flex items-center space-x-1 text-sm ${
                          review.userVote === 'helpful'
                            ? 'text-green-600'
                            : 'text-gray-600 hover:text-green-600'
                        }`}
                      >
                        <ThumbsUp className="h-4 w-4" />
                        <span>Helpful ({review.helpful})</span>
                      </button>
                      
                      <button
                        onClick={() => handleVoteHelpful(review.id, false)}
                        className={`flex items-center space-x-1 text-sm ${
                          review.userVote === 'not_helpful'
                            ? 'text-red-600'
                            : 'text-gray-600 hover:text-red-600'
                        }`}
                      >
                        <ThumbsDown className="h-4 w-4" />
                        <span>Not Helpful ({review.notHelpful})</span>
                      </button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
