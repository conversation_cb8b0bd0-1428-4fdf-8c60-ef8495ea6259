import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get current date and last month date
    const now = new Date()
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())

    // Get stats
    const [
      totalUsers,
      totalProducts,
      totalServices,
      totalBlogPosts,
      usersThisMonth,
      productsThisMonth,
      servicesThisMonth,
      blogPostsThisMonth
    ] = await Promise.all([
      // Total counts
      prisma.user.count(),
      prisma.product.count(),
      prisma.service.count(),
      prisma.blogPost.count(),
      
      // This month counts
      prisma.user.count({
        where: {
          createdAt: {
            gte: lastMonth
          }
        }
      }),
      prisma.product.count({
        where: {
          createdAt: {
            gte: lastMonth
          }
        }
      }),
      prisma.service.count({
        where: {
          createdAt: {
            gte: lastMonth
          }
        }
      }),
      prisma.blogPost.count({
        where: {
          createdAt: {
            gte: lastMonth
          }
        }
      })
    ])

    // Calculate growth percentages
    const calculateGrowth = (current: number, thisMonth: number) => {
      const lastMonthCount = current - thisMonth
      if (lastMonthCount === 0) return thisMonth > 0 ? 100 : 0
      return ((thisMonth / lastMonthCount) * 100) - 100
    }

    const stats = {
      totalUsers: {
        value: totalUsers,
        change: calculateGrowth(totalUsers, usersThisMonth),
        trend: usersThisMonth > 0 ? 'up' : 'neutral',
        period: 'from last month'
      },
      totalProducts: {
        value: totalProducts,
        change: calculateGrowth(totalProducts, productsThisMonth),
        trend: productsThisMonth > 0 ? 'up' : 'neutral',
        period: 'from last month'
      },
      totalServices: {
        value: totalServices,
        change: calculateGrowth(totalServices, servicesThisMonth),
        trend: servicesThisMonth > 0 ? 'up' : 'neutral',
        period: 'from last month'
      },
      totalBlogPosts: {
        value: totalBlogPosts,
        change: calculateGrowth(totalBlogPosts, blogPostsThisMonth),
        trend: blogPostsThisMonth > 0 ? 'up' : 'neutral',
        period: 'from last month'
      }
    }

    return NextResponse.json({ stats })

  } catch (error) {
    console.error('Admin stats error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
