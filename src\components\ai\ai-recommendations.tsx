"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { useFeaturedProducts } from "@/hooks/use-products"
import { useCartStore } from "@/store/cart-store"
import { useWishlistStore } from "@/store/wishlist-store"
import { formatPrice } from "@/lib/utils"
import { aiService, AIRecommendation } from "@/lib/ai-service"
import {
  Sparkles,
  Star,
  ShoppingCart,
  Heart,
  TrendingUp,
  Users,
  Zap,
  Brain,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
} from "lucide-react"

// Mock product data (in a real app, this would come from your product service)
const mockProducts = {
  "1": {
    id: "1",
    name: "Premium Wireless Headphones",
    price: 199.99,
    comparePrice: 249.99,
    rating: 4.9,
    reviewCount: 234,
    image: "/product-1.jpg",
    category: "Electronics"
  },
  "2": {
    id: "2",
    name: "Smart Fitness Watch",
    price: 299.99,
    rating: 4.8,
    reviewCount: 189,
    image: "/product-2.jpg",
    category: "Electronics"
  },
  "3": {
    id: "3",
    name: "Ergonomic Office Chair",
    price: 449.99,
    comparePrice: 599.99,
    rating: 4.7,
    reviewCount: 156,
    image: "/product-3.jpg",
    category: "Furniture"
  },
  "4": {
    id: "4",
    name: "Wireless Charging Pad",
    price: 49.99,
    rating: 4.6,
    reviewCount: 298,
    image: "/product-4.jpg",
    category: "Accessories"
  },
  "5": {
    id: "5",
    name: "Professional Camera Lens",
    price: 899.99,
    rating: 4.9,
    reviewCount: 67,
    image: "/product-5.jpg",
    category: "Photography"
  },
  "6": {
    id: "6",
    name: "Gaming Mechanical Keyboard",
    price: 159.99,
    rating: 4.8,
    reviewCount: 445,
    image: "/product-6.jpg",
    category: "Gaming"
  }
}

interface AIRecommendationsProps {
  userId?: string
  productId?: string
  category?: string
  title?: string
  subtitle?: string
  limit?: number
  showReason?: boolean
  variant?: 'default' | 'compact' | 'carousel'
  className?: string
}

export function AIRecommendations({
  userId,
  productId,
  category,
  title = "Recommended for You",
  subtitle = "AI-powered recommendations based on your preferences",
  limit = 6,
  showReason = true,
  variant = 'default',
  className = ""
}: AIRecommendationsProps) {
  const [recommendations, setRecommendations] = useState<AIRecommendation[]>([])
  const [loading, setLoading] = useState(true)
  const [currentIndex, setCurrentIndex] = useState(0)
  const { t } = useLanguage()
  const { toast } = useToast()
  const { addItem: addToCart } = useCartStore()
  const { toggleItem: toggleWishlistItem } = useWishlistStore()

  // Fetch real products for recommendations
  const { data: productsData } = useFeaturedProducts(limit * 2)

  useEffect(() => {
    loadRecommendations()
  }, [userId, productId, category, limit])

  const loadRecommendations = async () => {
    try {
      setLoading(true)

      // Try to get AI recommendations first
      let recs: AIRecommendation[] = []
      try {
        recs = await aiService.getProductRecommendations(userId, productId, category, limit)
      } catch (aiError) {
        console.log('AI service unavailable, using fallback recommendations')

        // Fallback to real products if AI service fails
        if (productsData?.products) {
          recs = productsData.products.slice(0, limit).map((product: any, index: number) => ({
            productId: product.id,
            score: 0.9 - (index * 0.1),
            reason: `Popular in ${product.category?.name || 'this category'}`,
            type: 'similar' as const
          }))
        }
      }

      setRecommendations(recs)
    } catch (error) {
      console.error('Failed to load recommendations:', error)
      toast({
        title: "Error",
        description: "Failed to load recommendations",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = () => {
    loadRecommendations()
    toast({
      title: "Refreshed",
      description: "Recommendations updated based on latest data"
    })
  }

  const handleAddToCart = (productId: string) => {
    toast({
      title: "Added to Cart",
      description: "Product added to your cart"
    })
  }

  const handleAddToWishlist = (productId: string) => {
    toast({
      title: "Added to Wishlist",
      description: "Product added to your wishlist"
    })
  }

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % Math.max(1, recommendations.length - 2))
  }

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + Math.max(1, recommendations.length - 2)) % Math.max(1, recommendations.length - 2))
  }

  const getReasonIcon = (reason: string) => {
    if (reason.includes('recent')) return Users
    if (reason.includes('similar')) return TrendingUp
    if (reason.includes('trending')) return Zap
    if (reason.includes('rated')) return Star
    return Brain
  }

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-between">
          <div>
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-48 mb-2"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-64"></div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-gray-200 dark:bg-gray-700 rounded-lg h-80 animate-pulse"></div>
          ))}
        </div>
      </div>
    )
  }

  if (recommendations.length === 0) {
    return null
  }

  const RecommendationCard = ({ recommendation, compact = false }: { recommendation: AIRecommendation, compact?: boolean }) => {
    // Find the real product from the API data
    const product = productsData?.products?.find((p: any) => p.id === recommendation.productId)
    if (!product) return null

    const ReasonIcon = getReasonIcon(recommendation.reason)

    const handleAddToCart = () => {
      addToCart({
        id: product.id,
        productId: product.id,
        name: product.name,
        slug: product.slug,
        price: product.price,
        image: product.images?.[0]?.url || '/placeholder-product.jpg',
        inStock: product.inStock !== false,
        maxQuantity: 10,
      })

      toast({
        title: "Added to Cart",
        description: `${product.name} has been added to your cart`,
      })
    }

    const handleAddToWishlist = () => {
      const wasAdded = toggleWishlistItem({
        productId: product.id,
        name: product.name,
        slug: product.slug,
        price: product.price,
        comparePrice: product.comparePrice,
        image: product.images?.[0]?.url || '/placeholder-product.jpg',
        inStock: product.inStock !== false,
        category: product.category?.name || '',
        rating: product.averageRating || 0,
      })

      toast({
        title: wasAdded ? "Added to Wishlist" : "Removed from Wishlist",
        description: `${product.name} has been ${wasAdded ? 'added to' : 'removed from'} your wishlist`,
      })
    }

    return (
      <Card className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
        <div className="relative">
          {/* Product Image */}
          <div className={`${compact ? 'aspect-square' : 'aspect-[4/3]'} bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 flex items-center justify-center relative overflow-hidden`}>
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
            <div className="text-6xl">📱</div>
            
            {/* AI Badge */}
            <div className="absolute top-3 left-3">
              <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0">
                <Sparkles className="h-3 w-3 mr-1" />
                AI Pick
              </Badge>
            </div>

            {/* Quick Actions */}
            <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity space-y-2">
              <Button
                size="sm"
                variant="secondary"
                onClick={() => handleAddToWishlist(product.id)}
                className="w-8 h-8 p-0"
              >
                <Heart className="h-4 w-4" />
              </Button>
            </div>

            {/* Confidence Score */}
            <div className="absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
              <Badge variant="secondary" className="text-xs">
                {Math.round(recommendation.score * 100)}% match
              </Badge>
            </div>
          </div>
        </div>

        <CardContent className={compact ? "p-4" : "p-6"}>
          <div className="space-y-3">
            {/* Product Info */}
            <div>
              <h3 className={`${compact ? 'text-sm' : 'text-lg'} font-semibold group-hover:text-primary-600 transition-colors line-clamp-2`}>
                <Link href={`/product/${product.slug}`}>
                  {product.name}
                </Link>
              </h3>
              
              <div className="flex items-center space-x-2 mt-1">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-3 w-3 ${
                        i < Math.floor(product.averageRating || 0)
                          ? "text-yellow-400 fill-current"
                          : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
                <span className="text-xs text-gray-500">
                  ({product.reviewCount || 0})
                </span>
              </div>
            </div>

            {/* Price */}
            <div className="flex items-center space-x-2">
              <span className={`${compact ? 'text-lg' : 'text-xl'} font-bold text-primary-600 dark:text-primary-400`}>
                {formatPrice(product.price)}
              </span>
              {product.comparePrice && (
                <span className="text-sm text-gray-500 line-through">
                  {formatPrice(product.comparePrice)}
                </span>
              )}
            </div>

            {/* AI Reason */}
            {showReason && (
              <div className="flex items-start space-x-2 p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <ReasonIcon className="h-4 w-4 text-purple-600 dark:text-purple-400 mt-0.5 flex-shrink-0" />
                <p className="text-xs text-purple-700 dark:text-purple-300">
                  {recommendation.reason}
                </p>
              </div>
            )}

            {/* Actions */}
            <div className="flex space-x-2 pt-2">
              <Button
                size={compact ? "sm" : "default"}
                onClick={handleAddToCart}
                className="flex-1"
              >
                <ShoppingCart className="h-4 w-4 mr-2" />
                Add to Cart
              </Button>
              <Link href={`/product/${product.slug}`}>
                <Button variant="outline" size={compact ? "sm" : "default"}>
                  View
                </Button>
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (variant === 'carousel') {
    return (
      <div className={`space-y-6 ${className}`}>
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold flex items-center">
              <Brain className="h-6 w-6 mr-2 text-purple-600" />
              {title}
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mt-1">{subtitle}</p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <div className="flex space-x-1">
              <Button variant="outline" size="sm" onClick={prevSlide}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={nextSlide}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Carousel */}
        <div className="relative overflow-hidden">
          <div 
            className="flex transition-transform duration-300 ease-in-out"
            style={{ transform: `translateX(-${currentIndex * 33.333}%)` }}
          >
            {recommendations.map((recommendation) => (
              <div key={recommendation.productId} className="w-1/3 flex-shrink-0 px-3">
                <RecommendationCard recommendation={recommendation} />
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (variant === 'compact') {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold flex items-center">
            <Sparkles className="h-5 w-5 mr-2 text-purple-600" />
            {title}
          </h3>
          <Button variant="ghost" size="sm" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {recommendations.slice(0, 6).map((recommendation) => (
            <RecommendationCard 
              key={recommendation.productId} 
              recommendation={recommendation} 
              compact={true}
            />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center">
            <Brain className="h-6 w-6 mr-2 text-purple-600" />
            {title}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">{subtitle}</p>
        </div>
        <Button variant="outline" onClick={handleRefresh}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh AI Picks
        </Button>
      </div>

      {/* Recommendations Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {recommendations.map((recommendation) => (
          <RecommendationCard key={recommendation.productId} recommendation={recommendation} />
        ))}
      </div>

      {/* AI Info */}
      <div className="text-center">
        <p className="text-sm text-gray-500 dark:text-gray-400 flex items-center justify-center">
          <Sparkles className="h-4 w-4 mr-2" />
          Powered by AI • Recommendations improve with your activity
        </p>
      </div>
    </div>
  )
}
