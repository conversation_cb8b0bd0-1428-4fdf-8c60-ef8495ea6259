import { useQuery } from '@tanstack/react-query'

export interface AdminStats {
  totalUsers: {
    value: number
    change: number
    trend: 'up' | 'down' | 'neutral'
    period: string
  }
  totalProducts: {
    value: number
    change: number
    trend: 'up' | 'down' | 'neutral'
    period: string
  }
  totalServices: {
    value: number
    change: number
    trend: 'up' | 'down' | 'neutral'
    period: string
  }
  totalBlogPosts: {
    value: number
    change: number
    trend: 'up' | 'down' | 'neutral'
    period: string
  }
}

export function useAdminStats() {
  return useQuery<{ stats: AdminStats }>({
    queryKey: ['admin-stats'],
    queryFn: async () => {
      const response = await fetch('/api/admin/stats')
      if (!response.ok) {
        throw new Error('Failed to fetch admin stats')
      }
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}
