"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useAuth } from "@/components/providers/auth-provider"
import { useCart } from "@/components/providers/cart-provider"
import { useWishlist } from "@/components/providers/wishlist-provider"
import { useToast } from "@/hooks/use-toast"
import { formatPrice } from "@/lib/utils"
import {
  Zap,
  Clock,
  ShoppingCart,
  Heart,
  Star,
  Tag,
  Flame,
  Gift,
  ArrowRight,
  Timer,
} from "lucide-react"

interface Deal {
  id: string
  title: string
  description: string
  originalPrice: number
  salePrice: number
  discount: number
  image: string
  category: string
  rating: number
  reviews: number
  stock: number
  sold: number
  endTime: Date
  featured: boolean
  flashSale: boolean
}

const deals: Deal[] = [
  {
    id: "deal-1",
    title: "Premium Wireless Headphones",
    description: "High-quality noise-canceling headphones with 30-hour battery life",
    originalPrice: 299.99,
    salePrice: 199.99,
    discount: 33,
    image: "/product-1.jpg",
    category: "Electronics",
    rating: 4.8,
    reviews: 1247,
    stock: 50,
    sold: 150,
    endTime: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days
    featured: true,
    flashSale: false
  },
  {
    id: "deal-2",
    title: "Smart Fitness Watch",
    description: "Advanced fitness tracking with heart rate monitor and GPS",
    originalPrice: 399.99,
    salePrice: 249.99,
    discount: 38,
    image: "/product-2.jpg",
    category: "Wearables",
    rating: 4.6,
    reviews: 892,
    stock: 25,
    sold: 75,
    endTime: new Date(Date.now() + 6 * 60 * 60 * 1000), // 6 hours
    featured: false,
    flashSale: true
  },
  {
    id: "deal-3",
    title: "Professional Camera Lens",
    description: "85mm f/1.4 portrait lens for professional photography",
    originalPrice: 1299.99,
    salePrice: 899.99,
    discount: 31,
    image: "/product-3.jpg",
    category: "Photography",
    rating: 4.9,
    reviews: 456,
    stock: 15,
    sold: 35,
    endTime: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000), // 1 day
    featured: true,
    flashSale: false
  },
  {
    id: "deal-4",
    title: "Gaming Mechanical Keyboard",
    description: "RGB backlit mechanical keyboard with custom switches",
    originalPrice: 179.99,
    salePrice: 119.99,
    discount: 33,
    image: "/product-4.jpg",
    category: "Gaming",
    rating: 4.7,
    reviews: 2103,
    stock: 100,
    sold: 200,
    endTime: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days
    featured: false,
    flashSale: false
  }
]

function CountdownTimer({ endTime }: { endTime: Date }) {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  })

  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date().getTime()
      const distance = endTime.getTime() - now

      if (distance > 0) {
        setTimeLeft({
          days: Math.floor(distance / (1000 * 60 * 60 * 24)),
          hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((distance % (1000 * 60)) / 1000)
        })
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 })
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [endTime])

  return (
    <div className="flex items-center space-x-2">
      <Timer className="h-4 w-4 text-red-500" />
      <div className="flex space-x-1 text-sm font-mono">
        {timeLeft.days > 0 && (
          <>
            <span className="bg-red-500 text-white px-2 py-1 rounded">{timeLeft.days}d</span>
          </>
        )}
        <span className="bg-red-500 text-white px-2 py-1 rounded">{timeLeft.hours.toString().padStart(2, '0')}h</span>
        <span className="bg-red-500 text-white px-2 py-1 rounded">{timeLeft.minutes.toString().padStart(2, '0')}m</span>
        <span className="bg-red-500 text-white px-2 py-1 rounded">{timeLeft.seconds.toString().padStart(2, '0')}s</span>
      </div>
    </div>
  )
}

export function DealsSection() {
  const { user } = useAuth()
  const { addToCart } = useCart()
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist()
  const { toast } = useToast()
  const { t } = useLanguage()

  const handleAddToCart = (deal: Deal) => {
    if (!user) {
      toast({
        title: "Login Required",
        description: "Please log in to add items to your cart.",
        variant: "destructive"
      })
      return
    }

    addToCart({
      id: deal.id,
      name: deal.title,
      price: deal.salePrice,
      image: deal.image,
      quantity: 1
    })

    toast({
      title: "Added to Cart",
      description: `${deal.title} has been added to your cart.`
    })
  }

  const handleWishlistToggle = (deal: Deal) => {
    if (!user) {
      toast({
        title: "Login Required",
        description: "Please log in to manage your wishlist.",
        variant: "destructive"
      })
      return
    }

    if (isInWishlist(deal.id)) {
      removeFromWishlist(deal.id)
      toast({
        title: "Removed from Wishlist",
        description: `${deal.title} has been removed from your wishlist.`
      })
    } else {
      addToWishlist({
        id: deal.id,
        name: deal.title,
        price: deal.salePrice,
        originalPrice: deal.originalPrice,
        image: deal.image,
        rating: deal.rating
      })
      toast({
        title: "Added to Wishlist",
        description: `${deal.title} has been added to your wishlist.`
      })
    }
  }

  const featuredDeals = deals.filter(deal => deal.featured)
  const flashSales = deals.filter(deal => deal.flashSale)

  return (
    <section className="py-20 bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 dark:from-red-900/20 dark:via-orange-900/20 dark:to-yellow-900/20">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge className="mb-4 px-4 py-2 bg-gradient-to-r from-red-500 to-orange-500 text-white">
            <Flame className="h-4 w-4 mr-2" />
            Limited Time Offers
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent">
              Hot Deals & Discounts
            </span>
            <br />
            <span className="text-gray-900 dark:text-white">Save Big Today!</span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed">
            Don't miss out on these incredible deals! Limited quantities available with 
            countdown timers showing exactly when these offers expire.
          </p>
        </div>

        {/* Flash Sales Banner */}
        {flashSales.length > 0 && (
          <div className="mb-12">
            <div className="bg-gradient-to-r from-red-500 to-orange-500 text-white rounded-2xl p-6 mb-8">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                    <Zap className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold">⚡ Flash Sale</h3>
                    <p className="text-red-100">Limited time offers ending soon!</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-red-100 mb-1">Ends in:</div>
                  <CountdownTimer endTime={flashSales[0].endTime} />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Featured Deals */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {featuredDeals.slice(0, 2).map((deal) => (
            <Card key={deal.id} className="overflow-hidden hover:shadow-2xl transition-all duration-300 group">
              <div className="relative">
                {/* Product Image */}
                <div className="h-64 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center relative overflow-hidden">
                  <div className="text-6xl opacity-50">📱</div>
                  
                  {/* Discount Badge */}
                  <div className="absolute top-4 left-4">
                    <Badge className="bg-gradient-to-r from-red-500 to-orange-500 text-white text-lg px-3 py-1">
                      -{deal.discount}%
                    </Badge>
                  </div>

                  {/* Wishlist Button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute top-4 right-4 w-10 h-10 rounded-full bg-white/80 hover:bg-white"
                    onClick={() => handleWishlistToggle(deal)}
                  >
                    <Heart className={`h-4 w-4 ${isInWishlist(deal.id) ? 'fill-red-500 text-red-500' : 'text-gray-600'}`} />
                  </Button>

                  {/* Flash Sale Indicator */}
                  {deal.flashSale && (
                    <div className="absolute top-4 right-16">
                      <Badge className="bg-yellow-500 text-black animate-pulse">
                        <Zap className="h-3 w-3 mr-1" />
                        Flash
                      </Badge>
                    </div>
                  )}
                </div>

                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Category & Rating */}
                    <div className="flex items-center justify-between">
                      <Badge variant="outline">{deal.category}</Badge>
                      <div className="flex items-center space-x-1">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="text-sm font-medium">{deal.rating}</span>
                        <span className="text-sm text-gray-500">({deal.reviews})</span>
                      </div>
                    </div>

                    {/* Title & Description */}
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                        {deal.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 text-sm">
                        {deal.description}
                      </p>
                    </div>

                    {/* Pricing */}
                    <div className="flex items-center space-x-3">
                      <span className="text-3xl font-bold text-red-600">
                        {formatPrice(deal.salePrice)}
                      </span>
                      <span className="text-lg text-gray-500 line-through">
                        {formatPrice(deal.originalPrice)}
                      </span>
                      <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        Save {formatPrice(deal.originalPrice - deal.salePrice)}
                      </Badge>
                    </div>

                    {/* Stock & Progress */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">
                          {deal.stock} left in stock
                        </span>
                        <span className="text-gray-600 dark:text-gray-400">
                          {deal.sold} sold
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-gradient-to-r from-red-500 to-orange-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${(deal.sold / (deal.sold + deal.stock)) * 100}%` }}
                        />
                      </div>
                    </div>

                    {/* Countdown Timer */}
                    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Offer ends in:
                        </span>
                        <CountdownTimer endTime={deal.endTime} />
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-3">
                      <Button 
                        onClick={() => handleAddToCart(deal)}
                        className="flex-1 bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 group"
                      >
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        Add to Cart
                      </Button>
                      <Link href={`/products/${deal.id}`}>
                        <Button variant="outline" className="px-6">
                          View Details
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </div>
            </Card>
          ))}
        </div>

        {/* More Deals Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {deals.slice(2).map((deal) => (
            <Card key={deal.id} className="overflow-hidden hover:shadow-lg transition-all duration-300 group">
              <div className="relative">
                {/* Product Image */}
                <div className="h-48 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center relative">
                  <div className="text-4xl opacity-50">📱</div>
                  
                  {/* Discount Badge */}
                  <div className="absolute top-2 left-2">
                    <Badge className="bg-red-500 text-white">
                      -{deal.discount}%
                    </Badge>
                  </div>

                  {/* Wishlist Button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute top-2 right-2 w-8 h-8 rounded-full bg-white/80 hover:bg-white"
                    onClick={() => handleWishlistToggle(deal)}
                  >
                    <Heart className={`h-3 w-3 ${isInWishlist(deal.id) ? 'fill-red-500 text-red-500' : 'text-gray-600'}`} />
                  </Button>
                </div>

                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white text-sm">
                        {deal.title}
                      </h4>
                      <div className="flex items-center space-x-1 mt-1">
                        <Star className="h-3 w-3 text-yellow-400 fill-current" />
                        <span className="text-xs">{deal.rating}</span>
                        <span className="text-xs text-gray-500">({deal.reviews})</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <span className="text-lg font-bold text-red-600">
                        {formatPrice(deal.salePrice)}
                      </span>
                      <span className="text-sm text-gray-500 line-through">
                        {formatPrice(deal.originalPrice)}
                      </span>
                    </div>

                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      <CountdownTimer endTime={deal.endTime} />
                    </div>

                    <Button 
                      onClick={() => handleAddToCart(deal)}
                      size="sm"
                      className="w-full bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600"
                    >
                      <ShoppingCart className="h-3 w-3 mr-1" />
                      Add to Cart
                    </Button>
                  </div>
                </CardContent>
              </div>
            </Card>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-red-500 to-orange-500 text-white rounded-2xl p-8">
            <h3 className="text-2xl font-bold mb-4">
              Don't Miss Out on These Amazing Deals!
            </h3>
            <p className="text-red-100 mb-6 max-w-2xl mx-auto">
              New deals added daily. Sign up for our newsletter to be the first to know about 
              exclusive offers and flash sales.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/deals">
                <Button size="lg" className="px-8 py-4 text-lg font-semibold bg-white text-red-600 hover:bg-gray-100 group">
                  View All Deals
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Button>
              </Link>
              <Button variant="outline" size="lg" className="px-8 py-4 text-lg font-semibold border-white text-white hover:bg-white/10">
                <Gift className="mr-2 h-5 w-5" />
                Subscribe for Deals
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
