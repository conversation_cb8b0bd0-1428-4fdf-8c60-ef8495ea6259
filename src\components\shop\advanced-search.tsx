"use client"

import { useState, useEffect, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { formatPrice } from "@/lib/utils"
import {
  Search,
  Filter,
  X,
  Star,
  ChevronDown,
  ChevronUp,
  SlidersHorizontal,
  MapPin,
  Truck,
  Shield,
  Clock,
  Award,
  Zap,
  TrendingUp,
  Sparkles,
} from "lucide-react"

interface Product {
  id: string
  name: string
  price: number
  originalPrice?: number
  rating: number
  reviewCount: number
  image: string
  category: string
  brand: string
  inStock: boolean
  isNew: boolean
  isBestseller: boolean
  isFeatured: boolean
  tags: string[]
  description: string
  specifications: Record<string, string>
}

interface SearchFilters {
  query: string
  category: string
  brand: string
  minPrice: number
  maxPrice: number
  rating: number
  inStock: boolean
  isNew: boolean
  isBestseller: boolean
  isFeatured: boolean
  sortBy: string
  tags: string[]
}

// Mock products data with more detailed information
const mockProducts: Product[] = [
  {
    id: "1",
    name: "Premium Wireless Headphones",
    price: 199.99,
    originalPrice: 249.99,
    rating: 4.8,
    reviewCount: 324,
    image: "/product-1.jpg",
    category: "Electronics",
    brand: "AudioTech",
    inStock: true,
    isNew: false,
    isBestseller: true,
    isFeatured: true,
    tags: ["wireless", "bluetooth", "noise-cancelling", "premium"],
    description: "High-quality wireless headphones with active noise cancellation",
    specifications: {
      "Battery Life": "30 hours",
      "Connectivity": "Bluetooth 5.0",
      "Weight": "250g",
      "Warranty": "2 years"
    }
  },
  {
    id: "2",
    name: "Smart Fitness Watch",
    price: 299.99,
    rating: 4.6,
    reviewCount: 189,
    image: "/product-2.jpg",
    category: "Wearables",
    brand: "FitTech",
    inStock: true,
    isNew: true,
    isBestseller: false,
    isFeatured: true,
    tags: ["fitness", "smartwatch", "health", "gps"],
    description: "Advanced fitness tracking with GPS and health monitoring",
    specifications: {
      "Display": "1.4 inch AMOLED",
      "Battery": "7 days",
      "Water Resistance": "50m",
      "Sensors": "Heart rate, GPS, Accelerometer"
    }
  },
  {
    id: "3",
    name: "Professional Camera Lens",
    price: 899.99,
    rating: 4.9,
    reviewCount: 76,
    image: "/product-3.jpg",
    category: "Photography",
    brand: "LensMaster",
    inStock: false,
    isNew: false,
    isBestseller: false,
    isFeatured: true,
    tags: ["photography", "professional", "lens", "camera"],
    description: "Professional-grade camera lens for stunning photography",
    specifications: {
      "Focal Length": "24-70mm",
      "Aperture": "f/2.8",
      "Mount": "Canon EF",
      "Weight": "805g"
    }
  },
  {
    id: "4",
    name: "Gaming Mechanical Keyboard",
    price: 149.99,
    originalPrice: 179.99,
    rating: 4.7,
    reviewCount: 412,
    image: "/product-4.jpg",
    category: "Gaming",
    brand: "GamePro",
    inStock: true,
    isNew: false,
    isBestseller: true,
    isFeatured: false,
    tags: ["gaming", "mechanical", "rgb", "keyboard"],
    description: "High-performance mechanical keyboard for gaming enthusiasts",
    specifications: {
      "Switch Type": "Cherry MX Blue",
      "Backlight": "RGB",
      "Layout": "Full Size",
      "Connection": "USB-C"
    }
  },
  {
    id: "5",
    name: "Wireless Charging Pad",
    price: 49.99,
    rating: 4.3,
    reviewCount: 203,
    image: "/product-5.jpg",
    category: "Accessories",
    brand: "ChargeTech",
    inStock: true,
    isNew: true,
    isBestseller: false,
    isFeatured: false,
    tags: ["wireless", "charging", "qi", "fast-charge"],
    description: "Fast wireless charging pad compatible with all Qi devices",
    specifications: {
      "Power Output": "15W",
      "Compatibility": "Qi-enabled devices",
      "Size": "100mm diameter",
      "Cable": "USB-C included"
    }
  }
]

const categories = ["All", "Electronics", "Wearables", "Photography", "Gaming", "Accessories"]
const brands = ["All", "AudioTech", "FitTech", "LensMaster", "GamePro", "ChargeTech"]
const sortOptions = [
  { value: "relevance", label: "Most Relevant" },
  { value: "price-low", label: "Price: Low to High" },
  { value: "price-high", label: "Price: High to Low" },
  { value: "rating", label: "Highest Rated" },
  { value: "newest", label: "Newest First" },
  { value: "bestseller", label: "Best Sellers" }
]

interface AdvancedSearchProps {
  onProductsChange: (products: Product[]) => void
}

export default function AdvancedSearch({ onProductsChange }: AdvancedSearchProps) {
  const [filters, setFilters] = useState<SearchFilters>({
    query: "",
    category: "All",
    brand: "All",
    minPrice: 0,
    maxPrice: 1000,
    rating: 0,
    inStock: false,
    isNew: false,
    isBestseller: false,
    isFeatured: false,
    sortBy: "relevance",
    tags: []
  })
  
  const [showFilters, setShowFilters] = useState(false)
  const [priceRange, setPriceRange] = useState([0, 1000])
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  
  const { t } = useLanguage()
  const { toast } = useToast()

  // Get all unique tags from products
  const allTags = useMemo(() => {
    const tags = new Set<string>()
    mockProducts.forEach(product => {
      product.tags.forEach(tag => tags.add(tag))
    })
    return Array.from(tags)
  }, [])

  // Filter and sort products
  const filteredProducts = useMemo(() => {
    let filtered = mockProducts.filter(product => {
      // Text search
      if (filters.query) {
        const query = filters.query.toLowerCase()
        const searchableText = `${product.name} ${product.description} ${product.brand} ${product.tags.join(' ')}`.toLowerCase()
        if (!searchableText.includes(query)) return false
      }

      // Category filter
      if (filters.category !== "All" && product.category !== filters.category) return false

      // Brand filter
      if (filters.brand !== "All" && product.brand !== filters.brand) return false

      // Price range
      if (product.price < filters.minPrice || product.price > filters.maxPrice) return false

      // Rating filter
      if (product.rating < filters.rating) return false

      // Stock filter
      if (filters.inStock && !product.inStock) return false

      // Special filters
      if (filters.isNew && !product.isNew) return false
      if (filters.isBestseller && !product.isBestseller) return false
      if (filters.isFeatured && !product.isFeatured) return false

      // Tags filter
      if (filters.tags.length > 0) {
        const hasMatchingTag = filters.tags.some(tag => product.tags.includes(tag))
        if (!hasMatchingTag) return false
      }

      return true
    })

    // Sort products
    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case "price-low":
          return a.price - b.price
        case "price-high":
          return b.price - a.price
        case "rating":
          return b.rating - a.rating
        case "newest":
          return (b.isNew ? 1 : 0) - (a.isNew ? 1 : 0)
        case "bestseller":
          return (b.isBestseller ? 1 : 0) - (a.isBestseller ? 1 : 0)
        default: // relevance
          // Score based on multiple factors
          const scoreA = (a.rating * 0.3) + (a.reviewCount * 0.0001) + (a.isFeatured ? 0.5 : 0) + (a.isBestseller ? 0.3 : 0)
          const scoreB = (b.rating * 0.3) + (b.reviewCount * 0.0001) + (b.isFeatured ? 0.5 : 0) + (b.isBestseller ? 0.3 : 0)
          return scoreB - scoreA
      }
    })

    return filtered
  }, [filters])

  // Update parent component when products change
  useEffect(() => {
    onProductsChange(filteredProducts)
  }, [filteredProducts, onProductsChange])

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handleTagToggle = (tag: string) => {
    const newTags = selectedTags.includes(tag)
      ? selectedTags.filter(t => t !== tag)
      : [...selectedTags, tag]
    
    setSelectedTags(newTags)
    handleFilterChange('tags', newTags)
  }

  const clearFilters = () => {
    setFilters({
      query: "",
      category: "All",
      brand: "All",
      minPrice: 0,
      maxPrice: 1000,
      rating: 0,
      inStock: false,
      isNew: false,
      isBestseller: false,
      isFeatured: false,
      sortBy: "relevance",
      tags: []
    })
    setSelectedTags([])
    setPriceRange([0, 1000])
  }

  const activeFiltersCount = useMemo(() => {
    let count = 0
    if (filters.query) count++
    if (filters.category !== "All") count++
    if (filters.brand !== "All") count++
    if (filters.minPrice > 0 || filters.maxPrice < 1000) count++
    if (filters.rating > 0) count++
    if (filters.inStock) count++
    if (filters.isNew) count++
    if (filters.isBestseller) count++
    if (filters.isFeatured) count++
    if (filters.tags.length > 0) count++
    return count
  }, [filters])

  return (
    <div className="space-y-6">
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <input
          type="text"
          placeholder="Search products, brands, categories..."
          value={filters.query}
          onChange={(e) => handleFilterChange('query', e.target.value)}
          className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
        />
      </div>

      {/* Quick Filters and Sort */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-2"
          >
            <SlidersHorizontal className="h-4 w-4" />
            <span>Filters</span>
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount}
              </Badge>
            )}
            {showFilters ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>

          {activeFiltersCount > 0 && (
            <Button variant="ghost" onClick={clearFilters} className="text-sm">
              <X className="h-4 w-4 mr-1" />
              Clear All
            </Button>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600 dark:text-gray-400">Sort by:</span>
          <select
            value={filters.sortBy}
            onChange={(e) => handleFilterChange('sortBy', e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 text-sm"
          >
            {sortOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Advanced Filters Panel */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Advanced Filters
            </CardTitle>
            <CardDescription>
              Refine your search with detailed filters
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Category and Brand */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Category</label>
                <select
                  value={filters.category}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Brand</label>
                <select
                  value={filters.brand}
                  onChange={(e) => handleFilterChange('brand', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                >
                  {brands.map(brand => (
                    <option key={brand} value={brand}>{brand}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Price Range */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Price Range: {formatPrice(filters.minPrice)} - {formatPrice(filters.maxPrice)}
              </label>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <input
                    type="number"
                    placeholder="Min Price"
                    value={filters.minPrice}
                    onChange={(e) => handleFilterChange('minPrice', Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                  />
                </div>
                <div>
                  <input
                    type="number"
                    placeholder="Max Price"
                    value={filters.maxPrice}
                    onChange={(e) => handleFilterChange('maxPrice', Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>
            </div>

            {/* Rating Filter */}
            <div>
              <label className="block text-sm font-medium mb-2">Minimum Rating</label>
              <div className="flex items-center space-x-2">
                {[1, 2, 3, 4, 5].map((rating) => (
                  <button
                    key={rating}
                    onClick={() => handleFilterChange('rating', rating === filters.rating ? 0 : rating)}
                    className={`flex items-center space-x-1 px-3 py-2 rounded-lg border transition-colors ${
                      filters.rating >= rating
                        ? 'bg-yellow-50 border-yellow-300 text-yellow-800'
                        : 'bg-white border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <Star className={`h-4 w-4 ${filters.rating >= rating ? 'text-yellow-400 fill-current' : 'text-gray-400'}`} />
                    <span className="text-sm">{rating}+</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Special Filters */}
            <div>
              <label className="block text-sm font-medium mb-2">Special Filters</label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.inStock}
                    onChange={(e) => handleFilterChange('inStock', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="text-sm">In Stock</span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.isNew}
                    onChange={(e) => handleFilterChange('isNew', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="text-sm flex items-center">
                    <Sparkles className="h-3 w-3 mr-1" />
                    New
                  </span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.isBestseller}
                    onChange={(e) => handleFilterChange('isBestseller', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="text-sm flex items-center">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    Bestseller
                  </span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.isFeatured}
                    onChange={(e) => handleFilterChange('isFeatured', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="text-sm flex items-center">
                    <Award className="h-3 w-3 mr-1" />
                    Featured
                  </span>
                </label>
              </div>
            </div>

            {/* Tags Filter */}
            <div>
              <label className="block text-sm font-medium mb-2">Tags</label>
              <div className="flex flex-wrap gap-2">
                {allTags.map(tag => (
                  <button
                    key={tag}
                    onClick={() => handleTagToggle(tag)}
                    className={`px-3 py-1 rounded-full text-sm border transition-colors ${
                      selectedTags.includes(tag)
                        ? 'bg-primary-100 border-primary-300 text-primary-800'
                        : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results Summary */}
      <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
        <span>
          Showing {filteredProducts.length} of {mockProducts.length} products
        </span>
        {filters.query && (
          <span>
            Search results for "{filters.query}"
          </span>
        )}
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.query && (
            <Badge variant="secondary" className="flex items-center space-x-1">
              <span>Search: "{filters.query}"</span>
              <button onClick={() => handleFilterChange('query', '')}>
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          {filters.category !== "All" && (
            <Badge variant="secondary" className="flex items-center space-x-1">
              <span>Category: {filters.category}</span>
              <button onClick={() => handleFilterChange('category', 'All')}>
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          {filters.brand !== "All" && (
            <Badge variant="secondary" className="flex items-center space-x-1">
              <span>Brand: {filters.brand}</span>
              <button onClick={() => handleFilterChange('brand', 'All')}>
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          {(filters.minPrice > 0 || filters.maxPrice < 1000) && (
            <Badge variant="secondary" className="flex items-center space-x-1">
              <span>Price: {formatPrice(filters.minPrice)} - {formatPrice(filters.maxPrice)}</span>
              <button onClick={() => {
                handleFilterChange('minPrice', 0)
                handleFilterChange('maxPrice', 1000)
              }}>
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          {filters.rating > 0 && (
            <Badge variant="secondary" className="flex items-center space-x-1">
              <span>{filters.rating}+ Stars</span>
              <button onClick={() => handleFilterChange('rating', 0)}>
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          {selectedTags.map(tag => (
            <Badge key={tag} variant="secondary" className="flex items-center space-x-1">
              <span>{tag}</span>
              <button onClick={() => handleTagToggle(tag)}>
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
}
