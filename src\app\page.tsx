"use client"

import { ChinaArabHeroSection } from "@/components/home/<USER>"
import { SuppliersSection } from "@/components/home/<USER>"
import { FeaturedProductsSection } from "@/components/home/<USER>"
import { ServicesSection } from "@/components/home/<USER>"
import { ProductionLinesSection } from "@/components/home/<USER>"
import { DealsSection } from "@/components/home/<USER>"
import { BlogSection } from "@/components/home/<USER>"
import { NewsletterSection } from "@/components/home/<USER>"
import { StatsSection } from "@/components/home/<USER>"
import { TestimonialsSection } from "@/components/home/<USER>"
import { PartnersSection } from "@/components/home/<USER>"
import { AIRecommendations } from "@/components/ai/ai-recommendations"
import { AIChatSupport } from "@/components/ai/ai-chat-support"

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* China-Arab Hero Section - Wholesale marketplace focus */}
      <ChinaArabHeroSection />

      {/* Verified Chinese Suppliers - Showcase trusted suppliers */}
      <SuppliersSection />

      {/* Featured Wholesale Products - with MOQ and pricing tiers */}
      <FeaturedProductsSection />

      {/* Professional Services - Inspection, shipping, certification */}
      <ServicesSection />

      {/* Production Lines & Machinery - Industrial equipment showcase */}
      <ProductionLinesSection />

      {/* Deals & Discounts - with countdown timers for limited offers */}
      <DealsSection />

      {/* AI-Powered Recommendations */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <AIRecommendations
            title="Recommended Just for You"
            subtitle="AI-powered product recommendations based on your preferences and browsing history"
            limit={6}
            showReason={true}
            variant="default"
          />
        </div>
      </section>

      {/* Blog Articles - latest 4 articles in a single row with attractive design */}
      <BlogSection />

      {/* Company Statistics - animated statistics with ascending counters */}
      <StatsSection />

      {/* Testimonials - customer testimonials with photos and ratings */}
      <TestimonialsSection />

      {/* Newsletter Subscription - subscription form with email verification */}
      <NewsletterSection />

      {/* Partners - partner logos with horizontal scroll effect */}
      <PartnersSection />

      {/* Stock Liquidation Deals - Special wholesale offers */}
      <DealsSection />

      {/* AI Chat Support */}
      <AIChatSupport
        initialMessage="Hi! I'm your AI shopping assistant. I can help you find products, track orders, or answer any questions you have!"
        position="bottom-right"
      />
    </div>
  )
}
