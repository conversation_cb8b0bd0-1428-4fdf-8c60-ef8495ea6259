"use client"

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import {
  Search,
  Package,
  Truck,
  Shield,
  Users,
  FileCheck,
  Globe,
  Upload,
  Phone,
  Mail,
  MessageCircle,
} from 'lucide-react'

interface ServiceRequestFormProps {
  serviceType?: string
}

const serviceTypes = [
  {
    id: 'INSPECTION',
    name: 'Product Inspection',
    nameAr: 'فحص المنتجات',
    description: 'Quality control and inspection services for your products',
    descriptionAr: 'خدمات مراقبة الجودة وفحص منتجاتك',
    icon: Search,
    estimatedDays: '3-5',
    basePrice: 150,
  },
  {
    id: 'STORAGE',
    name: 'Warehouse Storage',
    nameAr: 'التخزين في المستودع',
    description: 'Secure storage solutions for your inventory',
    descriptionAr: 'حلول تخزين آمنة لمخزونك',
    icon: Package,
    estimatedDays: '1-30',
    basePrice: 50,
  },
  {
    id: 'SHIPPING',
    name: 'Shipping & Logistics',
    nameAr: 'الشحن واللوجستيات',
    description: 'International shipping and logistics management',
    descriptionAr: 'إدارة الشحن الدولي واللوجستيات',
    icon: Truck,
    estimatedDays: '7-21',
    basePrice: 200,
  },
  {
    id: 'CERTIFICATION',
    name: 'Product Certification',
    nameAr: 'شهادة المنتج',
    description: 'Help obtaining required certificates and documentation',
    descriptionAr: 'المساعدة في الحصول على الشهادات والوثائق المطلوبة',
    icon: FileCheck,
    estimatedDays: '10-20',
    basePrice: 300,
  },
  {
    id: 'CONSULTING',
    name: 'Business Consulting',
    nameAr: 'الاستشارات التجارية',
    description: 'Expert advice on sourcing, importing, and business development',
    descriptionAr: 'مشورة الخبراء حول التوريد والاستيراد وتطوير الأعمال',
    icon: Users,
    estimatedDays: '5-10',
    basePrice: 100,
  },
  {
    id: 'QUALITY_CONTROL',
    name: 'Quality Control',
    nameAr: 'مراقبة الجودة',
    description: 'Comprehensive quality assurance and testing',
    descriptionAr: 'ضمان الجودة الشامل والاختبار',
    icon: Shield,
    estimatedDays: '5-7',
    basePrice: 180,
  },
]

export function ServiceRequestForm({ serviceType }: ServiceRequestFormProps) {
  const { data: session } = useSession()
  const router = useRouter()
  const { toast } = useToast()
  
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedService, setSelectedService] = useState(serviceType || '')
  const [formData, setFormData] = useState({
    serviceType: serviceType || '',
    title: '',
    description: '',
    priority: 'MEDIUM',
    customerName: session?.user?.name || '',
    customerEmail: session?.user?.email || '',
    customerPhone: '',
    customerCompany: '',
    urgentDeadline: '',
    budget: '',
    additionalNotes: '',
  })

  const selectedServiceInfo = serviceTypes.find(s => s.id === selectedService)

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleServiceSelect = (serviceId: string) => {
    setSelectedService(serviceId)
    setFormData(prev => ({ ...prev, serviceType: serviceId }))
    
    const service = serviceTypes.find(s => s.id === serviceId)
    if (service && !formData.title) {
      setFormData(prev => ({ 
        ...prev, 
        title: `${service.name} Request`,
        serviceType: serviceId 
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!session) {
      toast({
        title: "Authentication Required",
        description: "Please log in to submit a service request",
        variant: "destructive",
      })
      return
    }

    if (!formData.serviceType || !formData.title || !formData.description) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch('/api/services/requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          customerId: session.user.id,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to submit service request')
      }

      const result = await response.json()

      toast({
        title: "Service Request Submitted",
        description: `Your request #${result.requestNumber} has been submitted successfully. We'll contact you within 24 hours.`,
      })

      // Redirect to service requests page
      router.push('/account/service-requests')

    } catch (error) {
      toast({
        title: "Submission Failed",
        description: "There was an error submitting your request. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!session) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">Login Required</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Please log in to submit a service request
          </p>
          <Button onClick={() => router.push('/auth/signin')}>
            Sign In
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Service Selection */}
      {!serviceType && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Globe className="h-5 w-5 mr-2" />
              Select Service Type
            </CardTitle>
            <CardDescription>
              Choose the service you need assistance with
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {serviceTypes.map((service) => {
                const Icon = service.icon
                return (
                  <button
                    key={service.id}
                    onClick={() => handleServiceSelect(service.id)}
                    className={`p-4 border rounded-lg text-left transition-all hover:shadow-md ${
                      selectedService === service.id
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="p-2 bg-primary-100 dark:bg-primary-900 rounded-lg">
                        <Icon className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium text-sm">{service.name}</h3>
                        <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                          {service.description}
                        </p>
                        <div className="flex items-center justify-between mt-2">
                          <Badge variant="outline" className="text-xs">
                            {service.estimatedDays} days
                          </Badge>
                          <span className="text-xs font-medium text-primary-600">
                            From ${service.basePrice}
                          </span>
                        </div>
                      </div>
                    </div>
                  </button>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Service Request Form */}
      {selectedService && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              {selectedServiceInfo && <selectedServiceInfo.icon className="h-5 w-5 mr-2" />}
              Service Request Details
            </CardTitle>
            <CardDescription>
              Provide detailed information about your {selectedServiceInfo?.name.toLowerCase()} requirements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Service Summary */}
              {selectedServiceInfo && (
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">{selectedServiceInfo.name}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {selectedServiceInfo.description}
                      </p>
                    </div>
                    <div className="text-right">
                      <Badge>{selectedServiceInfo.estimatedDays} days</Badge>
                      <p className="text-sm font-medium mt-1">
                        Starting from ${selectedServiceInfo.basePrice}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="title">Request Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Brief description of your request"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="priority">Priority</Label>
                  <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="LOW">Low</SelectItem>
                      <SelectItem value="MEDIUM">Medium</SelectItem>
                      <SelectItem value="HIGH">High</SelectItem>
                      <SelectItem value="URGENT">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Detailed Description */}
              <div>
                <Label htmlFor="description">Detailed Description *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Provide detailed information about your requirements, specifications, quantities, timeline, etc."
                  rows={6}
                  required
                />
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="customerName">Full Name *</Label>
                  <Input
                    id="customerName"
                    value={formData.customerName}
                    onChange={(e) => handleInputChange('customerName', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="customerCompany">Company Name</Label>
                  <Input
                    id="customerCompany"
                    value={formData.customerCompany}
                    onChange={(e) => handleInputChange('customerCompany', e.target.value)}
                    placeholder="Your company or business name"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="customerEmail">Email Address *</Label>
                  <Input
                    id="customerEmail"
                    type="email"
                    value={formData.customerEmail}
                    onChange={(e) => handleInputChange('customerEmail', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="customerPhone">Phone Number</Label>
                  <Input
                    id="customerPhone"
                    value={formData.customerPhone}
                    onChange={(e) => handleInputChange('customerPhone', e.target.value)}
                    placeholder="+****************"
                  />
                </div>
              </div>

              {/* Additional Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="urgentDeadline">Required Completion Date</Label>
                  <Input
                    id="urgentDeadline"
                    type="date"
                    value={formData.urgentDeadline}
                    onChange={(e) => handleInputChange('urgentDeadline', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="budget">Budget Range (USD)</Label>
                  <Input
                    id="budget"
                    value={formData.budget}
                    onChange={(e) => handleInputChange('budget', e.target.value)}
                    placeholder="e.g., $500 - $1000"
                  />
                </div>
              </div>

              {/* Additional Notes */}
              <div>
                <Label htmlFor="additionalNotes">Additional Notes</Label>
                <Textarea
                  id="additionalNotes"
                  value={formData.additionalNotes}
                  onChange={(e) => handleInputChange('additionalNotes', e.target.value)}
                  placeholder="Any additional information, special requirements, or questions"
                  rows={3}
                />
              </div>

              {/* Submit Button */}
              <div className="flex items-center justify-between pt-6 border-t">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  <p>* Required fields</p>
                  <p>We'll respond within 24 hours</p>
                </div>
                <div className="flex space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => router.back()}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="min-w-[120px]"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Submitting...
                      </>
                    ) : (
                      <>
                        <MessageCircle className="h-4 w-4 mr-2" />
                        Submit Request
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle>Need Help?</CardTitle>
          <CardDescription>
            Our team is here to assist you with your service requirements
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <Phone className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="font-medium">Phone Support</p>
                <p className="text-sm text-gray-600">+86-20-1234-5678</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <MessageCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="font-medium">WhatsApp</p>
                <p className="text-sm text-gray-600">+86-138-1234-5678</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <Mail className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="font-medium">Email Support</p>
                <p className="text-sm text-gray-600"><EMAIL></p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
