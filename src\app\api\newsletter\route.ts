import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const subscribeSchema = z.object({
  email: z.string().email('Invalid email address'),
  name: z.string().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = subscribeSchema.parse(body)
    const { email, name } = validatedData

    // Check if email is already subscribed
    const existingSubscriber = await prisma.newsletterSubscriber.findUnique({
      where: { email }
    })

    if (existingSubscriber) {
      if (existingSubscriber.status === 'active') {
        return NextResponse.json(
          { message: 'Email is already subscribed to our newsletter' },
          { status: 400 }
        )
      } else {
        // Reactivate subscription
        await prisma.newsletterSubscriber.update({
          where: { email },
          data: {
            status: 'active',
            subscribedAt: new Date(),
            unsubscribedAt: null
          }
        })

        return NextResponse.json(
          { message: 'Successfully resubscribed to newsletter' },
          { status: 200 }
        )
      }
    }

    // Create new subscription
    const subscriber = await prisma.newsletterSubscriber.create({
      data: {
        email,
        name,
        status: 'active'
      }
    })

    // TODO: Send welcome email
    // await sendWelcomeEmail(subscriber.email, subscriber.name)

    return NextResponse.json(
      { 
        message: 'Successfully subscribed to newsletter',
        subscriber: {
          email: subscriber.email,
          name: subscriber.name
        }
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Newsletter subscription error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          message: 'Validation error',
          errors: error.errors
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: 'Failed to subscribe to newsletter' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')
    const token = searchParams.get('token')

    if (!email) {
      return NextResponse.json(
        { message: 'Email is required' },
        { status: 400 }
      )
    }

    // Find subscriber
    const subscriber = await prisma.newsletterSubscriber.findUnique({
      where: { email }
    })

    if (!subscriber) {
      return NextResponse.json(
        { message: 'Email not found in our newsletter list' },
        { status: 404 }
      )
    }

    if (subscriber.status === 'unsubscribed') {
      return NextResponse.json(
        { message: 'Email is already unsubscribed' },
        { status: 400 }
      )
    }

    // TODO: Verify unsubscribe token for security
    // if (token && !verifyUnsubscribeToken(email, token)) {
    //   return NextResponse.json(
    //     { message: 'Invalid unsubscribe token' },
    //     { status: 400 }
    //   )
    // }

    // Update subscription status
    await prisma.newsletterSubscriber.update({
      where: { email },
      data: {
        status: 'unsubscribed',
        unsubscribedAt: new Date()
      }
    })

    return NextResponse.json(
      { message: 'Successfully unsubscribed from newsletter' },
      { status: 200 }
    )
  } catch (error) {
    console.error('Newsletter unsubscribe error:', error)
    return NextResponse.json(
      { message: 'Failed to unsubscribe from newsletter' },
      { status: 500 }
    )
  }
}

// Get newsletter statistics (admin only)
export async function GET(request: NextRequest) {
  try {
    // TODO: Add admin authentication check
    
    const [totalSubscribers, activeSubscribers, recentSubscribers] = await Promise.all([
      prisma.newsletterSubscriber.count(),
      prisma.newsletterSubscriber.count({
        where: { status: 'active' }
      }),
      prisma.newsletterSubscriber.count({
        where: {
          status: 'active',
          subscribedAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        }
      })
    ])

    return NextResponse.json({
      totalSubscribers,
      activeSubscribers,
      recentSubscribers,
      unsubscribedCount: totalSubscribers - activeSubscribers
    })
  } catch (error) {
    console.error('Error fetching newsletter statistics:', error)
    return NextResponse.json(
      { message: 'Failed to fetch newsletter statistics' },
      { status: 500 }
    )
  }
}
