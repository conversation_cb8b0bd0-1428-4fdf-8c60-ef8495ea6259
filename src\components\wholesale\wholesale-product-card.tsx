"use client"

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { useCartStore } from '@/store/cart-store'
import { useWishlistStore } from '@/store/wishlist-store'
import { useToast } from '@/hooks/use-toast'
import { formatPrice } from '@/lib/utils'
import {
  Star,
  Heart,
  ShoppingCart,
  Package,
  Truck,
  MapPin,
  Users,
  Clock,
  AlertCircle,
  MessageCircle,
  Eye,
  TrendingDown,
} from 'lucide-react'

interface WholesaleProduct {
  id: string
  name: string
  nameAr?: string
  slug: string
  description: string
  retailPrice?: number
  wholesalePrice?: number
  minimumOrder: number
  stockQuantity: number
  productType: string
  isAffiliate: boolean
  affiliateUrl?: string
  isLiquidation: boolean
  liquidationPrice?: number
  liquidationQuantity?: number
  images?: string[]
  category?: {
    name: string
    nameAr?: string
  }
  supplier?: {
    name: string
    city: string
    rating: number
    verified: boolean
  }
  averageRating?: number
  reviewCount?: number
  brand?: string
  origin: string
  weight?: number
  leadTime?: number
}

interface WholesaleProductCardProps {
  product: WholesaleProduct
  compact?: boolean
  showSupplier?: boolean
  language?: 'en' | 'ar'
}

export function WholesaleProductCard({ 
  product, 
  compact = false, 
  showSupplier = true,
  language = 'en' 
}: WholesaleProductCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const { addItem } = useCartStore()
  const { toggleItem: toggleWishlistItem, isInWishlist } = useWishlistStore()
  const { toast } = useToast()

  const isInWishlistState = isInWishlist(product.id)
  const productName = language === 'ar' && product.nameAr ? product.nameAr : product.name
  const categoryName = language === 'ar' && product.category?.nameAr ? product.category.nameAr : product.category?.name

  // Calculate pricing tiers
  const getPricingTiers = () => {
    const tiers = []
    
    if (product.retailPrice && product.minimumOrder === 1) {
      tiers.push({
        quantity: '1+',
        price: product.retailPrice,
        label: 'Retail Price',
        labelAr: 'سعر التجزئة'
      })
    }
    
    if (product.wholesalePrice) {
      tiers.push({
        quantity: `${product.minimumOrder}+`,
        price: product.wholesalePrice,
        label: 'Wholesale Price',
        labelAr: 'سعر الجملة'
      })
    }
    
    if (product.isLiquidation && product.liquidationPrice) {
      tiers.push({
        quantity: `${product.liquidationQuantity || 'All'}`,
        price: product.liquidationPrice,
        label: 'Liquidation Price',
        labelAr: 'سعر التصفية'
      })
    }
    
    return tiers
  }

  const pricingTiers = getPricingTiers()
  const mainPrice = product.isLiquidation && product.liquidationPrice 
    ? product.liquidationPrice 
    : product.wholesalePrice || product.retailPrice || 0

  const handleAddToCart = () => {
    addItem({
      id: product.id,
      productId: product.id,
      name: product.name,
      slug: product.slug,
      price: mainPrice,
      image: product.images?.[0] || '/placeholder-product.jpg',
      inStock: product.stockQuantity > 0,
      maxQuantity: product.stockQuantity,
      quantity: product.minimumOrder,
    })

    toast({
      title: language === 'ar' ? 'تم إضافة المنتج' : 'Added to Cart',
      description: language === 'ar' 
        ? `تم إضافة ${productName} إلى سلة التسوق`
        : `${productName} has been added to your cart`,
    })
  }

  const handleAddToWishlist = () => {
    const wasAdded = toggleWishlistItem({
      productId: product.id,
      name: product.name,
      slug: product.slug,
      price: mainPrice,
      comparePrice: product.retailPrice,
      image: product.images?.[0] || '/placeholder-product.jpg',
      inStock: product.stockQuantity > 0,
      category: categoryName || '',
      rating: product.averageRating || 0,
    })

    toast({
      title: wasAdded 
        ? (language === 'ar' ? 'تم إضافة إلى المفضلة' : 'Added to Wishlist')
        : (language === 'ar' ? 'تم إزالة من المفضلة' : 'Removed from Wishlist'),
      description: wasAdded
        ? (language === 'ar' ? `تم إضافة ${productName} إلى قائمة المفضلة` : `${productName} has been added to your wishlist`)
        : (language === 'ar' ? `تم إزالة ${productName} من قائمة المفضلة` : `${productName} has been removed from your wishlist`),
    })
  }

  const handleInquiry = () => {
    // Navigate to inquiry form
    window.open(`/inquiry?product=${product.id}`, '_blank')
  }

  return (
    <Card 
      className={`group relative overflow-hidden transition-all duration-300 hover:shadow-lg ${
        compact ? 'h-auto' : 'h-full'
      }`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Product Badges */}
      <div className="absolute top-2 left-2 z-10 flex flex-col space-y-1">
        {product.isLiquidation && (
          <Badge className="bg-red-500 text-white">
            <TrendingDown className="h-3 w-3 mr-1" />
            {language === 'ar' ? 'تصفية' : 'Liquidation'}
          </Badge>
        )}
        {product.isAffiliate && (
          <Badge variant="secondary">
            {language === 'ar' ? 'تسويق بالعمولة' : 'Affiliate'}
          </Badge>
        )}
        {product.productType === 'machinery' && (
          <Badge className="bg-blue-500 text-white">
            {language === 'ar' ? 'آلات' : 'Machinery'}
          </Badge>
        )}
        {product.supplier?.verified && (
          <Badge className="bg-green-500 text-white">
            {language === 'ar' ? 'موثق' : 'Verified'}
          </Badge>
        )}
      </div>

      {/* Wishlist Button */}
      <button
        onClick={handleAddToWishlist}
        className="absolute top-2 right-2 z-10 p-2 bg-white dark:bg-gray-800 rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity"
      >
        <Heart 
          className={`h-4 w-4 ${
            isInWishlistState 
              ? 'text-red-500 fill-current' 
              : 'text-gray-600 dark:text-gray-400'
          }`} 
        />
      </button>

      <CardContent className="p-0">
        {/* Product Image */}
        <div className="relative aspect-square overflow-hidden">
          <Link href={`/product/${product.slug}`}>
            <Image
              src={product.images?.[0] || '/placeholder-product.jpg'}
              alt={productName}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
          </Link>
          
          {/* Quick Actions Overlay */}
          <div className={`absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center space-x-2 transition-opacity duration-300 ${
            isHovered ? 'opacity-100' : 'opacity-0'
          }`}>
            <Button size="sm" variant="secondary" asChild>
              <Link href={`/product/${product.slug}`}>
                <Eye className="h-4 w-4 mr-1" />
                {language === 'ar' ? 'عرض' : 'View'}
              </Link>
            </Button>
            <Button size="sm" onClick={handleInquiry}>
              <MessageCircle className="h-4 w-4 mr-1" />
              {language === 'ar' ? 'استفسار' : 'Inquiry'}
            </Button>
          </div>
        </div>

        {/* Product Information */}
        <div className="p-4 space-y-3">
          {/* Category & Brand */}
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>{categoryName}</span>
            {product.brand && <span>{product.brand}</span>}
          </div>

          {/* Product Name */}
          <Link href={`/product/${product.slug}`}>
            <h3 className={`font-semibold line-clamp-2 hover:text-primary-600 transition-colors ${
              compact ? 'text-sm' : 'text-base'
            }`}>
              {productName}
            </h3>
          </Link>

          {/* Rating & Reviews */}
          {product.averageRating && product.reviewCount ? (
            <div className="flex items-center space-x-2">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-3 w-3 ${
                      i < Math.floor(product.averageRating || 0)
                        ? "text-yellow-400 fill-current"
                        : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
              <span className="text-xs text-gray-500">
                ({product.reviewCount})
              </span>
            </div>
          ) : null}

          {/* Pricing Tiers */}
          <div className="space-y-2">
            {pricingTiers.map((tier, index) => (
              <div key={index} className="flex items-center justify-between text-sm">
                <span className="text-gray-600">
                  {tier.quantity} {language === 'ar' ? 'قطعة' : 'pcs'}
                </span>
                <span className="font-semibold text-primary-600">
                  {formatPrice(tier.price)}
                </span>
              </div>
            ))}
          </div>

          {/* MOQ & Stock */}
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center space-x-1">
              <Package className="h-3 w-3" />
              <span>
                {language === 'ar' ? 'الحد الأدنى:' : 'MOQ:'} {product.minimumOrder}
              </span>
            </div>
            <div className="flex items-center space-x-1">
              <span className={product.stockQuantity > 0 ? 'text-green-600' : 'text-red-600'}>
                {product.stockQuantity > 0 
                  ? (language === 'ar' ? 'متوفر' : 'In Stock')
                  : (language === 'ar' ? 'غير متوفر' : 'Out of Stock')
                }
              </span>
            </div>
          </div>

          {/* Supplier Information */}
          {showSupplier && product.supplier && (
            <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t">
              <div className="flex items-center space-x-1">
                <MapPin className="h-3 w-3" />
                <span>{product.supplier.name}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Star className="h-3 w-3 text-yellow-400 fill-current" />
                <span>{product.supplier.rating}</span>
              </div>
            </div>
          )}

          {/* Lead Time */}
          {product.leadTime && (
            <div className="flex items-center space-x-1 text-xs text-gray-500">
              <Clock className="h-3 w-3" />
              <span>
                {language === 'ar' ? 'وقت التسليم:' : 'Lead time:'} {product.leadTime} {language === 'ar' ? 'يوم' : 'days'}
              </span>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-2 pt-2">
            <Button
              onClick={handleAddToCart}
              disabled={product.stockQuantity === 0}
              className="flex-1"
              size={compact ? "sm" : "default"}
            >
              <ShoppingCart className="h-4 w-4 mr-1" />
              {language === 'ar' ? 'أضف للسلة' : 'Add to Cart'}
            </Button>
            <Button
              onClick={handleInquiry}
              variant="outline"
              size={compact ? "sm" : "default"}
            >
              <MessageCircle className="h-4 w-4" />
            </Button>
          </div>

          {/* Affiliate Link */}
          {product.isAffiliate && product.affiliateUrl && (
            <Button
              asChild
              variant="link"
              size="sm"
              className="w-full text-xs"
            >
              <a href={product.affiliateUrl} target="_blank" rel="noopener noreferrer">
                {language === 'ar' ? 'عرض في الموقع الأصلي' : 'View on Original Site'}
              </a>
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
