import { useSession, signIn, signOut } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useToast } from '@/hooks/use-toast'

export function useAuth() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { toast } = useToast()

  const login = async (email: string, password: string) => {
    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      })

      if (result?.error) {
        toast({
          title: 'Login Failed',
          description: 'Invalid email or password',
          variant: 'destructive',
        })
        return false
      }

      toast({
        title: 'Welcome back!',
        description: 'Successfully logged in',
      })
      
      return true
    } catch (error) {
      toast({
        title: 'Login Error',
        description: 'An error occurred during login',
        variant: 'destructive',
      })
      return false
    }
  }

  const register = async (name: string, email: string, password: string) => {
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, email, password }),
      })

      if (!response.ok) {
        const error = await response.json()
        toast({
          title: 'Registration Failed',
          description: error.message || 'Failed to create account',
          variant: 'destructive',
        })
        return false
      }

      // Auto-login after successful registration
      const loginResult = await login(email, password)
      
      if (loginResult) {
        toast({
          title: 'Welcome to AIDEVCOMMERCE!',
          description: 'Account created successfully',
        })
      }
      
      return loginResult
    } catch (error) {
      toast({
        title: 'Registration Error',
        description: 'An error occurred during registration',
        variant: 'destructive',
      })
      return false
    }
  }

  const logout = async () => {
    try {
      await signOut({ redirect: false })
      toast({
        title: 'Logged out',
        description: 'You have been successfully logged out',
      })
      router.push('/')
    } catch (error) {
      toast({
        title: 'Logout Error',
        description: 'An error occurred during logout',
        variant: 'destructive',
      })
    }
  }

  const loginWithProvider = async (provider: 'google' | 'facebook' | 'apple') => {
    try {
      await signIn(provider, { callbackUrl: '/' })
    } catch (error) {
      toast({
        title: 'Login Error',
        description: `Failed to login with ${provider}`,
        variant: 'destructive',
      })
    }
  }

  return {
    user: session?.user || null,
    isLoading: status === 'loading',
    isAuthenticated: !!session,
    login,
    register,
    logout,
    loginWithProvider,
  }
}
