"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { useLanguage } from "@/components/providers/language-provider"
import {
  Handshake,
  Award,
  Globe,
  TrendingUp,
  Star,
  Users,
  Building,
  Zap,
  Shield,
  ArrowRight,
  ChevronLeft,
  ChevronRight,
} from "lucide-react"

interface Partner {
  id: string
  name: string
  logo: string
  category: string
  description: string
  partnership: string
  since: string
  website: string
  featured: boolean
}

const partners: Partner[] = [
  {
    id: "microsoft",
    name: "Microsoft",
    logo: "🏢",
    category: "Technology",
    description: "Strategic cloud and AI partnership",
    partnership: "Gold Partner",
    since: "2020",
    website: "microsoft.com",
    featured: true
  },
  {
    id: "aws",
    name: "Amazon Web Services",
    logo: "☁️",
    category: "Cloud Infrastructure",
    description: "Advanced cloud solutions provider",
    partnership: "Premier Partner",
    since: "2019",
    website: "aws.amazon.com",
    featured: true
  },
  {
    id: "google",
    name: "Google Cloud",
    logo: "🌐",
    category: "Cloud & AI",
    description: "Machine learning and analytics",
    partnership: "Premier Partner",
    since: "2021",
    website: "cloud.google.com",
    featured: true
  },
  {
    id: "salesforce",
    name: "Salesforce",
    logo: "⚡",
    category: "CRM",
    description: "Customer relationship management",
    partnership: "ISV Partner",
    since: "2020",
    website: "salesforce.com",
    featured: false
  },
  {
    id: "stripe",
    name: "Stripe",
    logo: "💳",
    category: "Payments",
    description: "Payment processing solutions",
    partnership: "Verified Partner",
    since: "2018",
    website: "stripe.com",
    featured: false
  },
  {
    id: "shopify",
    name: "Shopify",
    logo: "🛍️",
    category: "E-commerce",
    description: "E-commerce platform integration",
    partnership: "Plus Partner",
    since: "2019",
    website: "shopify.com",
    featured: false
  },
  {
    id: "hubspot",
    name: "HubSpot",
    logo: "🎯",
    category: "Marketing",
    description: "Marketing automation platform",
    partnership: "Solutions Partner",
    since: "2021",
    website: "hubspot.com",
    featured: false
  },
  {
    id: "zendesk",
    name: "Zendesk",
    logo: "🎧",
    category: "Customer Support",
    description: "Customer service platform",
    partnership: "Technology Partner",
    since: "2020",
    website: "zendesk.com",
    featured: false
  },
  {
    id: "slack",
    name: "Slack",
    logo: "💬",
    category: "Communication",
    description: "Team collaboration tools",
    partnership: "App Directory",
    since: "2021",
    website: "slack.com",
    featured: false
  },
  {
    id: "adobe",
    name: "Adobe",
    logo: "🎨",
    category: "Creative",
    description: "Creative and marketing solutions",
    partnership: "Solution Partner",
    since: "2022",
    website: "adobe.com",
    featured: false
  }
]

const categories = [
  "All Partners",
  "Technology",
  "Cloud Infrastructure",
  "Cloud & AI",
  "CRM",
  "Payments",
  "E-commerce",
  "Marketing",
  "Customer Support",
  "Communication",
  "Creative"
]

export function PartnersSection() {
  const [selectedCategory, setSelectedCategory] = useState("All Partners")
  const [scrollPosition, setScrollPosition] = useState(0)
  const [isAutoScrolling, setIsAutoScrolling] = useState(true)
  const { t } = useLanguage()

  const filteredPartners = selectedCategory === "All Partners" 
    ? partners 
    : partners.filter(p => p.category === selectedCategory)

  const featuredPartners = partners.filter(p => p.featured)

  // Auto-scroll functionality
  useEffect(() => {
    if (!isAutoScrolling) return

    const interval = setInterval(() => {
      setScrollPosition(prev => {
        const maxScroll = (filteredPartners.length - 1) * 200
        return prev >= maxScroll ? 0 : prev + 200
      })
    }, 3000)

    return () => clearInterval(interval)
  }, [isAutoScrolling, filteredPartners.length])

  const scrollLeft = () => {
    setScrollPosition(prev => Math.max(0, prev - 200))
    setIsAutoScrolling(false)
  }

  const scrollRight = () => {
    const maxScroll = (filteredPartners.length - 1) * 200
    setScrollPosition(prev => Math.min(maxScroll, prev + 200))
    setIsAutoScrolling(false)
  }

  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50 dark:from-slate-900 dark:via-gray-900 dark:to-zinc-900">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge className="mb-4 px-4 py-2 bg-gradient-to-r from-slate-600 to-gray-600 text-white">
            <Handshake className="h-4 w-4 mr-2" />
            Trusted Partnerships
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-slate-700 to-gray-700 bg-clip-text text-transparent">
              Powered by
            </span>
            <br />
            <span className="text-gray-900 dark:text-white">Industry Leaders</span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed">
            We partner with the world's leading technology companies to deliver 
            exceptional solutions and ensure the highest standards of service and innovation.
          </p>
        </div>

        {/* Featured Partners */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white text-center mb-8">
            Strategic Partners
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {featuredPartners.map((partner) => (
              <Card key={partner.id} className="overflow-hidden hover:shadow-xl transition-all duration-300 group">
                <CardContent className="p-8 text-center">
                  {/* Logo */}
                  <div className="w-20 h-20 bg-gradient-to-br from-slate-100 to-gray-100 dark:from-slate-800 dark:to-gray-800 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-4xl">{partner.logo}</span>
                  </div>

                  {/* Partner Info */}
                  <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                    {partner.name}
                  </h4>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                    {partner.description}
                  </p>

                  {/* Partnership Badge */}
                  <Badge className="bg-gradient-to-r from-blue-500 to-purple-500 text-white mb-4">
                    {partner.partnership}
                  </Badge>

                  {/* Meta Info */}
                  <div className="flex items-center justify-center space-x-4 text-xs text-gray-500">
                    <span>Since {partner.since}</span>
                    <div className="w-px h-3 bg-gray-300" />
                    <span>{partner.category}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          {categories.slice(0, 6).map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => {
                setSelectedCategory(category)
                setScrollPosition(0)
                setIsAutoScrolling(true)
              }}
              className={selectedCategory === category ? "bg-gradient-to-r from-slate-600 to-gray-600" : ""}
            >
              {category}
            </Button>
          ))}
        </div>

        {/* Horizontal Scrolling Partners */}
        <div className="relative mb-16">
          {/* Scroll Controls */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
              {selectedCategory === "All Partners" ? "All Partners" : selectedCategory}
            </h3>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={scrollLeft}
                className="w-10 h-10 rounded-full p-0"
                disabled={scrollPosition === 0}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={scrollRight}
                className="w-10 h-10 rounded-full p-0"
                disabled={scrollPosition >= (filteredPartners.length - 1) * 200}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Scrolling Container */}
          <div className="overflow-hidden">
            <div 
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${scrollPosition}px)` }}
            >
              {filteredPartners.map((partner) => (
                <div key={partner.id} className="flex-shrink-0 w-48 mr-6">
                  <Card className="h-full hover:shadow-lg transition-all duration-300 group cursor-pointer">
                    <CardContent className="p-6 text-center">
                      {/* Logo */}
                      <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-slate-100 dark:from-gray-800 dark:to-slate-800 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <span className="text-2xl">{partner.logo}</span>
                      </div>

                      {/* Name */}
                      <h4 className="font-semibold text-gray-900 dark:text-white text-sm mb-2">
                        {partner.name}
                      </h4>

                      {/* Category */}
                      <Badge variant="outline" className="text-xs mb-3">
                        {partner.category}
                      </Badge>

                      {/* Partnership */}
                      <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                        {partner.partnership}
                      </p>

                      {/* Since */}
                      <p className="text-xs text-gray-500">
                        Since {partner.since}
                      </p>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          </div>

          {/* Scroll Indicator */}
          <div className="flex justify-center mt-4 space-x-1">
            {Array.from({ length: Math.ceil(filteredPartners.length / 4) }).map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  Math.floor(scrollPosition / 800) === index
                    ? "bg-slate-600 w-6"
                    : "bg-gray-300 dark:bg-gray-600"
                }`}
              />
            ))}
          </div>
        </div>

        {/* Partnership Benefits */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-16">
          <div className="text-center">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mx-auto mb-4">
              <Globe className="h-6 w-6 text-white" />
            </div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Global Reach</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Worldwide coverage through our partner network
            </p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mx-auto mb-4">
              <Shield className="h-6 w-6 text-white" />
            </div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Enterprise Security</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Bank-level security through trusted partners
            </p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center mx-auto mb-4">
              <Zap className="h-6 w-6 text-white" />
            </div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Innovation</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Cutting-edge technology and solutions
            </p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mx-auto mb-4">
              <Award className="h-6 w-6 text-white" />
            </div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Excellence</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Award-winning solutions and support
            </p>
          </div>
        </div>

        {/* Partnership Stats */}
        <div className="bg-gradient-to-r from-slate-100 to-gray-100 dark:from-slate-800 dark:to-gray-800 rounded-2xl p-8 mb-16">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
            <div>
              <div className="text-3xl font-bold text-slate-700 dark:text-slate-300 mb-2">50+</div>
              <div className="text-gray-600 dark:text-gray-400">Technology Partners</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-slate-700 dark:text-slate-300 mb-2">15+</div>
              <div className="text-gray-600 dark:text-gray-400">Strategic Alliances</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-slate-700 dark:text-slate-300 mb-2">99.9%</div>
              <div className="text-gray-600 dark:text-gray-400">Service Reliability</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-slate-700 dark:text-slate-300 mb-2">24/7</div>
              <div className="text-gray-600 dark:text-gray-400">Partner Support</div>
            </div>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-slate-600 to-gray-600 text-white rounded-2xl p-8">
            <h3 className="text-2xl font-bold mb-4">
              Interested in Partnering with Us?
            </h3>
            <p className="text-slate-100 mb-6 max-w-2xl mx-auto">
              Join our ecosystem of innovative partners and help us deliver exceptional 
              solutions to customers worldwide. Let's build the future together.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="px-8 py-4 text-lg font-semibold bg-white text-slate-600 hover:bg-gray-100 group">
                Become a Partner
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Button>
              <Button variant="outline" size="lg" className="px-8 py-4 text-lg font-semibold border-white text-white hover:bg-white/10">
                <Building className="mr-2 h-5 w-5" />
                Partner Portal
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
