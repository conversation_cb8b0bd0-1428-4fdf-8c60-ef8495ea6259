import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const contactSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name must be less than 100 characters'),
  email: z.string().email('Invalid email address'),
  phone: z.string().optional(),
  subject: z.string().min(5, 'Subject must be at least 5 characters').max(200, 'Subject must be less than 200 characters'),
  message: z.string().min(10, 'Message must be at least 10 characters').max(2000, 'Message must be less than 2000 characters'),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = contactSchema.parse(body)
    const { name, email, phone, subject, message } = validatedData

    // Create contact submission
    const submission = await prisma.contactSubmission.create({
      data: {
        name,
        email,
        phone,
        subject,
        message,
        status: 'new'
      }
    })

    // TODO: Send notification email to admin
    // await sendContactNotificationEmail(submission)

    // TODO: Send confirmation email to user
    // await sendContactConfirmationEmail(email, name)

    return NextResponse.json(
      { 
        message: 'Thank you for your message. We will get back to you soon!',
        submissionId: submission.id
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Contact form submission error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          message: 'Validation error',
          errors: error.errors
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: 'Failed to submit contact form. Please try again.' },
      { status: 500 }
    )
  }
}

// Get contact submissions (admin only)
export async function GET(request: NextRequest) {
  try {
    // TODO: Add admin authentication check
    
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const search = searchParams.get('search')

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    if (status) {
      where.status = status
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { subject: { contains: search, mode: 'insensitive' } },
        { message: { contains: search, mode: 'insensitive' } }
      ]
    }

    const [submissions, total] = await Promise.all([
      prisma.contactSubmission.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.contactSubmission.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      submissions,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })
  } catch (error) {
    console.error('Error fetching contact submissions:', error)
    return NextResponse.json(
      { message: 'Failed to fetch contact submissions' },
      { status: 500 }
    )
  }
}

// Update contact submission status (admin only)
export async function PUT(request: NextRequest) {
  try {
    // TODO: Add admin authentication check
    
    const body = await request.json()
    const { id, status } = body

    if (!id || !status) {
      return NextResponse.json(
        { message: 'ID and status are required' },
        { status: 400 }
      )
    }

    if (!['new', 'read', 'replied'].includes(status)) {
      return NextResponse.json(
        { message: 'Invalid status. Must be: new, read, or replied' },
        { status: 400 }
      )
    }

    const submission = await prisma.contactSubmission.findUnique({
      where: { id }
    })

    if (!submission) {
      return NextResponse.json(
        { message: 'Contact submission not found' },
        { status: 404 }
      )
    }

    const updatedSubmission = await prisma.contactSubmission.update({
      where: { id },
      data: { status }
    })

    return NextResponse.json({
      message: 'Contact submission status updated successfully',
      submission: updatedSubmission
    })
  } catch (error) {
    console.error('Error updating contact submission:', error)
    return NextResponse.json(
      { message: 'Failed to update contact submission' },
      { status: 500 }
    )
  }
}
