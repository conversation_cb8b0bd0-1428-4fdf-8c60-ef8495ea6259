"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useLanguage } from '@/components/providers/language-provider'
import {
  TrendingUp,
  TrendingDown,
  Users,
  Package,
  DollarSign,
  Globe,
  ShoppingCart,
  Star,
  Clock,
  Truck,
  BarChart3,
  PieChart,
  Activity,
  Target,
  Award,
  MapPin,
  Calendar,
  Filter,
} from 'lucide-react'

interface AnalyticsData {
  overview: {
    totalRevenue: number
    revenueGrowth: number
    totalOrders: number
    ordersGrowth: number
    totalCustomers: number
    customersGrowth: number
    averageOrderValue: number
    aovGrowth: number
  }
  topProducts: Array<{
    id: string
    name: string
    nameAr?: string
    revenue: number
    orders: number
    growth: number
    category: string
  }>
  topSuppliers: Array<{
    id: string
    name: string
    nameAr?: string
    revenue: number
    orders: number
    rating: number
    location: string
  }>
  customerSegments: Array<{
    segment: string
    segmentAr?: string
    count: number
    revenue: number
    percentage: number
  }>
  geographicData: Array<{
    country: string
    countryAr?: string
    orders: number
    revenue: number
    growth: number
  }>
  serviceMetrics: {
    totalRequests: number
    completedRequests: number
    averageResponseTime: number
    customerSatisfaction: number
  }
  trends: {
    dailyRevenue: Array<{ date: string; revenue: number; orders: number }>
    categoryPerformance: Array<{ category: string; categoryAr?: string; revenue: number; growth: number }>
    supplierPerformance: Array<{ supplier: string; rating: number; orders: number; revenue: number }>
  }
}

const mockAnalyticsData: AnalyticsData = {
  overview: {
    totalRevenue: 2450000,
    revenueGrowth: 15.3,
    totalOrders: 1250,
    ordersGrowth: 8.7,
    totalCustomers: 450,
    customersGrowth: 12.1,
    averageOrderValue: 1960,
    aovGrowth: 6.2,
  },
  topProducts: [
    { id: '1', name: 'Industrial LED Lights', nameAr: 'أضواء LED صناعية', revenue: 125000, orders: 85, growth: 23.5, category: 'Electronics' },
    { id: '2', name: 'Cotton Fabric Rolls', nameAr: 'لفائف قماش قطني', revenue: 98000, orders: 120, growth: 18.2, category: 'Textiles' },
    { id: '3', name: 'Plastic Injection Machine', nameAr: 'آلة حقن البلاستيك', revenue: 85000, orders: 12, growth: 45.1, category: 'Machinery' },
  ],
  topSuppliers: [
    { id: '1', name: 'Guangzhou Electronics Co.', nameAr: 'شركة قوانغتشو للإلكترونيات', revenue: 185000, orders: 95, rating: 4.8, location: 'Guangzhou' },
    { id: '2', name: 'Yiwu Trading Hub', nameAr: 'مركز ييوو التجاري', revenue: 142000, orders: 78, rating: 4.6, location: 'Yiwu' },
    { id: '3', name: 'Shenzhen Tech Solutions', nameAr: 'حلول شنتشن التقنية', revenue: 128000, orders: 65, rating: 4.9, location: 'Shenzhen' },
  ],
  customerSegments: [
    { segment: 'Large Importers', segmentAr: 'مستوردون كبار', count: 85, revenue: 1200000, percentage: 49 },
    { segment: 'Medium Distributors', segmentAr: 'موزعون متوسطون', count: 165, revenue: 850000, percentage: 35 },
    { segment: 'Small Retailers', segmentAr: 'تجار تجزئة صغار', count: 200, revenue: 400000, percentage: 16 },
  ],
  geographicData: [
    { country: 'Saudi Arabia', countryAr: 'المملكة العربية السعودية', orders: 385, revenue: 750000, growth: 18.5 },
    { country: 'UAE', countryAr: 'الإمارات العربية المتحدة', orders: 295, revenue: 580000, growth: 22.1 },
    { country: 'Egypt', countryAr: 'مصر', orders: 220, revenue: 420000, growth: 15.3 },
    { country: 'Jordan', countryAr: 'الأردن', orders: 180, revenue: 350000, growth: 12.8 },
    { country: 'Kuwait', countryAr: 'الكويت', orders: 170, revenue: 350000, growth: 25.2 },
  ],
  serviceMetrics: {
    totalRequests: 485,
    completedRequests: 462,
    averageResponseTime: 4.2,
    customerSatisfaction: 4.7,
  },
  trends: {
    dailyRevenue: [],
    categoryPerformance: [
      { category: 'Electronics', categoryAr: 'الإلكترونيات', revenue: 850000, growth: 18.5 },
      { category: 'Textiles', categoryAr: 'المنسوجات', revenue: 620000, growth: 15.2 },
      { category: 'Machinery', categoryAr: 'الآلات', revenue: 480000, growth: 28.7 },
      { category: 'Raw Materials', categoryAr: 'المواد الخام', revenue: 350000, growth: 12.3 },
    ],
    supplierPerformance: [],
  },
}

export function WholesaleAnalyticsDashboard() {
  const { language, t } = useLanguage()
  const isArabic = language === 'ar'
  
  const [data, setData] = useState<AnalyticsData>(mockAnalyticsData)
  const [timeRange, setTimeRange] = useState('30d')
  const [selectedTab, setSelectedTab] = useState('overview')

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`
  }

  const MetricCard = ({ 
    title, 
    titleAr, 
    value, 
    growth, 
    icon: Icon, 
    format = 'number' 
  }: {
    title: string
    titleAr?: string
    value: number
    growth: number
    icon: any
    format?: 'number' | 'currency' | 'percentage'
  }) => {
    const formatValue = () => {
      switch (format) {
        case 'currency':
          return formatCurrency(value)
        case 'percentage':
          return `${value.toFixed(1)}%`
        default:
          return value.toLocaleString()
      }
    }

    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {isArabic && titleAr ? titleAr : title}
              </p>
              <p className="text-2xl font-bold">{formatValue()}</p>
              <div className="flex items-center mt-1">
                {growth >= 0 ? (
                  <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                )}
                <span className={`text-sm font-medium ${growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatPercentage(growth)}
                </span>
                <span className="text-sm text-gray-500 ml-1">
                  {isArabic ? 'مقارنة بالفترة السابقة' : 'vs last period'}
                </span>
              </div>
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
              <Icon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {isArabic ? 'لوحة تحليلات الجملة' : 'Wholesale Analytics Dashboard'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {isArabic ? 'نظرة شاملة على أداء أعمال الجملة' : 'Comprehensive overview of wholesale business performance'}
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">{isArabic ? '7 أيام' : '7 days'}</SelectItem>
              <SelectItem value="30d">{isArabic ? '30 يوم' : '30 days'}</SelectItem>
              <SelectItem value="90d">{isArabic ? '90 يوم' : '90 days'}</SelectItem>
              <SelectItem value="1y">{isArabic ? 'سنة' : '1 year'}</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            {isArabic ? 'تصفية' : 'Filter'}
          </Button>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Revenue"
          titleAr="إجمالي الإيرادات"
          value={data.overview.totalRevenue}
          growth={data.overview.revenueGrowth}
          icon={DollarSign}
          format="currency"
        />
        <MetricCard
          title="Total Orders"
          titleAr="إجمالي الطلبات"
          value={data.overview.totalOrders}
          growth={data.overview.ordersGrowth}
          icon={ShoppingCart}
        />
        <MetricCard
          title="Total Customers"
          titleAr="إجمالي العملاء"
          value={data.overview.totalCustomers}
          growth={data.overview.customersGrowth}
          icon={Users}
        />
        <MetricCard
          title="Average Order Value"
          titleAr="متوسط قيمة الطلب"
          value={data.overview.averageOrderValue}
          growth={data.overview.aovGrowth}
          icon={Target}
          format="currency"
        />
      </div>

      {/* Detailed Analytics Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">{isArabic ? 'نظرة عامة' : 'Overview'}</TabsTrigger>
          <TabsTrigger value="products">{isArabic ? 'المنتجات' : 'Products'}</TabsTrigger>
          <TabsTrigger value="suppliers">{isArabic ? 'الموردين' : 'Suppliers'}</TabsTrigger>
          <TabsTrigger value="customers">{isArabic ? 'العملاء' : 'Customers'}</TabsTrigger>
          <TabsTrigger value="services">{isArabic ? 'الخدمات' : 'Services'}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Category Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PieChart className="h-5 w-5 mr-2" />
                  {isArabic ? 'أداء الفئات' : 'Category Performance'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.trends.categoryPerformance.map((category, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full bg-blue-${(index + 1) * 100}`}></div>
                        <span className="font-medium">
                          {isArabic && category.categoryAr ? category.categoryAr : category.category}
                        </span>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">{formatCurrency(category.revenue)}</div>
                        <div className={`text-sm ${category.growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {formatPercentage(category.growth)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Geographic Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Globe className="h-5 w-5 mr-2" />
                  {isArabic ? 'التوزيع الجغرافي' : 'Geographic Distribution'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.geographicData.map((country, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        <span className="font-medium">
                          {isArabic && country.countryAr ? country.countryAr : country.country}
                        </span>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">{country.orders} {isArabic ? 'طلب' : 'orders'}</div>
                        <div className="text-sm text-gray-600">{formatCurrency(country.revenue)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="products" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Package className="h-5 w-5 mr-2" />
                {isArabic ? 'أفضل المنتجات أداءً' : 'Top Performing Products'}
              </CardTitle>
              <CardDescription>
                {isArabic ? 'المنتجات الأكثر مبيعاً ونمواً' : 'Best selling and fastest growing products'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.topProducts.map((product, index) => (
                  <div key={product.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="text-2xl font-bold text-gray-400">#{index + 1}</div>
                      <div>
                        <h3 className="font-semibold">
                          {isArabic && product.nameAr ? product.nameAr : product.name}
                        </h3>
                        <p className="text-sm text-gray-600">{product.category}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">{formatCurrency(product.revenue)}</div>
                      <div className="text-sm text-gray-600">{product.orders} {isArabic ? 'طلب' : 'orders'}</div>
                      <Badge variant={product.growth >= 0 ? "default" : "destructive"}>
                        {formatPercentage(product.growth)}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="suppliers" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                {isArabic ? 'أفضل الموردين أداءً' : 'Top Performing Suppliers'}
              </CardTitle>
              <CardDescription>
                {isArabic ? 'الموردين الأكثر مبيعاً وتقييماً' : 'Highest revenue and rated suppliers'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.topSuppliers.map((supplier, index) => (
                  <div key={supplier.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="text-2xl font-bold text-gray-400">#{index + 1}</div>
                      <div>
                        <h3 className="font-semibold">
                          {isArabic && supplier.nameAr ? supplier.nameAr : supplier.name}
                        </h3>
                        <div className="flex items-center space-x-2">
                          <MapPin className="h-3 w-3 text-gray-500" />
                          <span className="text-sm text-gray-600">{supplier.location}</span>
                          <div className="flex items-center space-x-1">
                            <Star className="h-3 w-3 text-yellow-400 fill-current" />
                            <span className="text-sm">{supplier.rating}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">{formatCurrency(supplier.revenue)}</div>
                      <div className="text-sm text-gray-600">{supplier.orders} {isArabic ? 'طلب' : 'orders'}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customers" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                {isArabic ? 'شرائح العملاء' : 'Customer Segments'}
              </CardTitle>
              <CardDescription>
                {isArabic ? 'توزيع العملاء حسب الحجم والإيرادات' : 'Customer distribution by size and revenue'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.customerSegments.map((segment, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-semibold">
                        {isArabic && segment.segmentAr ? segment.segmentAr : segment.segment}
                      </h3>
                      <p className="text-sm text-gray-600">{segment.count} {isArabic ? 'عميل' : 'customers'}</p>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">{formatCurrency(segment.revenue)}</div>
                      <div className="text-sm text-gray-600">{segment.percentage}% {isArabic ? 'من الإيرادات' : 'of revenue'}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="services" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
              title="Service Requests"
              titleAr="طلبات الخدمة"
              value={data.serviceMetrics.totalRequests}
              growth={12.5}
              icon={Activity}
            />
            <MetricCard
              title="Completion Rate"
              titleAr="معدل الإنجاز"
              value={(data.serviceMetrics.completedRequests / data.serviceMetrics.totalRequests) * 100}
              growth={3.2}
              icon={CheckCircle}
              format="percentage"
            />
            <MetricCard
              title="Avg Response Time"
              titleAr="متوسط وقت الاستجابة"
              value={data.serviceMetrics.averageResponseTime}
              growth={-8.5}
              icon={Clock}
            />
            <MetricCard
              title="Customer Satisfaction"
              titleAr="رضا العملاء"
              value={data.serviceMetrics.customerSatisfaction}
              growth={2.1}
              icon={Star}
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
