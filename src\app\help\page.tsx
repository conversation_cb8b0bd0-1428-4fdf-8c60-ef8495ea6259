"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import {
  Search,
  ShoppingCart,
  CreditCard,
  Truck,
  RotateCcw,
  User,
  Settings,
  MessageCircle,
  Phone,
  Mail,
  ChevronRight,
  Star,
  Clock,
  CheckCircle,
  AlertCircle,
  HelpCircle,
  Book,
  Video,
  FileText,
} from "lucide-react"

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const { t } = useLanguage()

  const helpCategories = [
    {
      id: "orders",
      title: "Orders & Checkout",
      description: "Help with placing orders, payment, and checkout process",
      icon: ShoppingCart,
      color: "bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400",
      articles: 24,
      popular: true
    },
    {
      id: "shipping",
      title: "Shipping & Delivery",
      description: "Track orders, shipping options, and delivery information",
      icon: Truck,
      color: "bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400",
      articles: 18,
      popular: true
    },
    {
      id: "returns",
      title: "Returns & Refunds",
      description: "Return policy, refund process, and exchange information",
      icon: RotateCcw,
      color: "bg-orange-100 text-orange-600 dark:bg-orange-900 dark:text-orange-400",
      articles: 15,
      popular: false
    },
    {
      id: "payments",
      title: "Payment & Billing",
      description: "Payment methods, billing issues, and transaction help",
      icon: CreditCard,
      color: "bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-400",
      articles: 12,
      popular: true
    },
    {
      id: "account",
      title: "Account & Profile",
      description: "Account settings, profile management, and security",
      icon: User,
      color: "bg-pink-100 text-pink-600 dark:bg-pink-900 dark:text-pink-400",
      articles: 20,
      popular: false
    },
    {
      id: "technical",
      title: "Technical Support",
      description: "Website issues, app problems, and technical troubleshooting",
      icon: Settings,
      color: "bg-gray-100 text-gray-600 dark:bg-gray-900 dark:text-gray-400",
      articles: 16,
      popular: false
    }
  ]

  const popularArticles = [
    {
      title: "How to track my order?",
      category: "Shipping",
      views: "15.2k",
      helpful: 98,
      href: "/help/shipping/track-order"
    },
    {
      title: "What payment methods do you accept?",
      category: "Payment",
      views: "12.8k",
      helpful: 95,
      href: "/help/payments/payment-methods"
    },
    {
      title: "How to return or exchange an item?",
      category: "Returns",
      views: "11.4k",
      helpful: 92,
      href: "/help/returns/how-to-return"
    },
    {
      title: "How long does shipping take?",
      category: "Shipping",
      views: "9.7k",
      helpful: 89,
      href: "/help/shipping/delivery-times"
    },
    {
      title: "How to change my shipping address?",
      category: "Orders",
      views: "8.3k",
      helpful: 94,
      href: "/help/orders/change-address"
    },
    {
      title: "How to cancel my order?",
      category: "Orders",
      views: "7.9k",
      helpful: 91,
      href: "/help/orders/cancel-order"
    }
  ]

  const quickActions = [
    {
      title: "Track Your Order",
      description: "Enter your order number to get real-time updates",
      icon: Truck,
      href: "/track-order",
      color: "bg-blue-600"
    },
    {
      title: "Start a Return",
      description: "Begin the return process for your recent purchase",
      icon: RotateCcw,
      href: "/returns",
      color: "bg-green-600"
    },
    {
      title: "Contact Support",
      description: "Get help from our customer service team",
      icon: MessageCircle,
      href: "/contact",
      color: "bg-purple-600"
    },
    {
      title: "Live Chat",
      description: "Chat with our AI assistant for instant help",
      icon: MessageCircle,
      href: "#",
      color: "bg-orange-600"
    }
  ]

  const contactOptions = [
    {
      method: "Live Chat",
      description: "Get instant help from our AI assistant",
      availability: "24/7 Available",
      icon: MessageCircle,
      action: "Start Chat"
    },
    {
      method: "Phone Support",
      description: "Speak with our customer service team",
      availability: "Mon-Fri, 9AM-6PM EST",
      icon: Phone,
      action: "Call Now"
    },
    {
      method: "Email Support",
      description: "Send us a detailed message",
      availability: "Response within 4 hours",
      icon: Mail,
      action: "Send Email"
    }
  ]

  const filteredCategories = helpCategories.filter(category =>
    category.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    category.description.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-600 to-primary-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              How can we help you?
            </h1>
            <p className="text-xl text-primary-100 mb-8">
              Find answers to your questions, get support, and learn how to make the most of AIDEVCOMMERCE
            </p>
            
            {/* Search Bar */}
            <div className="relative max-w-2xl mx-auto">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search for help articles, guides, or topics..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-4 text-gray-900 bg-white rounded-lg shadow-lg focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Quick Actions */}
      <section className="py-12 -mt-8 relative z-10">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickActions.map((action) => {
              const Icon = action.icon
              return (
                <Link key={action.title} href={action.href}>
                  <Card className="hover:shadow-lg transition-shadow cursor-pointer h-full">
                    <CardContent className="p-6 text-center">
                      <div className={`w-16 h-16 ${action.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                        <Icon className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        {action.title}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {action.description}
                      </p>
                    </CardContent>
                  </Card>
                </Link>
              )
            })}
          </div>
        </div>
      </section>

      {/* Help Categories */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Browse Help Topics
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Find detailed guides and answers organized by topic
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCategories.map((category) => {
              const Icon = category.icon
              return (
                <Link key={category.id} href={`/help/${category.id}`}>
                  <Card className="hover:shadow-lg transition-shadow cursor-pointer h-full">
                    <CardContent className="p-6">
                      <div className="flex items-start space-x-4">
                        <div className={`w-12 h-12 ${category.color} rounded-lg flex items-center justify-center flex-shrink-0`}>
                          <Icon className="h-6 w-6" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                              {category.title}
                            </h3>
                            {category.popular && (
                              <Badge className="bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 text-xs">
                                Popular
                              </Badge>
                            )}
                          </div>
                          <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">
                            {category.description}
                          </p>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-500">
                              {category.articles} articles
                            </span>
                            <ChevronRight className="h-4 w-4 text-gray-400" />
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              )
            })}
          </div>
        </div>
      </section>

      {/* Popular Articles */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Most Popular Articles
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Quick answers to the most frequently asked questions
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-4">
            {popularArticles.map((article, index) => (
              <Link key={index} href={article.href}>
                <Card className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <span className="text-2xl font-bold text-gray-300 dark:text-gray-600">
                            {(index + 1).toString().padStart(2, '0')}
                          </span>
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            {article.title}
                          </h3>
                          <Badge variant="outline" className="text-xs">
                            {article.category}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-500 ml-8">
                          <span className="flex items-center space-x-1">
                            <Star className="h-3 w-3" />
                            <span>{article.helpful}% helpful</span>
                          </span>
                          <span>{article.views} views</span>
                        </div>
                      </div>
                      <ChevronRight className="h-5 w-5 text-gray-400" />
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Support */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Still Need Help?
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Can't find what you're looking for? Our support team is here to help
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            {contactOptions.map((option) => {
              const Icon = option.icon
              return (
                <Card key={option.method} className="text-center">
                  <CardContent className="p-6">
                    <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Icon className="h-8 w-8 text-primary-600 dark:text-primary-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      {option.method}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">
                      {option.description}
                    </p>
                    <p className="text-xs text-gray-500 mb-4">
                      {option.availability}
                    </p>
                    <Button className="w-full">
                      {option.action}
                    </Button>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Resources */}
      <section className="py-16 bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">
              Additional Resources
            </h2>
            <p className="text-primary-100 max-w-2xl mx-auto">
              Explore more ways to get help and learn about our platform
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <Card className="bg-white/10 border-white/20 text-white">
              <CardContent className="p-6 text-center">
                <Video className="h-12 w-12 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Video Tutorials</h3>
                <p className="text-sm text-primary-100 mb-4">
                  Watch step-by-step guides and tutorials
                </p>
                <Button variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600">
                  Watch Videos
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white/10 border-white/20 text-white">
              <CardContent className="p-6 text-center">
                <Book className="h-12 w-12 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">User Guide</h3>
                <p className="text-sm text-primary-100 mb-4">
                  Comprehensive documentation and guides
                </p>
                <Button variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600">
                  Read Guide
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white/10 border-white/20 text-white">
              <CardContent className="p-6 text-center">
                <FileText className="h-12 w-12 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">API Documentation</h3>
                <p className="text-sm text-primary-100 mb-4">
                  Technical documentation for developers
                </p>
                <Button variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600">
                  View Docs
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  )
}
