import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const type = searchParams.get('type') || 'all' // all, products, services, production-lines, blog
    const limit = parseInt(searchParams.get('limit') || '10')
    const session = await getServerSession(authOptions)

    if (!query || query.trim().length < 2) {
      return NextResponse.json(
        { error: 'Search query must be at least 2 characters' },
        { status: 400 }
      )
    }

    const searchTerm = query.trim()
    const results: any = {}

    // Log search query for analytics
    if (session?.user?.id) {
      await prisma.searchQuery.create({
        data: {
          query: searchTerm,
          userId: session.user.id,
          results: 0 // Will be updated later
        }
      }).catch(console.error) // Don't fail the search if logging fails
    }

    // Search products
    if (type === 'all' || type === 'products') {
      const products = await prisma.product.findMany({
        where: {
          status: 'ACTIVE',
          OR: [
            { name: { contains: searchTerm, mode: 'insensitive' } },
            { description: { contains: searchTerm, mode: 'insensitive' } },
            { shortDescription: { contains: searchTerm, mode: 'insensitive' } }
          ]
        },
        include: {
          category: true,
          images: {
            take: 1,
            orderBy: { position: 'asc' }
          },
          reviews: {
            select: { rating: true }
          }
        },
        take: limit,
        orderBy: [
          { featured: 'desc' },
          { createdAt: 'desc' }
        ]
      })

      // Calculate average ratings
      results.products = products.map(product => {
        const ratings = product.reviews.map(r => r.rating)
        const averageRating = ratings.length > 0 
          ? ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length 
          : 0

        return {
          ...product,
          averageRating: Math.round(averageRating * 10) / 10,
          reviewCount: ratings.length
        }
      })
    }

    // Search services
    if (type === 'all' || type === 'services') {
      results.services = await prisma.service.findMany({
        where: {
          status: 'ACTIVE',
          OR: [
            { name: { contains: searchTerm, mode: 'insensitive' } },
            { description: { contains: searchTerm, mode: 'insensitive' } },
            { shortDescription: { contains: searchTerm, mode: 'insensitive' } }
          ]
        },
        include: {
          category: true
        },
        take: limit,
        orderBy: [
          { featured: 'desc' },
          { createdAt: 'desc' }
        ]
      })
    }

    // Search production lines
    if (type === 'all' || type === 'production-lines') {
      results.productionLines = await prisma.productionLine.findMany({
        where: {
          status: 'ACTIVE',
          OR: [
            { name: { contains: searchTerm, mode: 'insensitive' } },
            { description: { contains: searchTerm, mode: 'insensitive' } },
            { shortDescription: { contains: searchTerm, mode: 'insensitive' } }
          ]
        },
        include: {
          category: true
        },
        take: limit,
        orderBy: [
          { featured: 'desc' },
          { createdAt: 'desc' }
        ]
      })
    }

    // Search blog posts
    if (type === 'all' || type === 'blog') {
      results.blogPosts = await prisma.blogPost.findMany({
        where: {
          status: 'published',
          OR: [
            { title: { contains: searchTerm, mode: 'insensitive' } },
            { excerpt: { contains: searchTerm, mode: 'insensitive' } },
            { content: { contains: searchTerm, mode: 'insensitive' } }
          ]
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              avatar: true
            }
          },
          category: true
        },
        take: limit,
        orderBy: [
          { featured: 'desc' },
          { publishedAt: 'desc' }
        ]
      })
    }

    // Calculate total results
    const totalResults = Object.values(results).reduce((sum: number, items: any) => {
      return sum + (Array.isArray(items) ? items.length : 0)
    }, 0)

    // Update search query with results count
    if (session?.user?.id) {
      await prisma.searchQuery.updateMany({
        where: {
          query: searchTerm,
          userId: session.user.id,
          results: 0
        },
        data: {
          results: totalResults
        }
      }).catch(console.error)
    }

    return NextResponse.json({
      query: searchTerm,
      totalResults,
      results
    })
  } catch (error) {
    console.error('Error performing search:', error)
    return NextResponse.json(
      { error: 'Failed to perform search' },
      { status: 500 }
    )
  }
}

// Get search suggestions
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { query } = body

    if (!query || query.trim().length < 2) {
      return NextResponse.json({ suggestions: [] })
    }

    const searchTerm = query.trim()

    // Get popular search queries
    const popularQueries = await prisma.searchQuery.groupBy({
      by: ['query'],
      where: {
        query: {
          contains: searchTerm,
          mode: 'insensitive'
        }
      },
      _count: {
        query: true
      },
      orderBy: {
        _count: {
          query: 'desc'
        }
      },
      take: 5
    })

    // Get product names that match
    const productSuggestions = await prisma.product.findMany({
      where: {
        status: 'ACTIVE',
        name: {
          contains: searchTerm,
          mode: 'insensitive'
        }
      },
      select: {
        name: true
      },
      take: 5
    })

    const suggestions = [
      ...popularQueries.map(q => q.query),
      ...productSuggestions.map(p => p.name)
    ].slice(0, 8) // Limit to 8 suggestions

    return NextResponse.json({ suggestions })
  } catch (error) {
    console.error('Error getting search suggestions:', error)
    return NextResponse.json({ suggestions: [] })
  }
}
