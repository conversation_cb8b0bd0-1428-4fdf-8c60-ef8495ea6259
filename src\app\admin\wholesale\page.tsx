"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { WholesaleAnalyticsDashboard } from '@/components/analytics/wholesale-analytics-dashboard'
import { useLanguage } from '@/components/providers/language-provider'
import {
  Users,
  Package,
  DollarSign,
  TrendingUp,
  ShoppingCart,
  Star,
  Shield,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Eye,
  Edit,
  Trash2,
  Plus,
  Filter,
  Search,
  Download,
  Upload,
  RefreshCw,
  Globe,
  MapPin,
  Phone,
  Mail,
  MessageCircle,
} from 'lucide-react'

interface DashboardStats {
  totalSuppliers: number
  verifiedSuppliers: number
  totalInquiries: number
  pendingInquiries: number
  totalServiceRequests: number
  activeServiceRequests: number
  totalRevenue: number
  monthlyGrowth: number
}

interface Supplier {
  id: string
  name: string
  nameAr?: string
  contactPerson: string
  email: string
  phone: string
  city: string
  province: string
  rating: number
  verified: boolean
  trustScore: number
  productsCount: number
  ordersCount: number
  revenue: number
  status: 'active' | 'inactive' | 'suspended'
  createdAt: string
}

interface ProductInquiry {
  id: string
  inquiryNumber: string
  customerName: string
  customerEmail: string
  productName: string
  quantity: number
  targetPrice?: number
  status: 'PENDING' | 'QUOTED' | 'NEGOTIATING' | 'ACCEPTED' | 'REJECTED'
  urgency: string
  createdAt: string
  supplierName: string
}

interface ServiceRequest {
  id: string
  requestNumber: string
  customerName: string
  serviceType: string
  title: string
  priority: string
  status: string
  estimatedCost?: number
  createdAt: string
  assignedSupplier?: string
}

const mockStats: DashboardStats = {
  totalSuppliers: 485,
  verifiedSuppliers: 342,
  totalInquiries: 1250,
  pendingInquiries: 85,
  totalServiceRequests: 680,
  activeServiceRequests: 45,
  totalRevenue: 2450000,
  monthlyGrowth: 15.3,
}

const mockSuppliers: Supplier[] = [
  {
    id: '1',
    name: 'Guangzhou Electronics Co.',
    nameAr: 'شركة قوانغتشو للإلكترونيات',
    contactPerson: 'Li Wei',
    email: '<EMAIL>',
    phone: '+86-20-12345678',
    city: 'Guangzhou',
    province: 'Guangdong',
    rating: 4.8,
    verified: true,
    trustScore: 95,
    productsCount: 2500,
    ordersCount: 185,
    revenue: 485000,
    status: 'active',
    createdAt: '2023-01-15',
  },
  {
    id: '2',
    name: 'Yiwu Trading Hub',
    nameAr: 'مركز ييوو التجاري',
    contactPerson: 'Zhang Ming',
    email: '<EMAIL>',
    phone: '+86-579-87654321',
    city: 'Yiwu',
    province: 'Zhejiang',
    rating: 4.6,
    verified: true,
    trustScore: 88,
    productsCount: 5000,
    ordersCount: 142,
    revenue: 325000,
    status: 'active',
    createdAt: '2023-02-20',
  },
]

const mockInquiries: ProductInquiry[] = [
  {
    id: '1',
    inquiryNumber: 'INQ-2024-001',
    customerName: 'Ahmed Hassan',
    customerEmail: '<EMAIL>',
    productName: 'LED Light Strips',
    quantity: 5000,
    targetPrice: 2.50,
    status: 'PENDING',
    urgency: 'high',
    createdAt: '2024-01-15T10:30:00Z',
    supplierName: 'Guangzhou Electronics Co.',
  },
  {
    id: '2',
    inquiryNumber: 'INQ-2024-002',
    customerName: 'Omar Salem',
    customerEmail: '<EMAIL>',
    productName: 'Cotton Fabric',
    quantity: 10000,
    status: 'QUOTED',
    urgency: 'normal',
    createdAt: '2024-01-14T14:20:00Z',
    supplierName: 'Yiwu Trading Hub',
  },
]

const mockServiceRequests: ServiceRequest[] = [
  {
    id: '1',
    requestNumber: 'SR-2024-001',
    customerName: 'Ahmed Hassan',
    serviceType: 'INSPECTION',
    title: 'Product Quality Inspection',
    priority: 'HIGH',
    status: 'IN_PROGRESS',
    estimatedCost: 350,
    createdAt: '2024-01-15T09:00:00Z',
    assignedSupplier: 'Quality Control Services Ltd.',
  },
  {
    id: '2',
    requestNumber: 'SR-2024-002',
    customerName: 'Omar Salem',
    serviceType: 'SHIPPING',
    title: 'International Shipping Quote',
    priority: 'MEDIUM',
    status: 'PENDING',
    estimatedCost: 1200,
    createdAt: '2024-01-14T16:45:00Z',
  },
]

export default function WholesaleAdminPage() {
  const { language, t } = useLanguage()
  const isArabic = language === 'ar'

  const [stats, setStats] = useState<DashboardStats>(mockStats)
  const [suppliers, setSuppliers] = useState<Supplier[]>(mockSuppliers)
  const [inquiries, setInquiries] = useState<ProductInquiry[]>(mockInquiries)
  const [serviceRequests, setServiceRequests] = useState<ServiceRequest[]>(mockServiceRequests)
  const [selectedTab, setSelectedTab] = useState('overview')
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { variant: 'default' as const, label: isArabic ? 'نشط' : 'Active' },
      inactive: { variant: 'secondary' as const, label: isArabic ? 'غير نشط' : 'Inactive' },
      suspended: { variant: 'destructive' as const, label: isArabic ? 'معلق' : 'Suspended' },
      PENDING: { variant: 'secondary' as const, label: isArabic ? 'معلق' : 'Pending' },
      QUOTED: { variant: 'default' as const, label: isArabic ? 'تم التسعير' : 'Quoted' },
      ACCEPTED: { variant: 'default' as const, label: isArabic ? 'مقبول' : 'Accepted' },
      REJECTED: { variant: 'destructive' as const, label: isArabic ? 'مرفوض' : 'Rejected' },
      IN_PROGRESS: { variant: 'default' as const, label: isArabic ? 'قيد التنفيذ' : 'In Progress' },
      COMPLETED: { variant: 'default' as const, label: isArabic ? 'مكتمل' : 'Completed' },
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  const StatCard = ({ 
    title, 
    titleAr, 
    value, 
    growth, 
    icon: Icon, 
    format = 'number' 
  }: {
    title: string
    titleAr?: string
    value: number
    growth?: number
    icon: any
    format?: 'number' | 'currency' | 'percentage'
  }) => {
    const formatValue = () => {
      switch (format) {
        case 'currency':
          return formatCurrency(value)
        case 'percentage':
          return `${value.toFixed(1)}%`
        default:
          return value.toLocaleString()
      }
    }

    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {isArabic && titleAr ? titleAr : title}
              </p>
              <p className="text-2xl font-bold">{formatValue()}</p>
              {growth !== undefined && (
                <div className="flex items-center mt-1">
                  <TrendingUp className={`h-4 w-4 mr-1 ${growth >= 0 ? 'text-green-500' : 'text-red-500'}`} />
                  <span className={`text-sm font-medium ${growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {growth >= 0 ? '+' : ''}{growth.toFixed(1)}%
                  </span>
                </div>
              )}
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
              <Icon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {isArabic ? 'إدارة الجملة' : 'Wholesale Management'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {isArabic ? 'إدارة شاملة لأعمال الجملة والموردين' : 'Comprehensive wholesale business and supplier management'}
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            {isArabic ? 'تصدير' : 'Export'}
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            {isArabic ? 'إضافة مورد' : 'Add Supplier'}
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Suppliers"
          titleAr="إجمالي الموردين"
          value={stats.totalSuppliers}
          icon={Users}
        />
        <StatCard
          title="Verified Suppliers"
          titleAr="موردين موثقين"
          value={stats.verifiedSuppliers}
          icon={Shield}
        />
        <StatCard
          title="Pending Inquiries"
          titleAr="استفسارات معلقة"
          value={stats.pendingInquiries}
          icon={MessageCircle}
        />
        <StatCard
          title="Total Revenue"
          titleAr="إجمالي الإيرادات"
          value={stats.totalRevenue}
          growth={stats.monthlyGrowth}
          icon={DollarSign}
          format="currency"
        />
      </div>

      {/* Management Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">{isArabic ? 'نظرة عامة' : 'Overview'}</TabsTrigger>
          <TabsTrigger value="suppliers">{isArabic ? 'الموردين' : 'Suppliers'}</TabsTrigger>
          <TabsTrigger value="inquiries">{isArabic ? 'الاستفسارات' : 'Inquiries'}</TabsTrigger>
          <TabsTrigger value="services">{isArabic ? 'الخدمات' : 'Services'}</TabsTrigger>
          <TabsTrigger value="analytics">{isArabic ? 'التحليلات' : 'Analytics'}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Inquiries */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageCircle className="h-5 w-5 mr-2" />
                  {isArabic ? 'الاستفسارات الحديثة' : 'Recent Inquiries'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {inquiries.slice(0, 5).map((inquiry) => (
                    <div key={inquiry.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h4 className="font-medium">{inquiry.inquiryNumber}</h4>
                        <p className="text-sm text-gray-600">{inquiry.customerName}</p>
                        <p className="text-xs text-gray-500">{inquiry.productName}</p>
                      </div>
                      <div className="text-right">
                        {getStatusBadge(inquiry.status)}
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(inquiry.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Service Requests */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Package className="h-5 w-5 mr-2" />
                  {isArabic ? 'طلبات الخدمة الحديثة' : 'Recent Service Requests'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {serviceRequests.slice(0, 5).map((request) => (
                    <div key={request.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h4 className="font-medium">{request.requestNumber}</h4>
                        <p className="text-sm text-gray-600">{request.customerName}</p>
                        <p className="text-xs text-gray-500">{request.serviceType}</p>
                      </div>
                      <div className="text-right">
                        {getStatusBadge(request.status)}
                        <p className="text-xs text-gray-500 mt-1">
                          {request.estimatedCost && formatCurrency(request.estimatedCost)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="suppliers" className="space-y-6">
          {/* Suppliers Management */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>{isArabic ? 'إدارة الموردين' : 'Supplier Management'}</CardTitle>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder={isArabic ? 'البحث عن الموردين...' : 'Search suppliers...'}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 w-64"
                    />
                  </div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">{isArabic ? 'الكل' : 'All'}</SelectItem>
                      <SelectItem value="active">{isArabic ? 'نشط' : 'Active'}</SelectItem>
                      <SelectItem value="verified">{isArabic ? 'موثق' : 'Verified'}</SelectItem>
                      <SelectItem value="suspended">{isArabic ? 'معلق' : 'Suspended'}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {suppliers.map((supplier) => (
                  <div key={supplier.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                        <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <h3 className="font-semibold">
                          {isArabic && supplier.nameAr ? supplier.nameAr : supplier.name}
                        </h3>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <MapPin className="h-3 w-3" />
                          <span>{supplier.city}, {supplier.province}</span>
                          <Star className="h-3 w-3 text-yellow-400 fill-current" />
                          <span>{supplier.rating}</span>
                          {supplier.verified && <Shield className="h-3 w-3 text-green-500" />}
                        </div>
                        <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                          <span>{supplier.productsCount} {isArabic ? 'منتج' : 'products'}</span>
                          <span>{supplier.ordersCount} {isArabic ? 'طلب' : 'orders'}</span>
                          <span>{formatCurrency(supplier.revenue)}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(supplier.status)}
                      <Button variant="outline" size="sm">
                        <Eye className="h-3 w-3 mr-1" />
                        {isArabic ? 'عرض' : 'View'}
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="h-3 w-3 mr-1" />
                        {isArabic ? 'تعديل' : 'Edit'}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="inquiries" className="space-y-6">
          {/* Inquiries Management */}
          <Card>
            <CardHeader>
              <CardTitle>{isArabic ? 'إدارة الاستفسارات' : 'Inquiry Management'}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {inquiries.map((inquiry) => (
                  <div key={inquiry.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-semibold">{inquiry.inquiryNumber}</h3>
                      <p className="text-sm text-gray-600">{inquiry.customerName} - {inquiry.productName}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                        <span>{inquiry.quantity} {isArabic ? 'قطعة' : 'pcs'}</span>
                        {inquiry.targetPrice && <span>{isArabic ? 'السعر المستهدف:' : 'Target:'} ${inquiry.targetPrice}</span>}
                        <span>{inquiry.supplierName}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(inquiry.status)}
                      <Badge variant="outline">{inquiry.urgency}</Badge>
                      <Button variant="outline" size="sm">
                        <Eye className="h-3 w-3 mr-1" />
                        {isArabic ? 'عرض' : 'View'}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="services" className="space-y-6">
          {/* Service Requests Management */}
          <Card>
            <CardHeader>
              <CardTitle>{isArabic ? 'إدارة طلبات الخدمة' : 'Service Request Management'}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {serviceRequests.map((request) => (
                  <div key={request.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-semibold">{request.requestNumber}</h3>
                      <p className="text-sm text-gray-600">{request.customerName} - {request.title}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                        <span>{request.serviceType}</span>
                        {request.estimatedCost && <span>{formatCurrency(request.estimatedCost)}</span>}
                        {request.assignedSupplier && <span>{request.assignedSupplier}</span>}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(request.status)}
                      <Badge variant="outline">{request.priority}</Badge>
                      <Button variant="outline" size="sm">
                        <Eye className="h-3 w-3 mr-1" />
                        {isArabic ? 'عرض' : 'View'}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <WholesaleAnalyticsDashboard />
        </TabsContent>
      </Tabs>
    </div>
  )
}
