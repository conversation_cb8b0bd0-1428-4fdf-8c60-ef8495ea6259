"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useToast } from "@/hooks/use-toast"
import { formatPrice, formatDate } from "@/lib/utils"
import {
  Percent,
  Plus,
  Edit,
  Trash2,
  Copy,
  Eye,
  Search,
  Filter,
  Calendar,
  Users,
  ShoppingCart,
  Gift,
  Tag,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
} from "lucide-react"

interface Coupon {
  id: string
  code: string
  name: string
  description: string
  type: 'percentage' | 'fixed_amount' | 'free_shipping' | 'buy_x_get_y'
  value: number
  minimumAmount?: number
  maximumDiscount?: number
  usageLimit?: number
  usageCount: number
  userLimit?: number
  startDate: Date
  endDate: Date
  status: 'active' | 'inactive' | 'expired' | 'scheduled'
  applicableProducts?: string[]
  applicableCategories?: string[]
  excludedProducts?: string[]
  firstTimeOnly: boolean
  stackable: boolean
  createdBy: string
  createdAt: Date
  lastUsed?: Date
}

// Mock coupons data
const mockCoupons: Coupon[] = [
  {
    id: "1",
    code: "SAVE20",
    name: "20% Off Everything",
    description: "Get 20% off on all products",
    type: 'percentage',
    value: 20,
    minimumAmount: 50,
    maximumDiscount: 100,
    usageLimit: 1000,
    usageCount: 234,
    userLimit: 1,
    startDate: new Date("2024-01-01"),
    endDate: new Date("2024-03-31"),
    status: 'active',
    firstTimeOnly: false,
    stackable: false,
    createdBy: "Admin",
    createdAt: new Date("2023-12-15"),
    lastUsed: new Date("2024-01-15")
  },
  {
    id: "2",
    code: "WELCOME10",
    name: "Welcome Discount",
    description: "10% off for new customers",
    type: 'percentage',
    value: 10,
    minimumAmount: 25,
    usageLimit: undefined,
    usageCount: 89,
    userLimit: 1,
    startDate: new Date("2024-01-01"),
    endDate: new Date("2024-12-31"),
    status: 'active',
    firstTimeOnly: true,
    stackable: true,
    createdBy: "Marketing Team",
    createdAt: new Date("2023-12-20"),
    lastUsed: new Date("2024-01-14")
  },
  {
    id: "3",
    code: "FREESHIP",
    name: "Free Shipping",
    description: "Free shipping on orders over $75",
    type: 'free_shipping',
    value: 0,
    minimumAmount: 75,
    usageLimit: 500,
    usageCount: 156,
    startDate: new Date("2024-01-01"),
    endDate: new Date("2024-02-29"),
    status: 'active',
    firstTimeOnly: false,
    stackable: true,
    createdBy: "Admin",
    createdAt: new Date("2023-12-10"),
    lastUsed: new Date("2024-01-13")
  },
  {
    id: "4",
    code: "FLASH50",
    name: "Flash Sale",
    description: "$50 off orders over $200",
    type: 'fixed_amount',
    value: 50,
    minimumAmount: 200,
    usageLimit: 100,
    usageCount: 67,
    userLimit: 1,
    startDate: new Date("2024-01-10"),
    endDate: new Date("2024-01-20"),
    status: 'expired',
    firstTimeOnly: false,
    stackable: false,
    createdBy: "Sales Team",
    createdAt: new Date("2024-01-05"),
    lastUsed: new Date("2024-01-19")
  },
  {
    id: "5",
    code: "SPRING2024",
    name: "Spring Collection",
    description: "25% off spring collection",
    type: 'percentage',
    value: 25,
    minimumAmount: 100,
    maximumDiscount: 150,
    usageLimit: 200,
    usageCount: 0,
    startDate: new Date("2024-03-01"),
    endDate: new Date("2024-05-31"),
    status: 'scheduled',
    applicableCategories: ["Fashion", "Accessories"],
    firstTimeOnly: false,
    stackable: false,
    createdBy: "Marketing Team",
    createdAt: new Date("2024-01-10")
  }
]

export default function CouponsPage() {
  const [coupons, setCoupons] = useState(mockCoupons)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [typeFilter, setTypeFilter] = useState("all")
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedCoupon, setSelectedCoupon] = useState<Coupon | null>(null)
  const [newCoupon, setNewCoupon] = useState({
    code: "",
    name: "",
    description: "",
    type: 'percentage' as Coupon['type'],
    value: 0,
    minimumAmount: 0,
    maximumDiscount: 0,
    usageLimit: 0,
    userLimit: 1,
    startDate: "",
    endDate: "",
    firstTimeOnly: false,
    stackable: false
  })

  const { t } = useLanguage()
  const { toast } = useToast()

  // Calculate summary statistics
  const totalCoupons = coupons.length
  const activeCoupons = coupons.filter(c => c.status === 'active').length
  const totalUsage = coupons.reduce((sum, coupon) => sum + coupon.usageCount, 0)
  const totalSavings = coupons.reduce((sum, coupon) => {
    if (coupon.type === 'percentage') {
      return sum + (coupon.usageCount * (coupon.maximumDiscount || 50))
    } else if (coupon.type === 'fixed_amount') {
      return sum + (coupon.usageCount * coupon.value)
    }
    return sum
  }, 0)

  // Filter coupons
  const filteredCoupons = coupons.filter(coupon => {
    const matchesSearch = coupon.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         coupon.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         coupon.description.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = statusFilter === "all" || coupon.status === statusFilter
    const matchesType = typeFilter === "all" || coupon.type === typeFilter

    return matchesSearch && matchesStatus && matchesType
  })

  const handleCreateCoupon = () => {
    if (!newCoupon.code || !newCoupon.name || !newCoupon.startDate || !newCoupon.endDate) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive"
      })
      return
    }

    const coupon: Coupon = {
      id: Date.now().toString(),
      code: newCoupon.code.toUpperCase(),
      name: newCoupon.name,
      description: newCoupon.description,
      type: newCoupon.type,
      value: newCoupon.value,
      minimumAmount: newCoupon.minimumAmount || undefined,
      maximumDiscount: newCoupon.maximumDiscount || undefined,
      usageLimit: newCoupon.usageLimit || undefined,
      usageCount: 0,
      userLimit: newCoupon.userLimit || undefined,
      startDate: new Date(newCoupon.startDate),
      endDate: new Date(newCoupon.endDate),
      status: new Date(newCoupon.startDate) > new Date() ? 'scheduled' : 'active',
      firstTimeOnly: newCoupon.firstTimeOnly,
      stackable: newCoupon.stackable,
      createdBy: "Current User",
      createdAt: new Date()
    }

    setCoupons([coupon, ...coupons])
    setShowCreateModal(false)
    setNewCoupon({
      code: "",
      name: "",
      description: "",
      type: 'percentage',
      value: 0,
      minimumAmount: 0,
      maximumDiscount: 0,
      usageLimit: 0,
      userLimit: 1,
      startDate: "",
      endDate: "",
      firstTimeOnly: false,
      stackable: false
    })

    toast({
      title: "Coupon Created",
      description: `Coupon ${coupon.code} has been created successfully`
    })
  }

  const handleToggleStatus = (couponId: string) => {
    setCoupons(coupons.map(coupon => {
      if (coupon.id === couponId) {
        const newStatus = coupon.status === 'active' ? 'inactive' : 'active'
        return { ...coupon, status: newStatus }
      }
      return coupon
    }))

    toast({
      title: "Status Updated",
      description: "Coupon status has been updated"
    })
  }

  const handleDeleteCoupon = (couponId: string) => {
    setCoupons(coupons.filter(coupon => coupon.id !== couponId))
    toast({
      title: "Coupon Deleted",
      description: "Coupon has been deleted successfully"
    })
  }

  const handleCopyCouponCode = (code: string) => {
    navigator.clipboard.writeText(code)
    toast({
      title: "Copied!",
      description: `Coupon code ${code} copied to clipboard`
    })
  }

  const getStatusColor = (status: Coupon['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'inactive':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
      case 'expired':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'scheduled':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: Coupon['status']) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4" />
      case 'inactive':
        return <XCircle className="h-4 w-4" />
      case 'expired':
        return <Clock className="h-4 w-4" />
      case 'scheduled':
        return <Calendar className="h-4 w-4" />
      default:
        return <Tag className="h-4 w-4" />
    }
  }

  const getTypeIcon = (type: Coupon['type']) => {
    switch (type) {
      case 'percentage':
        return <Percent className="h-4 w-4" />
      case 'fixed_amount':
        return <Tag className="h-4 w-4" />
      case 'free_shipping':
        return <Gift className="h-4 w-4" />
      case 'buy_x_get_y':
        return <ShoppingCart className="h-4 w-4" />
      default:
        return <Tag className="h-4 w-4" />
    }
  }

  const formatCouponValue = (coupon: Coupon) => {
    switch (coupon.type) {
      case 'percentage':
        return `${coupon.value}% off`
      case 'fixed_amount':
        return `${formatPrice(coupon.value)} off`
      case 'free_shipping':
        return 'Free shipping'
      case 'buy_x_get_y':
        return `Buy ${coupon.value} get 1 free`
      default:
        return coupon.value.toString()
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Coupons & Discounts</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage promotional codes and discount campaigns
          </p>
        </div>
        <Button onClick={() => setShowCreateModal(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Coupon
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Coupons
                </p>
                <p className="text-2xl font-bold">{totalCoupons}</p>
              </div>
              <Tag className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Active Coupons
                </p>
                <p className="text-2xl font-bold text-green-600">{activeCoupons}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Usage
                </p>
                <p className="text-2xl font-bold">{totalUsage}</p>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Savings
                </p>
                <p className="text-2xl font-bold">{formatPrice(totalSavings)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search coupons..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="expired">Expired</option>
                <option value="scheduled">Scheduled</option>
              </select>
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
              >
                <option value="all">All Types</option>
                <option value="percentage">Percentage</option>
                <option value="fixed_amount">Fixed Amount</option>
                <option value="free_shipping">Free Shipping</option>
                <option value="buy_x_get_y">Buy X Get Y</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Coupons Table */}
      <Card>
        <CardHeader>
          <CardTitle>Coupons</CardTitle>
          <CardDescription>
            Showing {filteredCoupons.length} of {totalCoupons} coupons
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium">Code</th>
                  <th className="text-left py-3 px-4 font-medium">Name</th>
                  <th className="text-left py-3 px-4 font-medium">Type</th>
                  <th className="text-left py-3 px-4 font-medium">Value</th>
                  <th className="text-left py-3 px-4 font-medium">Usage</th>
                  <th className="text-left py-3 px-4 font-medium">Status</th>
                  <th className="text-left py-3 px-4 font-medium">Valid Until</th>
                  <th className="text-left py-3 px-4 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredCoupons.map((coupon) => (
                  <tr key={coupon.id} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-2">
                        <code className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm font-mono">
                          {coupon.code}
                        </code>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleCopyCouponCode(coupon.code)}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div>
                        <p className="font-medium">{coupon.name}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {coupon.description}
                        </p>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-2">
                        {getTypeIcon(coupon.type)}
                        <span className="capitalize">{coupon.type.replace('_', ' ')}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <p className="font-medium">{formatCouponValue(coupon)}</p>
                      {coupon.minimumAmount && (
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Min: {formatPrice(coupon.minimumAmount)}
                        </p>
                      )}
                    </td>
                    <td className="py-3 px-4">
                      <div className="text-sm">
                        <p className="font-medium">{coupon.usageCount}</p>
                        {coupon.usageLimit && (
                          <p className="text-gray-600 dark:text-gray-400">
                            / {coupon.usageLimit} limit
                          </p>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <Badge className={getStatusColor(coupon.status)}>
                        {getStatusIcon(coupon.status)}
                        <span className="ml-1 capitalize">{coupon.status}</span>
                      </Badge>
                    </td>
                    <td className="py-3 px-4">
                      <p className="text-sm">{formatDate(coupon.endDate)}</p>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">
                          <Eye className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleToggleStatus(coupon.id)}
                        >
                          {coupon.status === 'active' ? (
                            <XCircle className="h-3 w-3" />
                          ) : (
                            <CheckCircle className="h-3 w-3" />
                          )}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDeleteCoupon(coupon.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Create Coupon Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <CardTitle>Create New Coupon</CardTitle>
              <CardDescription>
                Set up a new promotional code or discount
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Coupon Code *</label>
                  <input
                    type="text"
                    value={newCoupon.code}
                    onChange={(e) => setNewCoupon({...newCoupon, code: e.target.value.toUpperCase()})}
                    placeholder="e.g., SAVE20"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Coupon Name *</label>
                  <input
                    type="text"
                    value={newCoupon.name}
                    onChange={(e) => setNewCoupon({...newCoupon, name: e.target.value})}
                    placeholder="e.g., 20% Off Everything"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Description</label>
                <textarea
                  value={newCoupon.description}
                  onChange={(e) => setNewCoupon({...newCoupon, description: e.target.value})}
                  placeholder="Describe what this coupon offers..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Discount Type *</label>
                  <select
                    value={newCoupon.type}
                    onChange={(e) => setNewCoupon({...newCoupon, type: e.target.value as Coupon['type']})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="percentage">Percentage</option>
                    <option value="fixed_amount">Fixed Amount</option>
                    <option value="free_shipping">Free Shipping</option>
                    <option value="buy_x_get_y">Buy X Get Y</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">
                    {newCoupon.type === 'percentage' ? 'Percentage (%)' : 
                     newCoupon.type === 'fixed_amount' ? 'Amount ($)' :
                     newCoupon.type === 'buy_x_get_y' ? 'Buy Quantity' : 'Value'}
                  </label>
                  <input
                    type="number"
                    value={newCoupon.value}
                    onChange={(e) => setNewCoupon({...newCoupon, value: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                    min="0"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Minimum Amount</label>
                  <input
                    type="number"
                    value={newCoupon.minimumAmount}
                    onChange={(e) => setNewCoupon({...newCoupon, minimumAmount: Number(e.target.value)})}
                    placeholder="0"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                    min="0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Maximum Discount</label>
                  <input
                    type="number"
                    value={newCoupon.maximumDiscount}
                    onChange={(e) => setNewCoupon({...newCoupon, maximumDiscount: Number(e.target.value)})}
                    placeholder="0"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                    min="0"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Usage Limit</label>
                  <input
                    type="number"
                    value={newCoupon.usageLimit}
                    onChange={(e) => setNewCoupon({...newCoupon, usageLimit: Number(e.target.value)})}
                    placeholder="Unlimited"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                    min="0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">User Limit</label>
                  <input
                    type="number"
                    value={newCoupon.userLimit}
                    onChange={(e) => setNewCoupon({...newCoupon, userLimit: Number(e.target.value)})}
                    placeholder="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                    min="1"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Start Date *</label>
                  <input
                    type="datetime-local"
                    value={newCoupon.startDate}
                    onChange={(e) => setNewCoupon({...newCoupon, startDate: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">End Date *</label>
                  <input
                    type="datetime-local"
                    value={newCoupon.endDate}
                    onChange={(e) => setNewCoupon({...newCoupon, endDate: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-6">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={newCoupon.firstTimeOnly}
                    onChange={(e) => setNewCoupon({...newCoupon, firstTimeOnly: e.target.checked})}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="text-sm">First-time customers only</span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={newCoupon.stackable}
                    onChange={(e) => setNewCoupon({...newCoupon, stackable: e.target.checked})}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="text-sm">Stackable with other coupons</span>
                </label>
              </div>

              <div className="flex space-x-2 pt-4">
                <Button onClick={handleCreateCoupon} className="flex-1">
                  Create Coupon
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowCreateModal(false)}
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
