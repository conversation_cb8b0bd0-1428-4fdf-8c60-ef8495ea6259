"use client"

import { useState, useEffect, useRef } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useDebounce } from "@/hooks/use-debounce"
import { formatPrice } from "@/lib/utils"
import {
  Search,
  Clock,
  TrendingUp,
  Star,
  ArrowRight,
  X,
  Filter,
  Zap,
  Tag,
  ShoppingBag,
  Sparkles,
} from "lucide-react"

interface SearchResult {
  id: string
  type: 'product' | 'category' | 'brand' | 'suggestion'
  title: string
  subtitle?: string
  price?: number
  originalPrice?: number
  rating?: number
  reviews?: number
  image?: string
  url: string
  badge?: string
  inStock?: boolean
}

interface SearchSuggestion {
  id: string
  query: string
  type: 'recent' | 'trending' | 'autocomplete'
  count?: number
}

interface InstantSearchProps {
  placeholder?: string
  className?: string
  onSearch?: (query: string) => void
  onResultClick?: (result: SearchResult) => void
}

export function InstantSearch({ 
  placeholder = "Search products, brands, categories...",
  className = "",
  onSearch,
  onResultClick
}: InstantSearchProps) {
  const [query, setQuery] = useState("")
  const [isOpen, setIsOpen] = useState(false)
  const [results, setResults] = useState<SearchResult[]>([])
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const [recentSearches, setRecentSearches] = useState<string[]>([])

  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const debouncedQuery = useDebounce(query, 300)

  // Mock data - in real app, this would come from API
  const mockResults: SearchResult[] = [
    {
      id: "1",
      type: "product",
      title: "Premium Wireless Headphones",
      subtitle: "AudioTech",
      price: 199.99,
      originalPrice: 249.99,
      rating: 4.8,
      reviews: 124,
      image: "/product-1.jpg",
      url: "/product/premium-wireless-headphones",
      badge: "Best Seller",
      inStock: true
    },
    {
      id: "2",
      type: "product",
      title: "Smart Fitness Watch",
      subtitle: "TechFit",
      price: 299.99,
      rating: 4.6,
      reviews: 89,
      image: "/product-2.jpg",
      url: "/product/smart-fitness-watch",
      inStock: true
    },
    {
      id: "3",
      type: "category",
      title: "Electronics",
      subtitle: "2,450 products",
      url: "/shop?category=electronics"
    },
    {
      id: "4",
      type: "brand",
      title: "AudioTech",
      subtitle: "Premium audio equipment",
      url: "/shop?brand=audiotech"
    }
  ]

  const mockSuggestions: SearchSuggestion[] = [
    { id: "1", query: "wireless headphones", type: "trending", count: 1250 },
    { id: "2", query: "smart watch", type: "trending", count: 890 },
    { id: "3", query: "bluetooth speakers", type: "trending", count: 650 },
    { id: "4", query: "gaming keyboard", type: "recent" },
    { id: "5", query: "laptop stand", type: "recent" }
  ]

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches')
    if (saved) {
      setRecentSearches(JSON.parse(saved))
    }
  }, [])

  // Perform search when debounced query changes
  useEffect(() => {
    if (debouncedQuery.trim()) {
      performSearch(debouncedQuery)
    } else {
      setResults([])
      setSuggestions(mockSuggestions)
    }
  }, [debouncedQuery])

  // Handle clicks outside to close search
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSelectedIndex(-1)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const performSearch = async (searchQuery: string) => {
    setIsLoading(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 200))
    
    // Filter mock results based on query
    const filteredResults = mockResults.filter(result =>
      result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      result.subtitle?.toLowerCase().includes(searchQuery.toLowerCase())
    )

    // Generate autocomplete suggestions
    const autocompleteSuggestions = [
      `${searchQuery} reviews`,
      `${searchQuery} price`,
      `best ${searchQuery}`,
      `${searchQuery} deals`
    ].map((suggestion, index) => ({
      id: `auto-${index}`,
      query: suggestion,
      type: 'autocomplete' as const
    }))

    setResults(filteredResults)
    setSuggestions([...mockSuggestions, ...autocompleteSuggestions])
    setIsLoading(false)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setQuery(value)
    setIsOpen(true)
    setSelectedIndex(-1)
  }

  const handleInputFocus = () => {
    setIsOpen(true)
    if (!query.trim()) {
      setSuggestions(mockSuggestions)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    const totalItems = results.length + suggestions.length
    
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => (prev + 1) % totalItems)
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => (prev - 1 + totalItems) % totalItems)
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0) {
          if (selectedIndex < results.length) {
            handleResultClick(results[selectedIndex])
          } else {
            const suggestionIndex = selectedIndex - results.length
            handleSuggestionClick(suggestions[suggestionIndex])
          }
        } else if (query.trim()) {
          handleSearch(query)
        }
        break
      case 'Escape':
        setIsOpen(false)
        setSelectedIndex(-1)
        inputRef.current?.blur()
        break
    }
  }

  const handleResultClick = (result: SearchResult) => {
    addToRecentSearches(result.title)
    setIsOpen(false)
    setQuery("")
    onResultClick?.(result)
  }

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.query)
    addToRecentSearches(suggestion.query)
    performSearch(suggestion.query)
  }

  const handleSearch = (searchQuery: string) => {
    if (searchQuery.trim()) {
      addToRecentSearches(searchQuery)
      setIsOpen(false)
      onSearch?.(searchQuery)
    }
  }

  const addToRecentSearches = (searchQuery: string) => {
    const updated = [searchQuery, ...recentSearches.filter(s => s !== searchQuery)].slice(0, 5)
    setRecentSearches(updated)
    localStorage.setItem('recentSearches', JSON.stringify(updated))
  }

  const clearRecentSearches = () => {
    setRecentSearches([])
    localStorage.removeItem('recentSearches')
  }

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'product':
        return <ShoppingBag className="h-4 w-4" />
      case 'category':
        return <Tag className="h-4 w-4" />
      case 'brand':
        return <Sparkles className="h-4 w-4" />
      default:
        return <Search className="h-4 w-4" />
    }
  }

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'trending':
        return <TrendingUp className="h-3 w-3 text-red-500" />
      case 'recent':
        return <Clock className="h-3 w-3 text-gray-400" />
      default:
        return <Search className="h-3 w-3 text-gray-400" />
    }
  }

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onKeyDown={handleKeyDown}
          className="pl-10 pr-10"
        />
        {query && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
            onClick={() => {
              setQuery("")
              setResults([])
              setSuggestions(mockSuggestions)
              inputRef.current?.focus()
            }}
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Search Results Dropdown */}
      {isOpen && (
        <Card className="absolute top-full left-0 right-0 z-50 mt-1 max-h-96 overflow-y-auto shadow-lg">
          <CardContent className="p-0">
            {isLoading ? (
              <div className="p-4 text-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mx-auto"></div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">Searching...</p>
              </div>
            ) : (
              <div>
                {/* Search Results */}
                {results.length > 0 && (
                  <div>
                    <div className="px-4 py-2 bg-gray-50 dark:bg-gray-800 border-b">
                      <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Products & Categories
                      </h4>
                    </div>
                    {results.map((result, index) => (
                      <Link
                        key={result.id}
                        href={result.url}
                        onClick={() => handleResultClick(result)}
                        className={`block px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-800 border-b border-gray-100 dark:border-gray-700 ${
                          selectedIndex === index ? 'bg-gray-50 dark:bg-gray-800' : ''
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          {result.image ? (
                            <div className="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                              <div className="text-lg">📱</div>
                            </div>
                          ) : (
                            <div className="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                              {getResultIcon(result.type)}
                            </div>
                          )}
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2">
                              <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                {result.title}
                              </h4>
                              {result.badge && (
                                <Badge className="text-xs">{result.badge}</Badge>
                              )}
                            </div>
                            
                            {result.subtitle && (
                              <p className="text-xs text-gray-600 dark:text-gray-400">
                                {result.subtitle}
                              </p>
                            )}
                            
                            {result.price && (
                              <div className="flex items-center space-x-2 mt-1">
                                <span className="text-sm font-semibold text-gray-900 dark:text-white">
                                  {formatPrice(result.price)}
                                </span>
                                {result.originalPrice && (
                                  <span className="text-xs text-gray-500 line-through">
                                    {formatPrice(result.originalPrice)}
                                  </span>
                                )}
                                {result.rating && (
                                  <div className="flex items-center space-x-1">
                                    <Star className="h-3 w-3 text-yellow-400 fill-current" />
                                    <span className="text-xs text-gray-600 dark:text-gray-400">
                                      {result.rating} ({result.reviews})
                                    </span>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                          
                          <ArrowRight className="h-4 w-4 text-gray-400" />
                        </div>
                      </Link>
                    ))}
                  </div>
                )}

                {/* Search Suggestions */}
                {suggestions.length > 0 && (
                  <div>
                    <div className="px-4 py-2 bg-gray-50 dark:bg-gray-800 border-b">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {query ? 'Suggestions' : 'Popular Searches'}
                        </h4>
                        {recentSearches.length > 0 && !query && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={clearRecentSearches}
                            className="text-xs"
                          >
                            Clear
                          </Button>
                        )}
                      </div>
                    </div>
                    {suggestions.slice(0, 5).map((suggestion, index) => (
                      <button
                        key={suggestion.id}
                        onClick={() => handleSuggestionClick(suggestion)}
                        className={`w-full px-4 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-800 border-b border-gray-100 dark:border-gray-700 ${
                          selectedIndex === results.length + index ? 'bg-gray-50 dark:bg-gray-800' : ''
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            {getSuggestionIcon(suggestion.type)}
                            <span className="text-sm text-gray-700 dark:text-gray-300">
                              {suggestion.query}
                            </span>
                          </div>
                          {suggestion.count && (
                            <span className="text-xs text-gray-500">
                              {suggestion.count.toLocaleString()} searches
                            </span>
                          )}
                        </div>
                      </button>
                    ))}
                  </div>
                )}

                {/* No Results */}
                {!isLoading && query && results.length === 0 && (
                  <div className="p-8 text-center">
                    <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      No results found
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                      Try searching for something else or check your spelling
                    </p>
                    <Button
                      variant="outline"
                      onClick={() => handleSearch(query)}
                    >
                      Search anyway
                    </Button>
                  </div>
                )}

                {/* Quick Actions */}
                {query && (
                  <div className="px-4 py-3 bg-gray-50 dark:bg-gray-800 border-t">
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-600 dark:text-gray-400">
                        Press Enter to search for "{query}"
                      </span>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSearch(query)}
                          className="text-xs"
                        >
                          <Search className="h-3 w-3 mr-1" />
                          Search All
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
