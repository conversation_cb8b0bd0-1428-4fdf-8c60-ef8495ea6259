"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import {
  Users,
  Target,
  Award,
  Globe,
  Zap,
  Shield,
  Heart,
  TrendingUp,
  Lightbulb,
  Handshake,
  Star,
  CheckCircle,
} from "lucide-react"

export default function AboutPage() {
  const { t } = useLanguage()

  const stats = [
    { label: "Happy Customers", value: "50,000+", icon: Users },
    { label: "Products Sold", value: "1M+", icon: Award },
    { label: "Countries Served", value: "25+", icon: Globe },
    { label: "Years of Excellence", value: "10+", icon: Star },
  ]

  const values = [
    {
      icon: Lightbulb,
      title: "Innovation",
      description: "We constantly push the boundaries of technology to deliver cutting-edge solutions that transform businesses and lives."
    },
    {
      icon: Shield,
      title: "Trust & Security",
      description: "Your data and transactions are protected with enterprise-grade security measures and transparent business practices."
    },
    {
      icon: Heart,
      title: "Customer First",
      description: "Every decision we make is guided by our commitment to delivering exceptional customer experiences and value."
    },
    {
      icon: Handshake,
      title: "Partnership",
      description: "We build lasting relationships with our customers, suppliers, and community through mutual respect and collaboration."
    },
    {
      icon: TrendingUp,
      title: "Excellence",
      description: "We strive for excellence in everything we do, from product quality to customer service and business operations."
    },
    {
      icon: Zap,
      title: "Agility",
      description: "We adapt quickly to market changes and customer needs, ensuring we always deliver relevant and timely solutions."
    }
  ]

  const team = [
    {
      name: "Sarah Johnson",
      role: "CEO & Founder",
      description: "Visionary leader with 15+ years in e-commerce and AI technology.",
      image: "/team-1.jpg"
    },
    {
      name: "Michael Chen",
      role: "CTO",
      description: "Technology expert specializing in AI, machine learning, and scalable systems.",
      image: "/team-2.jpg"
    },
    {
      name: "Emily Rodriguez",
      role: "Head of Customer Experience",
      description: "Customer advocate focused on delivering exceptional service and support.",
      image: "/team-3.jpg"
    },
    {
      name: "David Thompson",
      role: "VP of Operations",
      description: "Operations specialist ensuring efficient and reliable service delivery.",
      image: "/team-4.jpg"
    }
  ]

  const milestones = [
    {
      year: "2014",
      title: "Company Founded",
      description: "Started with a vision to revolutionize e-commerce through AI technology."
    },
    {
      year: "2016",
      title: "First AI Integration",
      description: "Launched our first AI-powered recommendation engine."
    },
    {
      year: "2018",
      title: "International Expansion",
      description: "Expanded operations to serve customers across 25 countries."
    },
    {
      year: "2020",
      title: "1 Million Products",
      description: "Reached the milestone of 1 million products sold."
    },
    {
      year: "2022",
      title: "AI Chat Support",
      description: "Introduced 24/7 AI-powered customer support."
    },
    {
      year: "2024",
      title: "Next Generation Platform",
      description: "Launched our most advanced AI-powered e-commerce platform."
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-600 to-primary-800 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              About AIDEVCOMMERCE
            </h1>
            <p className="text-xl md:text-2xl text-primary-100 mb-8 leading-relaxed">
              Pioneering the future of e-commerce through artificial intelligence, 
              exceptional service, and innovative technology solutions.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Badge className="bg-white/20 text-white px-4 py-2 text-sm">
                <Zap className="h-4 w-4 mr-2" />
                AI-Powered
              </Badge>
              <Badge className="bg-white/20 text-white px-4 py-2 text-sm">
                <Globe className="h-4 w-4 mr-2" />
                Global Reach
              </Badge>
              <Badge className="bg-white/20 text-white px-4 py-2 text-sm">
                <Award className="h-4 w-4 mr-2" />
                Award Winning
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat) => {
              const Icon = stat.icon
              return (
                <Card key={stat.label} className="text-center">
                  <CardContent className="p-6">
                    <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Icon className="h-8 w-8 text-primary-600 dark:text-primary-400" />
                    </div>
                    <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                      {stat.value}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">{stat.label}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="h-6 w-6 mr-3 text-primary-600" />
                  Our Mission
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  To democratize access to cutting-edge technology and professional services 
                  by creating an AI-powered e-commerce platform that understands, anticipates, 
                  and fulfills customer needs with unprecedented precision and care.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Lightbulb className="h-6 w-6 mr-3 text-primary-600" />
                  Our Vision
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  To be the world's most trusted and innovative e-commerce platform, 
                  where artificial intelligence and human expertise converge to create 
                  extraordinary shopping experiences and drive global digital transformation.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Our Core Values
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              These principles guide every decision we make and every interaction we have
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {values.map((value) => {
              const Icon = value.icon
              return (
                <Card key={value.title} className="h-full">
                  <CardContent className="p-6">
                    <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mb-4">
                      <Icon className="h-6 w-6 text-primary-600 dark:text-primary-400" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                      {value.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                      {value.description}
                    </p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Timeline */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Our Journey
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              A decade of innovation, growth, and customer success
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="relative">
              {/* Timeline line */}
              <div className="absolute left-4 md:left-1/2 transform md:-translate-x-1/2 w-0.5 h-full bg-primary-200 dark:bg-primary-800"></div>
              
              {milestones.map((milestone, index) => (
                <div key={milestone.year} className={`relative flex items-center mb-8 ${
                  index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
                }`}>
                  {/* Timeline dot */}
                  <div className="absolute left-4 md:left-1/2 transform md:-translate-x-1/2 w-4 h-4 bg-primary-600 rounded-full border-4 border-white dark:border-gray-800 z-10"></div>
                  
                  {/* Content */}
                  <div className={`ml-12 md:ml-0 md:w-1/2 ${
                    index % 2 === 0 ? 'md:pr-8' : 'md:pl-8'
                  }`}>
                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center mb-2">
                          <Badge className="bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
                            {milestone.year}
                          </Badge>
                        </div>
                        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                          {milestone.title}
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400">
                          {milestone.description}
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Team */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Meet Our Leadership Team
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Experienced professionals dedicated to driving innovation and excellence
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member) => (
              <Card key={member.name} className="text-center">
                <CardContent className="p-6">
                  <div className="w-24 h-24 bg-gray-200 dark:bg-gray-700 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <Users className="h-12 w-12 text-gray-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-1">
                    {member.name}
                  </h3>
                  <p className="text-primary-600 dark:text-primary-400 font-medium mb-3">
                    {member.role}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {member.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Experience the Future of E-commerce?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Join millions of satisfied customers who trust AIDEVCOMMERCE for their technology and service needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/shop"
              className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center justify-center"
            >
              <CheckCircle className="h-5 w-5 mr-2" />
              Start Shopping
            </a>
            <a
              href="/contact"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors inline-flex items-center justify-center"
            >
              Contact Us
            </a>
          </div>
        </div>
      </section>
    </div>
  )
}
