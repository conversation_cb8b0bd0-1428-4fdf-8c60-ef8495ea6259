"use client"

import { useState, useRef, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { formatDate } from "@/lib/utils"
import {
  Upload,
  X,
  Image as ImageIcon,
  File,
  Download,
  Eye,
  Trash2,
  Plus,
  AlertCircle,
  CheckCircle,
  Clock,
  FileImage,
  FileText,
  Video,
} from "lucide-react"

interface UploadedFile {
  id: string
  name: string
  size: number
  type: string
  url: string
  uploadedAt: Date
  status: 'uploading' | 'completed' | 'error'
  progress?: number
  error?: string
}

interface FileUploadProps {
  accept?: string
  maxSize?: number // in bytes
  maxFiles?: number
  onFilesUploaded?: (files: UploadedFile[]) => void
  existingFiles?: UploadedFile[]
  allowMultiple?: boolean
  showPreview?: boolean
}

const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
const ALLOWED_DOCUMENT_TYPES = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB

export default function FileUpload({
  accept = "image/*",
  maxSize = MAX_FILE_SIZE,
  maxFiles = 10,
  onFilesUploaded,
  existingFiles = [],
  allowMultiple = true,
  showPreview = true
}: FileUploadProps) {
  const [files, setFiles] = useState<UploadedFile[]>(existingFiles)
  const [isDragging, setIsDragging] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return <FileImage className="h-8 w-8 text-blue-500" />
    if (type.startsWith('video/')) return <Video className="h-8 w-8 text-purple-500" />
    if (type.includes('pdf')) return <FileText className="h-8 w-8 text-red-500" />
    return <File className="h-8 w-8 text-gray-500" />
  }

  const validateFile = (file: File): string | null => {
    if (file.size > maxSize) {
      return `File size must be less than ${formatFileSize(maxSize)}`
    }

    if (accept === "image/*" && !ALLOWED_IMAGE_TYPES.includes(file.type)) {
      return "Only image files are allowed (JPEG, PNG, WebP, GIF)"
    }

    if (accept === ".pdf,.doc,.docx" && !ALLOWED_DOCUMENT_TYPES.includes(file.type)) {
      return "Only document files are allowed (PDF, DOC, DOCX)"
    }

    return null
  }

  const simulateUpload = (file: UploadedFile): Promise<UploadedFile> => {
    return new Promise((resolve, reject) => {
      let progress = 0
      const interval = setInterval(() => {
        progress += Math.random() * 30
        if (progress >= 100) {
          clearInterval(interval)
          // Simulate occasional upload errors
          if (Math.random() < 0.1) {
            reject(new Error("Upload failed. Please try again."))
          } else {
            resolve({
              ...file,
              status: 'completed',
              progress: 100,
              url: URL.createObjectURL(new File([file.name], file.name, { type: file.type }))
            })
          }
        } else {
          setFiles(prevFiles =>
            prevFiles.map(f =>
              f.id === file.id ? { ...f, progress: Math.min(progress, 100) } : f
            )
          )
        }
      }, 200)
    })
  }

  const handleFiles = useCallback(async (fileList: FileList) => {
    const newFiles = Array.from(fileList)

    // Check file count limit
    if (!allowMultiple && newFiles.length > 1) {
      toast({
        title: "Too many files",
        description: "Only one file is allowed",
        variant: "destructive"
      })
      return
    }

    if (files.length + newFiles.length > maxFiles) {
      toast({
        title: "Too many files",
        description: `Maximum ${maxFiles} files allowed`,
        variant: "destructive"
      })
      return
    }

    // Validate files
    const validFiles: File[] = []
    for (const file of newFiles) {
      const error = validateFile(file)
      if (error) {
        toast({
          title: "Invalid file",
          description: `${file.name}: ${error}`,
          variant: "destructive"
        })
      } else {
        validFiles.push(file)
      }
    }

    if (validFiles.length === 0) return

    // Create upload file objects
    const uploadFiles: UploadedFile[] = validFiles.map(file => ({
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: file.name,
      size: file.size,
      type: file.type,
      url: '',
      uploadedAt: new Date(),
      status: 'uploading',
      progress: 0
    }))

    setFiles(prevFiles => [...prevFiles, ...uploadFiles])

    // Simulate upload for each file
    const uploadPromises = uploadFiles.map(async (uploadFile) => {
      try {
        const completedFile = await simulateUpload(uploadFile)
        setFiles(prevFiles =>
          prevFiles.map(f =>
            f.id === uploadFile.id ? completedFile : f
          )
        )
        return completedFile
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Upload failed'
        setFiles(prevFiles =>
          prevFiles.map(f =>
            f.id === uploadFile.id
              ? { ...f, status: 'error' as const, error: errorMessage }
              : f
          )
        )
        toast({
          title: "Upload failed",
          description: `${uploadFile.name}: ${errorMessage}`,
          variant: "destructive"
        })
        throw error
      }
    })

    try {
      const completedFiles = await Promise.allSettled(uploadPromises)
      const successfulFiles = completedFiles
        .filter((result): result is PromiseFulfilledResult<UploadedFile> => result.status === 'fulfilled')
        .map(result => result.value)

      if (successfulFiles.length > 0) {
        toast({
          title: "Upload successful",
          description: `${successfulFiles.length} file(s) uploaded successfully`
        })
        onFilesUploaded?.(successfulFiles)
      }
    } catch (error) {
      // Individual file errors are already handled above
    }
  }, [files, maxFiles, allowMultiple, onFilesUploaded, toast])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    const droppedFiles = e.dataTransfer.files
    if (droppedFiles.length > 0) {
      handleFiles(droppedFiles)
    }
  }, [handleFiles])

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files
    if (selectedFiles && selectedFiles.length > 0) {
      handleFiles(selectedFiles)
    }
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [handleFiles])

  const removeFile = (fileId: string) => {
    setFiles(prevFiles => prevFiles.filter(f => f.id !== fileId))
    toast({
      title: "File removed",
      description: "File has been removed from the upload list"
    })
  }

  const retryUpload = async (fileId: string) => {
    const file = files.find(f => f.id === fileId)
    if (!file) return

    setFiles(prevFiles =>
      prevFiles.map(f =>
        f.id === fileId
          ? { ...f, status: 'uploading', progress: 0, error: undefined }
          : f
      )
    )

    try {
      const completedFile = await simulateUpload(file)
      setFiles(prevFiles =>
        prevFiles.map(f =>
          f.id === fileId ? completedFile : f
        )
      )
      toast({
        title: "Upload successful",
        description: `${file.name} uploaded successfully`
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed'
      setFiles(prevFiles =>
        prevFiles.map(f =>
          f.id === fileId
            ? { ...f, status: 'error', error: errorMessage }
            : f
        )
      )
    }
  }

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <Card>
        <CardContent className="p-6">
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              isDragging
                ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {isDragging ? 'Drop files here' : 'Upload files'}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Drag and drop files here, or click to select files
            </p>
            <Button onClick={openFileDialog} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Select Files
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              accept={accept}
              multiple={allowMultiple}
              onChange={handleFileSelect}
              className="hidden"
            />
            <div className="mt-4 text-sm text-gray-500">
              <p>Maximum file size: {formatFileSize(maxSize)}</p>
              <p>Maximum files: {maxFiles}</p>
              {accept === "image/*" && <p>Supported formats: JPEG, PNG, WebP, GIF</p>}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* File List */}
      {files.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Uploaded Files ({files.length})</CardTitle>
            <CardDescription>
              Manage your uploaded files
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {files.map((file) => (
                <div
                  key={file.id}
                  className="flex items-center space-x-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
                >
                  {/* File Icon/Preview */}
                  <div className="flex-shrink-0">
                    {showPreview && file.type.startsWith('image/') && file.url ? (
                      <img
                        src={file.url}
                        alt={file.name}
                        className="h-12 w-12 object-cover rounded-lg"
                      />
                    ) : (
                      getFileIcon(file.type)
                    )}
                  </div>

                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {file.name}
                    </p>
                    <p className="text-sm text-gray-500">
                      {formatFileSize(file.size)} • {formatDate(file.uploadedAt)}
                    </p>
                    
                    {/* Progress Bar */}
                    {file.status === 'uploading' && (
                      <div className="mt-2">
                        <div className="bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div
                            className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${file.progress || 0}%` }}
                          />
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          Uploading... {Math.round(file.progress || 0)}%
                        </p>
                      </div>
                    )}

                    {/* Error Message */}
                    {file.status === 'error' && file.error && (
                      <p className="text-sm text-red-600 dark:text-red-400 mt-1">
                        {file.error}
                      </p>
                    )}
                  </div>

                  {/* Status Badge */}
                  <div className="flex-shrink-0">
                    {file.status === 'completed' && (
                      <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Completed
                      </Badge>
                    )}
                    {file.status === 'uploading' && (
                      <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        <Clock className="h-3 w-3 mr-1" />
                        Uploading
                      </Badge>
                    )}
                    {file.status === 'error' && (
                      <Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Error
                      </Badge>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex-shrink-0 flex items-center space-x-2">
                    {file.status === 'completed' && file.url && (
                      <>
                        <Button size="sm" variant="outline">
                          <Eye className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Download className="h-3 w-3" />
                        </Button>
                      </>
                    )}
                    {file.status === 'error' && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => retryUpload(file.id)}
                      >
                        Retry
                      </Button>
                    )}
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => removeFile(file.id)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
