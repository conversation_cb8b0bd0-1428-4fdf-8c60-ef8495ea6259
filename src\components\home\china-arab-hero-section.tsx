"use client"

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { useLanguage } from '@/components/providers/language-provider'
import {
  Search,
  Globe,
  Truck,
  Shield,
  Users,
  TrendingUp,
  Package,
  Star,
  ArrowRight,
  Play,
  CheckCircle,
} from 'lucide-react'

const heroStats = [
  {
    number: '10,000+',
    label: 'Products Available',
    labelAr: 'منتج متاح',
    icon: Package,
  },
  {
    number: '500+',
    label: 'Verified Suppliers',
    labelAr: 'مورد موثق',
    icon: Users,
  },
  {
    number: '50+',
    label: 'Countries Served',
    labelAr: 'دولة نخدمها',
    icon: Globe,
  },
  {
    number: '99%',
    label: 'Customer Satisfaction',
    labelAr: 'رضا العملاء',
    icon: Star,
  },
]

const serviceHighlights = [
  {
    title: 'Quality Inspection',
    titleAr: 'فحص الجودة',
    description: 'Professional quality control before shipping',
    descriptionAr: 'مراقبة جودة احترافية قبل الشحن',
    icon: Shield,
  },
  {
    title: 'Secure Shipping',
    titleAr: 'شحن آمن',
    description: 'Safe delivery to Middle East & Arab countries',
    descriptionAr: 'توصيل آمن للشرق الأوسط والدول العربية',
    icon: Truck,
  },
  {
    title: 'Business Support',
    titleAr: 'دعم الأعمال',
    description: 'Complete support for Arab merchants',
    descriptionAr: 'دعم شامل للتجار العرب',
    icon: TrendingUp,
  },
]

export function ChinaArabHeroSection() {
  const { t, language } = useLanguage()
  const [searchQuery, setSearchQuery] = useState('')
  const [currentSlide, setCurrentSlide] = useState(0)

  const heroSlides = [
    {
      title: 'China to Arab Wholesale Marketplace',
      titleAr: 'سوق الجملة من الصين للعرب',
      subtitle: 'Connect with verified Chinese suppliers for wholesale, machinery, and raw materials',
      subtitleAr: 'تواصل مع موردين صينيين موثقين للجملة والآلات والمواد الخام',
      image: '/hero-china-arab-1.jpg',
      cta: 'Start Sourcing',
      ctaAr: 'ابدأ التوريد',
    },
    {
      title: 'Professional Services for Arab Merchants',
      titleAr: 'خدمات احترافية للتجار العرب',
      subtitle: 'Inspection, storage, shipping, and certification services',
      subtitleAr: 'خدمات الفحص والتخزين والشحن والشهادات',
      image: '/hero-china-arab-2.jpg',
      cta: 'View Services',
      ctaAr: 'عرض الخدمات',
    },
    {
      title: 'Stock Liquidation Deals',
      titleAr: 'عروض تصفية المخزون',
      subtitle: 'Exclusive wholesale prices on bulk inventory',
      subtitleAr: 'أسعار جملة حصرية على المخزون بالجملة',
      image: '/hero-china-arab-3.jpg',
      cta: 'Browse Deals',
      ctaAr: 'تصفح العروض',
    },
  ]

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length)
    }, 5000)
    return () => clearInterval(timer)
  }, [heroSlides.length])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`
    }
  }

  const currentSlideData = heroSlides[currentSlide]
  const isArabic = language === 'ar'

  return (
    <section className="relative min-h-[700px] bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[url('/pattern-china-arab.svg')] bg-repeat bg-center"></div>
      </div>

      {/* Background Image */}
      <div className="absolute inset-0 bg-black bg-opacity-40">
        <div 
          className="absolute inset-0 bg-cover bg-center transition-all duration-1000"
          style={{
            backgroundImage: `url(${currentSlideData.image})`,
          }}
        />
      </div>

      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 py-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-[500px]">
          {/* Left Column - Main Content */}
          <div className={`space-y-8 ${isArabic ? 'text-right' : 'text-left'}`}>
            {/* Badge */}
            <Badge className="bg-yellow-500 text-black hover:bg-yellow-400">
              <Globe className="h-4 w-4 mr-2" />
              {isArabic ? 'منصة التجارة الصينية العربية' : 'China-Arab Trade Platform'}
            </Badge>

            {/* Main Heading */}
            <div className="space-y-4">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight">
                {isArabic ? currentSlideData.titleAr : currentSlideData.title}
              </h1>
              <p className="text-xl md:text-2xl text-blue-100 leading-relaxed">
                {isArabic ? currentSlideData.subtitleAr : currentSlideData.subtitle}
              </p>
            </div>

            {/* Search Bar */}
            <form onSubmit={handleSearch} className="flex space-x-2 max-w-md">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="text"
                  placeholder={isArabic ? 'ابحث عن المنتجات...' : 'Search products...'}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 h-12 bg-white/90 backdrop-blur-sm border-0 text-gray-900"
                />
              </div>
              <Button type="submit" size="lg" className="h-12 px-6">
                <Search className="h-5 w-5" />
              </Button>
            </form>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
              <Button size="lg" className="bg-yellow-500 hover:bg-yellow-400 text-black font-semibold">
                {isArabic ? currentSlideData.ctaAr : currentSlideData.cta}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-900">
                <Play className="mr-2 h-5 w-5" />
                {isArabic ? 'شاهد الفيديو' : 'Watch Video'}
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="flex items-center space-x-6 pt-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-400" />
                <span className="text-white text-sm">
                  {isArabic ? 'موردين موثقين' : 'Verified Suppliers'}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-400" />
                <span className="text-white text-sm">
                  {isArabic ? 'ضمان الجودة' : 'Quality Guaranteed'}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-400" />
                <span className="text-white text-sm">
                  {isArabic ? 'شحن آمن' : 'Secure Shipping'}
                </span>
              </div>
            </div>
          </div>

          {/* Right Column - Stats & Services */}
          <div className="space-y-8">
            {/* Stats Grid */}
            <div className="grid grid-cols-2 gap-4">
              {heroStats.map((stat, index) => {
                const Icon = stat.icon
                return (
                  <div
                    key={index}
                    className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center border border-white/20"
                  >
                    <Icon className="h-8 w-8 text-yellow-400 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-white">{stat.number}</div>
                    <div className="text-sm text-blue-100">
                      {isArabic ? stat.labelAr : stat.label}
                    </div>
                  </div>
                )
              })}
            </div>

            {/* Service Highlights */}
            <div className="space-y-3">
              {serviceHighlights.map((service, index) => {
                const Icon = service.icon
                return (
                  <div
                    key={index}
                    className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-yellow-500 rounded-lg">
                        <Icon className="h-5 w-5 text-black" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-white">
                          {isArabic ? service.titleAr : service.title}
                        </h3>
                        <p className="text-sm text-blue-100">
                          {isArabic ? service.descriptionAr : service.description}
                        </p>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* Slide Indicators */}
        <div className="flex justify-center space-x-2 mt-12">
          {heroSlides.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full transition-all ${
                index === currentSlide
                  ? 'bg-yellow-400'
                  : 'bg-white/30 hover:bg-white/50'
              }`}
            />
          ))}
        </div>
      </div>

      {/* Bottom Wave */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg
          viewBox="0 0 1200 120"
          preserveAspectRatio="none"
          className="relative block w-full h-16"
        >
          <path
            d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"
            opacity=".25"
            className="fill-white"
          ></path>
          <path
            d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z"
            opacity=".5"
            className="fill-white"
          ></path>
          <path
            d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"
            className="fill-white"
          ></path>
        </svg>
      </div>
    </section>
  )
}
