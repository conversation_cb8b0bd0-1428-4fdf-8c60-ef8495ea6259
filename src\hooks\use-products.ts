import { useQuery } from '@tanstack/react-query'

export interface Product {
  id: string
  name: string
  slug: string
  description: string
  shortDescription?: string
  price: number
  comparePrice?: number
  sku: string
  barcode?: string
  trackQuantity: boolean
  quantity?: number
  weight?: number
  length?: number
  width?: number
  height?: number
  categoryId: string
  tags: string[]
  seoTitle?: string
  seoDescription?: string
  status: string
  featured: boolean
  createdAt: string
  updatedAt: string
  category: {
    id: string
    name: string
    slug: string
  }
  images: Array<{
    id: string
    url: string
    alt: string
    position: number
  }>
  variants: Array<{
    id: string
    name: string
    price: number
    comparePrice?: number
    sku: string
    quantity?: number
    image?: string
    options: any
  }>
  averageRating?: number
  reviewCount?: number
}

export interface ProductsResponse {
  products: Product[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface ProductsParams {
  page?: number
  limit?: number
  category?: string
  search?: string
  featured?: boolean
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  minPrice?: number
  maxPrice?: number
}

export function useProducts(params: ProductsParams = {}) {
  const queryParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, value.toString())
    }
  })

  return useQuery<ProductsResponse>({
    queryKey: ['products', params],
    queryFn: async () => {
      const response = await fetch(`/api/products?${queryParams.toString()}`)
      if (!response.ok) {
        throw new Error('Failed to fetch products')
      }
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useProduct(slug: string) {
  return useQuery<Product>({
    queryKey: ['product', slug],
    queryFn: async () => {
      const response = await fetch(`/api/products/${slug}`)
      if (!response.ok) {
        throw new Error('Failed to fetch product')
      }
      return response.json()
    },
    enabled: !!slug,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useFeaturedProducts(limit: number = 8) {
  return useProducts({
    featured: true,
    limit,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })
}
