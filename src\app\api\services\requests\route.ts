import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { emailService } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      serviceType,
      title,
      description,
      priority = 'MEDIUM',
      customerName,
      customerEmail,
      customerPhone,
      customerCompany,
      urgentDeadline,
      budget,
      additionalNotes,
    } = body

    // Validate required fields
    if (!serviceType || !title || !description) {
      return NextResponse.json(
        { error: 'Missing required fields: serviceType, title, description' },
        { status: 400 }
      )
    }

    // Generate unique request number
    const requestNumber = `SR-${Date.now()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`

    // Create service-specific details based on type
    let serviceDetails = {}
    
    switch (serviceType) {
      case 'INSPECTION':
        serviceDetails = {
          inspectionType: 'quality_control',
          products: [],
          specifications: description,
          timeline: urgentDeadline || null,
        }
        break
      case 'STORAGE':
        serviceDetails = {
          storageType: 'warehouse',
          duration: 'monthly',
          specialRequirements: additionalNotes || '',
        }
        break
      case 'SHIPPING':
        serviceDetails = {
          shippingMethod: 'sea_freight',
          destination: 'middle_east',
          weight: null,
          dimensions: null,
        }
        break
      case 'CERTIFICATION':
        serviceDetails = {
          certificationType: 'quality_certificate',
          standards: [],
          urgency: priority,
        }
        break
      case 'CONSULTING':
        serviceDetails = {
          consultingArea: 'sourcing',
          sessionType: 'remote',
          duration: '1_hour',
        }
        break
      case 'QUALITY_CONTROL':
        serviceDetails = {
          testingType: 'comprehensive',
          standards: ['ISO', 'CE'],
          sampleRequired: true,
        }
        break
      default:
        serviceDetails = {
          customRequirements: description,
        }
    }

    // Create the service request
    const serviceRequest = await prisma.serviceRequest.create({
      data: {
        requestNumber,
        customerId: session.user.id,
        customerName: customerName || session.user.name || 'Unknown',
        customerEmail: customerEmail || session.user.email || '',
        customerPhone: customerPhone || '',
        customerCompany: customerCompany || '',
        serviceType: serviceType as any,
        title,
        description,
        priority: priority as any,
        status: 'PENDING',
        
        // Service-specific details
        inspectionDetails: serviceType === 'INSPECTION' ? serviceDetails : null,
        storageDetails: serviceType === 'STORAGE' ? serviceDetails : null,
        shippingDetails: serviceType === 'SHIPPING' ? serviceDetails : null,
        certificationDetails: serviceType === 'CERTIFICATION' ? serviceDetails : null,
        consultingDetails: serviceType === 'CONSULTING' ? serviceDetails : null,
        
        // Additional information
        internalNotes: `Budget: ${budget || 'Not specified'}\nDeadline: ${urgentDeadline || 'Flexible'}\nAdditional Notes: ${additionalNotes || 'None'}`,
        customerNotes: additionalNotes || '',
      },
    })

    // Send confirmation email to customer
    try {
      await emailService.sendServiceRequestConfirmation({
        customerName: customerName || session.user.name || 'Customer',
        customerEmail: customerEmail || session.user.email || '',
        requestNumber,
        serviceType,
        title,
        description,
        priority,
      })
    } catch (emailError) {
      console.error('Failed to send confirmation email:', emailError)
      // Don't fail the request if email fails
    }

    // Send notification to admin/team
    try {
      await emailService.sendServiceRequestNotification({
        requestNumber,
        customerName: customerName || session.user.name || 'Customer',
        customerEmail: customerEmail || session.user.email || '',
        serviceType,
        title,
        description,
        priority,
      })
    } catch (emailError) {
      console.error('Failed to send admin notification:', emailError)
    }

    return NextResponse.json({
      success: true,
      requestNumber,
      serviceRequest: {
        id: serviceRequest.id,
        requestNumber: serviceRequest.requestNumber,
        status: serviceRequest.status,
        createdAt: serviceRequest.createdAt,
      },
    })

  } catch (error) {
    console.error('Service request creation error:', error)
    return NextResponse.json(
      { error: 'Failed to create service request' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status')
    const serviceType = searchParams.get('serviceType')
    
    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      customerId: session.user.id,
    }

    if (status) {
      where.status = status
    }

    if (serviceType) {
      where.serviceType = serviceType
    }

    // Get service requests
    const [serviceRequests, total] = await Promise.all([
      prisma.serviceRequest.findMany({
        where,
        include: {
          supplier: {
            select: {
              id: true,
              name: true,
              contactPerson: true,
              email: true,
              phone: true,
            },
          },
          updates: {
            orderBy: {
              createdAt: 'desc',
            },
            take: 3, // Latest 3 updates
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.serviceRequest.count({ where }),
    ])

    return NextResponse.json({
      serviceRequests,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })

  } catch (error) {
    console.error('Service requests fetch error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch service requests' },
      { status: 500 }
    )
  }
}
