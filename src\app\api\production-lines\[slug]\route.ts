import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params

    const productionLine = await prisma.productionLine.findUnique({
      where: { slug },
      include: {
        category: true
      }
    })

    if (!productionLine) {
      return NextResponse.json(
        { error: 'Production line not found' },
        { status: 404 }
      )
    }

    // Get related production lines from the same category
    const relatedLines = await prisma.productionLine.findMany({
      where: {
        categoryId: productionLine.categoryId,
        id: { not: productionLine.id },
        status: 'ACTIVE'
      },
      include: {
        category: true
      },
      take: 4
    })

    const lineWithRelated = {
      ...productionLine,
      relatedLines
    }

    return NextResponse.json(lineWithRelated)
  } catch (error) {
    console.error('Error fetching production line:', error)
    return NextResponse.json(
      { error: 'Failed to fetch production line' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params
    const body = await request.json()

    const existingLine = await prisma.productionLine.findUnique({
      where: { slug }
    })

    if (!existingLine) {
      return NextResponse.json(
        { error: 'Production line not found' },
        { status: 404 }
      )
    }

    const {
      name,
      description,
      shortDescription,
      specifications,
      images,
      videos,
      brochure,
      categoryId,
      status,
      featured,
      seoTitle,
      seoDescription
    } = body

    const updatedLine = await prisma.productionLine.update({
      where: { slug },
      data: {
        name,
        description,
        shortDescription,
        specifications,
        images,
        videos,
        brochure,
        categoryId,
        status,
        featured,
        seoTitle,
        seoDescription
      },
      include: {
        category: true
      }
    })

    return NextResponse.json(updatedLine)
  } catch (error) {
    console.error('Error updating production line:', error)
    return NextResponse.json(
      { error: 'Failed to update production line' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params

    const existingLine = await prisma.productionLine.findUnique({
      where: { slug }
    })

    if (!existingLine) {
      return NextResponse.json(
        { error: 'Production line not found' },
        { status: 404 }
      )
    }

    await prisma.productionLine.delete({
      where: { slug }
    })

    return NextResponse.json({ message: 'Production line deleted successfully' })
  } catch (error) {
    console.error('Error deleting production line:', error)
    return NextResponse.json(
      { error: 'Failed to delete production line' },
      { status: 500 }
    )
  }
}
