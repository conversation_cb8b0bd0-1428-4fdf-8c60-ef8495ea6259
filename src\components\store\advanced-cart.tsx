"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { useCart } from "@/components/providers/cart-provider"
import { useAuth } from "@/components/providers/auth-provider"
import { useToast } from "@/hooks/use-toast"
import { formatPrice } from "@/lib/utils"
import {
  Plus,
  Minus,
  Trash2,
  ShoppingBag,
  ArrowLeft,
  ArrowRight,
  Heart,
  RefreshCw,
  Truck,
  Shield,
  Tag,
  Clock,
  CheckCircle,
  AlertCircle,
  Gift,
  Percent,
  Save,
  Zap,
} from "lucide-react"

interface CartItem {
  id: string
  productId: string
  name: string
  slug: string
  price: number
  originalPrice?: number
  quantity: number
  maxQuantity: number
  image: string
  variant?: {
    id: string
    name: string
    price: number
  }
  inStock: boolean
  estimatedDelivery?: string
  warranty?: string
}

interface CartSummary {
  subtotal: number
  tax: number
  shipping: number
  discount: number
  total: number
  savings: number
}

interface AdvancedCartProps {
  className?: string
  showRecommendations?: boolean
  autoSave?: boolean
}

export function AdvancedCart({ 
  className = "",
  showRecommendations = true,
  autoSave = true
}: AdvancedCartProps) {
  const [couponCode, setCouponCode] = useState("")
  const [appliedCoupon, setAppliedCoupon] = useState<string | null>(null)
  const [isApplyingCoupon, setIsApplyingCoupon] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [savedForLater, setSavedForLater] = useState<CartItem[]>([])

  const { user } = useAuth()
  const { toast } = useToast()

  // Mock cart data - in real app, this would come from cart context
  const [items, setItems] = useState<CartItem[]>([
    {
      id: "1",
      productId: "1",
      name: "Premium Wireless Headphones",
      slug: "premium-wireless-headphones",
      price: 199.99,
      originalPrice: 249.99,
      quantity: 1,
      maxQuantity: 5,
      image: "/product-1.jpg",
      variant: { id: "1", name: "Black", price: 199.99 },
      inStock: true,
      estimatedDelivery: "2-3 business days",
      warranty: "2 years"
    },
    {
      id: "2",
      productId: "2",
      name: "Smart Fitness Watch",
      slug: "smart-fitness-watch",
      price: 299.99,
      quantity: 2,
      maxQuantity: 3,
      image: "/product-2.jpg",
      variant: { id: "2", name: "Silver", price: 299.99 },
      inStock: true,
      estimatedDelivery: "1-2 business days"
    }
  ])

  const [summary, setSummary] = useState<CartSummary>({
    subtotal: 799.97,
    tax: 64.00,
    shipping: 0,
    discount: 50.00,
    total: 813.97,
    savings: 99.98
  })

  // Auto-save cart changes
  useEffect(() => {
    if (autoSave && user) {
      const saveCart = async () => {
        setIsSaving(true)
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500))
        setLastSaved(new Date())
        setIsSaving(false)
      }

      const timeoutId = setTimeout(saveCart, 1000)
      return () => clearTimeout(timeoutId)
    }
  }, [items, autoSave, user])

  const updateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) return

    setItems(prev => prev.map(item => {
      if (item.id === itemId) {
        const quantity = Math.min(newQuantity, item.maxQuantity)
        return { ...item, quantity }
      }
      return item
    }))

    toast({
      title: "Cart Updated",
      description: "Quantity has been updated",
    })
  }

  const removeItem = (itemId: string) => {
    const item = items.find(i => i.id === itemId)
    setItems(prev => prev.filter(item => item.id !== itemId))

    toast({
      title: "Item Removed",
      description: `${item?.name} has been removed from your cart`,
      action: (
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            if (item) {
              setItems(prev => [...prev, item])
              toast({ title: "Item Restored", description: "Item has been added back to your cart" })
            }
          }}
        >
          Undo
        </Button>
      )
    })
  }

  const saveForLater = (itemId: string) => {
    const item = items.find(i => i.id === itemId)
    if (item) {
      setSavedForLater(prev => [...prev, item])
      setItems(prev => prev.filter(i => i.id !== itemId))
      
      toast({
        title: "Saved for Later",
        description: `${item.name} has been saved for later`
      })
    }
  }

  const moveToCart = (itemId: string) => {
    const item = savedForLater.find(i => i.id === itemId)
    if (item) {
      setItems(prev => [...prev, item])
      setSavedForLater(prev => prev.filter(i => i.id !== itemId))
      
      toast({
        title: "Moved to Cart",
        description: `${item.name} has been moved back to your cart`
      })
    }
  }

  const applyCoupon = async () => {
    if (!couponCode.trim()) return

    setIsApplyingCoupon(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Mock coupon validation
    const validCoupons = ['SAVE10', 'WELCOME20', 'STUDENT15']
    
    if (validCoupons.includes(couponCode.toUpperCase())) {
      setAppliedCoupon(couponCode.toUpperCase())
      setCouponCode("")
      
      toast({
        title: "Coupon Applied!",
        description: `${couponCode.toUpperCase()} has been applied to your order`
      })
    } else {
      toast({
        title: "Invalid Coupon",
        description: "The coupon code you entered is not valid",
        variant: "destructive"
      })
    }
    
    setIsApplyingCoupon(false)
  }

  const removeCoupon = () => {
    setAppliedCoupon(null)
    toast({
      title: "Coupon Removed",
      description: "The coupon has been removed from your order"
    })
  }

  const clearCart = () => {
    setItems([])
    toast({
      title: "Cart Cleared",
      description: "All items have been removed from your cart"
    })
  }

  if (items.length === 0 && savedForLater.length === 0) {
    return (
      <div className={`${className}`}>
        <Card>
          <CardContent className="p-12 text-center">
            <ShoppingBag className="h-16 w-16 text-gray-400 mx-auto mb-6" />
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
              Your cart is empty
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-8">
              Looks like you haven't added any items to your cart yet. Start shopping to fill it up!
            </p>
            <div className="space-y-4">
              <Link href="/shop">
                <Button size="lg" className="w-full sm:w-auto">
                  <ShoppingBag className="h-5 w-5 mr-2" />
                  Start Shopping
                </Button>
              </Link>
              <div className="flex items-center justify-center space-x-4 text-sm text-gray-500">
                <div className="flex items-center space-x-1">
                  <Truck className="h-4 w-4" />
                  <span>Free Shipping</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Shield className="h-4 w-4" />
                  <span>Secure Checkout</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Shopping Cart
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {items.length} item{items.length !== 1 ? 's' : ''} in your cart
          </p>
        </div>
        
        {/* Auto-save indicator */}
        {autoSave && user && (
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            {isSaving ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin" />
                <span>Saving...</span>
              </>
            ) : lastSaved ? (
              <>
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Saved {lastSaved.toLocaleTimeString()}</span>
              </>
            ) : null}
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Cart Items */}
        <div className="lg:col-span-2 space-y-4">
          {/* Cart Items List */}
          {items.map((item) => (
            <Card key={item.id} className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  {/* Product Image */}
                  <div className="w-20 h-20 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center flex-shrink-0">
                    <div className="text-2xl">📱</div>
                  </div>

                  {/* Product Details */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          <Link 
                            href={`/product/${item.slug}`}
                            className="hover:text-primary-600 transition-colors"
                          >
                            {item.name}
                          </Link>
                        </h3>
                        {item.variant && (
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Color: {item.variant.name}
                          </p>
                        )}
                        
                        {/* Stock Status */}
                        <div className="flex items-center space-x-4 mt-2">
                          {item.inStock ? (
                            <div className="flex items-center space-x-1 text-green-600">
                              <CheckCircle className="h-3 w-3" />
                              <span className="text-xs">In Stock</span>
                            </div>
                          ) : (
                            <div className="flex items-center space-x-1 text-red-600">
                              <AlertCircle className="h-3 w-3" />
                              <span className="text-xs">Out of Stock</span>
                            </div>
                          )}
                          
                          {item.estimatedDelivery && (
                            <div className="flex items-center space-x-1 text-gray-600 dark:text-gray-400">
                              <Truck className="h-3 w-3" />
                              <span className="text-xs">{item.estimatedDelivery}</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Price */}
                      <div className="text-right">
                        <div className="flex items-center space-x-2">
                          <span className="font-semibold text-gray-900 dark:text-white">
                            {formatPrice(item.price)}
                          </span>
                          {item.originalPrice && (
                            <span className="text-sm text-gray-500 line-through">
                              {formatPrice(item.originalPrice)}
                            </span>
                          )}
                        </div>
                        {item.originalPrice && (
                          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 text-xs mt-1">
                            Save {formatPrice(item.originalPrice - item.price)}
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Quantity and Actions */}
                    <div className="flex items-center justify-between mt-4">
                      {/* Quantity Controls */}
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center border rounded-md">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                            disabled={item.quantity <= 1}
                            className="h-8 w-8 p-0"
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          <Input
                            type="number"
                            min="1"
                            max={item.maxQuantity}
                            value={item.quantity}
                            onChange={(e) => updateQuantity(item.id, parseInt(e.target.value) || 1)}
                            className="h-8 w-16 text-center border-0 focus:ring-0"
                          />
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                            disabled={item.quantity >= item.maxQuantity}
                            className="h-8 w-8 p-0"
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                        
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Max: {item.maxQuantity}
                        </span>
                      </div>

                      {/* Item Actions */}
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => saveForLater(item.id)}
                        >
                          <Heart className="h-4 w-4 mr-1" />
                          Save for Later
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeItem(item.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Remove
                        </Button>
                      </div>
                    </div>

                    {/* Item Total */}
                    <div className="flex justify-between items-center mt-3 pt-3 border-t">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Item total:
                      </span>
                      <span className="font-semibold text-gray-900 dark:text-white">
                        {formatPrice(item.price * item.quantity)}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {/* Saved for Later */}
          {savedForLater.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Saved for Later ({savedForLater.length})</CardTitle>
                <CardDescription>
                  Items you've saved to purchase later
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {savedForLater.map((item) => (
                    <div key={item.id} className="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                        <div className="text-lg">📱</div>
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 dark:text-white">{item.name}</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {formatPrice(item.price)}
                        </p>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          onClick={() => moveToCart(item.id)}
                        >
                          Move to Cart
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setSavedForLater(prev => prev.filter(i => i.id !== item.id))}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Cart Actions */}
          <div className="flex items-center justify-between">
            <Link href="/shop">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Continue Shopping
              </Button>
            </Link>
            <Button variant="ghost" onClick={clearCart} className="text-red-600">
              Clear Cart
            </Button>
          </div>
        </div>

        {/* Order Summary */}
        <div className="space-y-6">
          {/* Coupon Code */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Tag className="h-5 w-5 mr-2" />
                Promo Code
              </CardTitle>
            </CardHeader>
            <CardContent>
              {appliedCoupon ? (
                <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium text-green-800 dark:text-green-200">
                      {appliedCoupon} Applied
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={removeCoupon}
                    className="text-green-600 hover:text-green-700"
                  >
                    Remove
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="flex space-x-2">
                    <Input
                      placeholder="Enter promo code"
                      value={couponCode}
                      onChange={(e) => setCouponCode(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && applyCoupon()}
                    />
                    <Button
                      onClick={applyCoupon}
                      disabled={!couponCode.trim() || isApplyingCoupon}
                    >
                      {isApplyingCoupon ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        'Apply'
                      )}
                    </Button>
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    Try: SAVE10, WELCOME20, STUDENT15
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Order Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span>Subtotal</span>
                <span>{formatPrice(summary.subtotal)}</span>
              </div>
              
              {summary.discount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Discount</span>
                  <span>-{formatPrice(summary.discount)}</span>
                </div>
              )}
              
              <div className="flex justify-between">
                <span>Shipping</span>
                <span className="text-green-600">
                  {summary.shipping === 0 ? 'FREE' : formatPrice(summary.shipping)}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span>Tax</span>
                <span>{formatPrice(summary.tax)}</span>
              </div>
              
              <div className="border-t pt-4">
                <div className="flex justify-between text-lg font-semibold">
                  <span>Total</span>
                  <span>{formatPrice(summary.total)}</span>
                </div>
                {summary.savings > 0 && (
                  <p className="text-sm text-green-600 mt-1">
                    You save {formatPrice(summary.savings)}!
                  </p>
                )}
              </div>

              {/* Checkout Button */}
              <Link href="/checkout" className="block">
                <Button size="lg" className="w-full">
                  <ArrowRight className="h-5 w-5 mr-2" />
                  Proceed to Checkout
                </Button>
              </Link>

              {/* Security Features */}
              <div className="space-y-2 pt-4 border-t">
                <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                  <Shield className="h-4 w-4 text-green-600" />
                  <span>Secure 256-bit SSL encryption</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                  <Truck className="h-4 w-4 text-blue-600" />
                  <span>Free shipping on orders over $75</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                  <RefreshCw className="h-4 w-4 text-purple-600" />
                  <span>30-day return policy</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardContent className="p-4">
              <div className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <Gift className="h-4 w-4 mr-2" />
                  Add Gift Message
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Percent className="h-4 w-4 mr-2" />
                  Student Discount
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Save className="h-4 w-4 mr-2" />
                  Save Cart for Later
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
