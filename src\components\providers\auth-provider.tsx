"use client"

import React from "react"
import { SessionProvider } from "next-auth/react"

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  // Mock users for demo purposes
  const mockUsers = [
    {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      password: "password123",
      avatar: "/avatar-1.jpg",
      role: "user" as const,
      preferences: {
        language: "en",
        currency: "USD",
        notifications: true
      }
    },
    {
      id: "2", 
      name: "<PERSON>",
      email: "<EMAIL>",
      password: "password123",
      avatar: "/avatar-2.jpg",
      role: "admin" as const,
      preferences: {
        language: "en",
        currency: "USD",
        notifications: true
      }
    }
  ]

  // Check for existing session on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const savedUser = localStorage.getItem("aidevcommerce_user")
        if (savedUser) {
          const userData = JSON.parse(savedUser)
          setUser(userData)
        }
      } catch (error) {
        console.error("Error checking auth:", error)
        localStorage.removeItem("aidevcommerce_user")
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [])

  const login = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true)
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Find user in mock data
      const foundUser = mockUsers.find(u => u.email === email && u.password === password)
      
      if (foundUser) {
        const { password: _, ...userWithoutPassword } = foundUser
        setUser(userWithoutPassword)
        localStorage.setItem("aidevcommerce_user", JSON.stringify(userWithoutPassword))
        
        toast({
          title: "Welcome back!",
          description: `Successfully logged in as ${foundUser.name}`
        })
        
        return true
      } else {
        toast({
          title: "Login Failed",
          description: "Invalid email or password. Try <EMAIL> / password123",
          variant: "destructive"
        })
        return false
      }
    } catch (error) {
      toast({
        title: "Login Error",
        description: "An error occurred during login. Please try again.",
        variant: "destructive"
      })
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (name: string, email: string, password: string): Promise<boolean> => {
    setIsLoading(true)
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Check if user already exists
      const existingUser = mockUsers.find(u => u.email === email)
      if (existingUser) {
        toast({
          title: "Registration Failed",
          description: "An account with this email already exists.",
          variant: "destructive"
        })
        return false
      }

      // Create new user
      const newUser: User = {
        id: Date.now().toString(),
        name,
        email,
        role: "user",
        preferences: {
          language: "en",
          currency: "USD",
          notifications: true
        }
      }

      setUser(newUser)
      localStorage.setItem("aidevcommerce_user", JSON.stringify(newUser))
      
      toast({
        title: "Welcome to AIDEVCOMMERCE!",
        description: `Account created successfully for ${name}`
      })
      
      return true
    } catch (error) {
      toast({
        title: "Registration Error",
        description: "An error occurred during registration. Please try again.",
        variant: "destructive"
      })
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    setUser(null)
    localStorage.removeItem("aidevcommerce_user")
    localStorage.removeItem("aidevcommerce_cart")
    localStorage.removeItem("aidevcommerce_wishlist")
    
    toast({
      title: "Logged out",
      description: "You have been successfully logged out."
    })
  }

  const updateProfile = async (updates: Partial<User>): Promise<boolean> => {
    if (!user) return false

    setIsLoading(true)
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      const updatedUser = { ...user, ...updates }
      setUser(updatedUser)
      localStorage.setItem("aidevcommerce_user", JSON.stringify(updatedUser))
      
      toast({
        title: "Profile Updated",
        description: "Your profile has been successfully updated."
      })
      
      return true
    } catch (error) {
      toast({
        title: "Update Error",
        description: "Failed to update profile. Please try again.",
        variant: "destructive"
      })
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    updateProfile
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
