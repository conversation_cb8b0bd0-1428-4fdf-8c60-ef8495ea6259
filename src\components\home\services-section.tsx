"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { useFeaturedServices } from "@/hooks/use-services"
import {
  Wrench,
  Code,
  Palette,
  BarChart3,
  Shield,
  Headphones,
  ArrowRight,
  CheckCircle,
  Star,
  Users,
  Clock,
  Award,
  Loader2,
} from "lucide-react"




export function ServicesSection() {
  const [hoveredService, setHoveredService] = useState<string | null>(null)
  const { t } = useLanguage()

  // Fetch featured services from API
  const { data: servicesData, isLoading, error } = useFeaturedServices(6)
  const services = servicesData?.services || []

  // Loading state
  if (isLoading) {
    return (
      <section className="py-20 bg-gradient-to-br from-gray-50 via-white to-gray-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Badge variant="secondary" className="mb-4">
              <Wrench className="h-4 w-4 mr-2" />
              Our Services
            </Badge>
            <h2 className="text-4xl md:text-6xl font-bold mb-6">
              <span className="bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                Professional Services
              </span>
            </h2>
          </div>
          <div className="flex justify-center items-center py-20">
            <Loader2 className="h-8 w-8 animate-spin text-primary-600" />
            <span className="ml-2 text-lg">Loading services...</span>
          </div>
        </div>
      </section>
    )
  }

  // Error state
  if (error) {
    return (
      <section className="py-20 bg-gradient-to-br from-gray-50 via-white to-gray-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Badge variant="secondary" className="mb-4">
              <Wrench className="h-4 w-4 mr-2" />
              Our Services
            </Badge>
            <h2 className="text-4xl md:text-6xl font-bold mb-6">
              <span className="bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                Professional Services
              </span>
            </h2>
          </div>
          <div className="text-center py-20">
            <p className="text-red-600 dark:text-red-400 text-lg">
              Failed to load services. Please try again later.
            </p>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 via-white to-gray-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge className="mb-4 px-4 py-2">
            <Wrench className="h-4 w-4 mr-2" />
            Professional Services
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
              Expert Services
            </span>
            <br />
            <span className="text-gray-900 dark:text-white">Tailored for Success</span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed">
            From concept to completion, our expert team delivers comprehensive solutions 
            that drive your business forward with cutting-edge technology and proven methodologies.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {services.map((service) => {
            // Map service category to icon
            const getServiceIcon = (categoryName: string) => {
              switch (categoryName?.toLowerCase()) {
                case 'web development': return Code
                case 'digital marketing': return BarChart3
                case 'consulting': return Users
                default: return Wrench
              }
            }

            const Icon = getServiceIcon(service.category?.name || '')
            const isHovered = hoveredService === service.id
            
            return (
              <Card 
                key={service.id}
                className={`relative overflow-hidden transition-all duration-500 hover:shadow-2xl hover:-translate-y-2 group cursor-pointer ${
                  isHovered ? 'ring-2 ring-primary-500' : ''
                }`}
                onMouseEnter={() => setHoveredService(service.id)}
                onMouseLeave={() => setHoveredService(null)}
              >
                {/* Featured Badge */}
                {service.featured && (
                  <div className="absolute top-4 right-4 z-10">
                    <Badge className="bg-gradient-to-r from-orange-500 to-red-500 text-white">
                      <Star className="h-3 w-3 mr-1 fill-current" />
                      Featured
                    </Badge>
                  </div>
                )}

                {/* Animated Background */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-cyan-500 opacity-0 group-hover:opacity-10 transition-opacity duration-500" />

                <CardHeader className="relative z-10">
                  {/* Animated Icon */}
                  <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center mb-4 transform transition-all duration-500 group-hover:scale-110 group-hover:rotate-6">
                    <Icon className="h-8 w-8 text-white" />
                  </div>

                  <CardTitle className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                    {service.name}
                  </CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400 leading-relaxed">
                    {service.shortDescription || service.description}
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-6">
                  {/* Service Stats */}
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-1">
                      {service.price && (
                        <span className="font-bold text-primary-600">
                          ${service.price}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center space-x-1 text-gray-500">
                      <Clock className="h-4 w-4" />
                      <span>{service.duration ? `${Math.ceil(service.duration / 60)} hours` : 'Custom'}</span>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="space-y-2">
                    {service.features && Array.isArray(service.features) && service.features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                        <span className="text-gray-600 dark:text-gray-400">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* Pricing */}
                  <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-between">
                      <div>
                        {service.price && (
                          <p className="text-2xl font-bold text-gray-900 dark:text-white">
                            ${service.price}
                          </p>
                        )}
                      </div>
                      <Link href={`/services/${service.slug}`}>
                        <Button
                          className="group/btn bg-gradient-to-r from-blue-500 to-cyan-500 hover:shadow-lg transform transition-all duration-300 hover:scale-105"
                        >
                          Learn More
                          <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>

                {/* Hover Effect Overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-cyan-500 opacity-0 group-hover:opacity-5 transition-opacity duration-500 pointer-events-none" />
              </Card>
            )
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-900/20 dark:to-secondary-900/20 rounded-2xl p-8 mb-8">
            <div className="flex items-center justify-center space-x-4 mb-6">
              <div className="flex items-center space-x-2">
                <Award className="h-6 w-6 text-primary-600" />
                <span className="font-semibold text-primary-600">Certified Experts</span>
              </div>
              <div className="w-px h-6 bg-gray-300 dark:bg-gray-600" />
              <div className="flex items-center space-x-2">
                <Users className="h-6 w-6 text-secondary-600" />
                <span className="font-semibold text-secondary-600">500+ Projects</span>
              </div>
              <div className="w-px h-6 bg-gray-300 dark:bg-gray-600" />
              <div className="flex items-center space-x-2">
                <Star className="h-6 w-6 text-yellow-500 fill-current" />
                <span className="font-semibold text-yellow-600">4.9 Rating</span>
              </div>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Ready to Transform Your Business?
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-2xl mx-auto">
              Let's discuss your project and create a custom solution that drives results. 
              Get a free consultation with our experts today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact">
                <Button size="lg" className="px-8 py-4 text-lg font-semibold group">
                  Get Free Consultation
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Button>
              </Link>
              <Link href="/services">
                <Button variant="outline" size="lg" className="px-8 py-4 text-lg font-semibold">
                  View All Services
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
