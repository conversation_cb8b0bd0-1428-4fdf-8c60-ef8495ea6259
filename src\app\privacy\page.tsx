"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import {
  Shield,
  Eye,
  Lock,
  Users,
  Globe,
  Mail,
  Phone,
  FileText,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Settings,
} from "lucide-react"

export default function PrivacyPage() {
  const { t } = useLanguage()

  const lastUpdated = "January 15, 2024"

  const sections = [
    {
      id: "information-collection",
      title: "Information We Collect",
      icon: Eye,
      content: [
        {
          subtitle: "Personal Information",
          text: "We collect information you provide directly to us, such as when you create an account, make a purchase, subscribe to our newsletter, or contact us. This may include your name, email address, phone number, shipping and billing addresses, payment information, and communication preferences."
        },
        {
          subtitle: "Automatically Collected Information",
          text: "We automatically collect certain information about your device and usage of our services, including IP address, browser type, operating system, referring URLs, pages viewed, time spent on pages, and other usage statistics through cookies and similar technologies."
        },
        {
          subtitle: "AI and Machine Learning Data",
          text: "To provide personalized recommendations and improve our AI services, we collect and analyze your browsing patterns, purchase history, product interactions, search queries, and preferences. This data is processed to enhance your shopping experience."
        }
      ]
    },
    {
      id: "information-use",
      title: "How We Use Your Information",
      icon: Settings,
      content: [
        {
          subtitle: "Service Provision",
          text: "We use your information to provide, maintain, and improve our services, process transactions, fulfill orders, provide customer support, and communicate with you about your account and orders."
        },
        {
          subtitle: "Personalization",
          text: "Our AI systems use your data to personalize your shopping experience, provide product recommendations, customize content, and improve our algorithms to better serve your needs and preferences."
        },
        {
          subtitle: "Marketing and Communications",
          text: "With your consent, we may use your information to send you promotional emails, newsletters, and other marketing communications. You can opt out of these communications at any time."
        }
      ]
    },
    {
      id: "information-sharing",
      title: "Information Sharing and Disclosure",
      icon: Users,
      content: [
        {
          subtitle: "Service Providers",
          text: "We may share your information with third-party service providers who perform services on our behalf, such as payment processing, shipping, email delivery, customer service, and data analysis. These providers are contractually obligated to protect your information."
        },
        {
          subtitle: "Business Transfers",
          text: "In the event of a merger, acquisition, or sale of assets, your information may be transferred as part of the transaction. We will notify you of any such change in ownership or control of your personal information."
        },
        {
          subtitle: "Legal Requirements",
          text: "We may disclose your information if required by law, regulation, legal process, or governmental request, or to protect our rights, property, or safety, or that of our users or the public."
        }
      ]
    },
    {
      id: "data-security",
      title: "Data Security",
      icon: Lock,
      content: [
        {
          subtitle: "Security Measures",
          text: "We implement appropriate technical and organizational security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. This includes encryption, secure servers, and regular security assessments."
        },
        {
          subtitle: "Payment Security",
          text: "All payment information is encrypted using industry-standard SSL technology. We do not store complete credit card information on our servers and work with PCI-compliant payment processors to ensure your financial data is secure."
        },
        {
          subtitle: "Data Breach Response",
          text: "In the unlikely event of a data breach that affects your personal information, we will notify you and relevant authorities as required by law, and take immediate steps to secure our systems and protect your data."
        }
      ]
    },
    {
      id: "cookies-tracking",
      title: "Cookies and Tracking Technologies",
      icon: Globe,
      content: [
        {
          subtitle: "Types of Cookies",
          text: "We use essential cookies for site functionality, performance cookies to analyze usage, and targeting cookies for personalized advertising. You can control cookie preferences through your browser settings or our cookie preference center."
        },
        {
          subtitle: "Third-Party Tracking",
          text: "We may use third-party analytics and advertising services that collect information about your online activities across different websites and services. These services may use cookies, web beacons, and other tracking technologies."
        },
        {
          subtitle: "Do Not Track",
          text: "Our website does not currently respond to Do Not Track signals. However, you can control tracking through your browser settings and by opting out of targeted advertising through industry opt-out tools."
        }
      ]
    },
    {
      id: "your-rights",
      title: "Your Privacy Rights",
      icon: Shield,
      content: [
        {
          subtitle: "Access and Portability",
          text: "You have the right to access, update, or delete your personal information. You can also request a copy of your data in a portable format. Most of this can be done through your account settings."
        },
        {
          subtitle: "Marketing Preferences",
          text: "You can opt out of marketing communications at any time by clicking the unsubscribe link in emails, adjusting your account preferences, or contacting us directly. This will not affect transactional communications about your orders."
        },
        {
          subtitle: "Regional Rights",
          text: "Depending on your location, you may have additional rights under laws such as GDPR, CCPA, or other privacy regulations. These may include rights to restrict processing, object to processing, or lodge complaints with supervisory authorities."
        }
      ]
    },
    {
      id: "international-transfers",
      title: "International Data Transfers",
      icon: Globe,
      content: [
        {
          subtitle: "Global Operations",
          text: "As a global company, we may transfer your personal information to countries other than your own. We ensure appropriate safeguards are in place for such transfers, including adequacy decisions, standard contractual clauses, or other approved mechanisms."
        },
        {
          subtitle: "Data Protection Standards",
          text: "Regardless of where your data is processed, we maintain the same high standards of data protection and security. Our international partners and service providers are contractually required to provide adequate protection for your personal information."
        }
      ]
    },
    {
      id: "children-privacy",
      title: "Children's Privacy",
      icon: Users,
      content: [
        {
          subtitle: "Age Restrictions",
          text: "Our services are not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13. If we become aware that we have collected such information, we will take steps to delete it promptly."
        },
        {
          subtitle: "Parental Rights",
          text: "If you are a parent or guardian and believe your child has provided us with personal information, please contact us. We will work with you to address any concerns and take appropriate action to protect your child's privacy."
        }
      ]
    }
  ]

  const contactInfo = [
    {
      method: "Email",
      value: "<EMAIL>",
      icon: Mail,
      description: "For privacy-related questions and requests"
    },
    {
      method: "Phone",
      value: "+****************",
      icon: Phone,
      description: "Mon-Fri, 9AM-6PM EST"
    },
    {
      method: "Mail",
      value: "123 Innovation Drive, Tech City, TC 12345",
      icon: FileText,
      description: "AIDEVCOMMERCE Privacy Office"
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-600 to-primary-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Privacy Policy
            </h1>
            <p className="text-xl text-primary-100 mb-8">
              Your privacy is important to us. This policy explains how we collect, use, and protect your personal information.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Badge className="bg-white/20 text-white px-4 py-2">
                <Shield className="h-4 w-4 mr-2" />
                GDPR Compliant
              </Badge>
              <Badge className="bg-white/20 text-white px-4 py-2">
                <Lock className="h-4 w-4 mr-2" />
                256-bit Encryption
              </Badge>
              <Badge className="bg-white/20 text-white px-4 py-2">
                <Calendar className="h-4 w-4 mr-2" />
                Updated {lastUpdated}
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Last Updated Notice */}
      <section className="py-8 bg-blue-50 dark:bg-blue-900/20 border-b border-blue-200 dark:border-blue-800">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center space-x-3">
              <Calendar className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              <div>
                <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  Last Updated: {lastUpdated}
                </p>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  We may update this privacy policy from time to time. We will notify you of any material changes.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Privacy Policy Content */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Introduction */}
            <Card className="mb-8">
              <CardContent className="p-8">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Introduction
                </h2>
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed mb-4">
                  AIDEVCOMMERCE ("we," "our," or "us") is committed to protecting your privacy and ensuring the security of your personal information. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website, use our services, or interact with our AI-powered e-commerce platform.
                </p>
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  By using our services, you agree to the collection and use of information in accordance with this policy. If you do not agree with our policies and practices, please do not use our services.
                </p>
              </CardContent>
            </Card>

            {/* Policy Sections */}
            <div className="space-y-8">
              {sections.map((section) => {
                const Icon = section.icon
                return (
                  <Card key={section.id}>
                    <CardHeader>
                      <CardTitle className="flex items-center text-xl">
                        <Icon className="h-6 w-6 mr-3 text-primary-600 dark:text-primary-400" />
                        {section.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {section.content.map((item, index) => (
                        <div key={index}>
                          <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                            {item.subtitle}
                          </h4>
                          <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                            {item.text}
                          </p>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            {/* Data Retention */}
            <Card className="mt-8">
              <CardHeader>
                <CardTitle className="flex items-center text-xl">
                  <FileText className="h-6 w-6 mr-3 text-primary-600 dark:text-primary-400" />
                  Data Retention
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed mb-4">
                  We retain your personal information only for as long as necessary to fulfill the purposes outlined in this privacy policy, unless a longer retention period is required or permitted by law. The retention period depends on the type of information and the purpose for which it was collected:
                </p>
                <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Account information: Retained while your account is active and for 3 years after closure</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Transaction records: Retained for 7 years for tax and legal compliance</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Marketing data: Retained until you opt out or for 2 years of inactivity</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Analytics data: Aggregated and anonymized data may be retained indefinitely</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <Card className="mt-8">
              <CardHeader>
                <CardTitle className="flex items-center text-xl">
                  <Mail className="h-6 w-6 mr-3 text-primary-600 dark:text-primary-400" />
                  Contact Us About Privacy
                </CardTitle>
                <CardDescription>
                  If you have questions about this privacy policy or our privacy practices, please contact us:
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {contactInfo.map((contact) => {
                    const Icon = contact.icon
                    return (
                      <div key={contact.method} className="flex items-start space-x-3">
                        <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center flex-shrink-0">
                          <Icon className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {contact.method}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                            {contact.value}
                          </p>
                          <p className="text-xs text-gray-500">
                            {contact.description}
                          </p>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Important Notice */}
            <Card className="mt-8 bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800">
              <CardContent className="p-6">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="h-6 w-6 text-amber-600 dark:text-amber-400 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="font-semibold text-amber-900 dark:text-amber-100 mb-2">
                      Important Notice
                    </h3>
                    <p className="text-amber-800 dark:text-amber-200 text-sm leading-relaxed">
                      This privacy policy may be updated from time to time to reflect changes in our practices or for other operational, legal, or regulatory reasons. We will notify you of any material changes by posting the new privacy policy on this page and updating the "Last Updated" date. We encourage you to review this privacy policy periodically to stay informed about how we are protecting your information.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  )
}
